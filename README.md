# Signpost

### Run the app and dev locally

Before running:

```
git pull
brew services start postgresql
rails db:migrate
bundle install
```

To run Signpost locally:

```
./bin/dev
```

Open a web browser:

```
open http://signposthd.localhost:3000/dashboard
```

Open the dev environment:

```
code .
```

To dev on centerforhumandesign.org:

```
open http://centerforhumandesign.localhost:3000
```

To dev on sfhdl.org:

```
open http://sfhdl.localhost:3000
```

To dev on highdeserthumandesign.com:

```
open http://highdeserthumandesign.localhost:3000
```

### Prerequisites

Make sure you are on Ruby 3.1.4 or higher:

```
ruby -v
```

e.g.:

```
➜  signposthd git:(main) ruby -v
ruby 3.1.4p223 (2023-03-30 revision 957bb7cb81) [x86_64-darwin22]
```

Install the Stripe CLI:

```
brew install stripe/stripe-cli/stripe
```

### Quickstart Setup from Scratch

After cloning, run the following to install and run `postgresql` and run database migrations:

```
brew install postgresql

brew services start postgresql

bundle install

rails db:create

rails db:migrate
```

To run Signpost locally:

```
./bin/dev
```

Open a web browser:

```
open http://signposthd.localhost:3000/dashboard
```

That's it! You should be up and running!

You can also click the following link to launch the dashboard:

##### Launch the Dashboard: _[http://signposthd.localhost:3000/dashboard](http://signposthd.localhost:3000/dashboard)_

### Google Cloud Storage

For our cloud storage provider we use GCS:

```
https://console.cloud.google.com/
```

**_Two helpful links:_**

[The `signposthd` bucket on GCS](https://console.cloud.google.com/storage/browser/signposthd;tab=objects?forceOnBucketsSortingFiltering=true&hl=en&project=signposthd&prefix=&forceOnObjectsSortingFiltering=false)

[List All Buckets on GCS](https://console.cloud.google.com/storage/browser?project=signposthd&prefix=&forceOnBucketsSortingFiltering=true)

### Notes on Timezones

For the Timezone gem to be able to lookup via latitude and longitude, we use the Google API. Make sure that your .env file has the proper GOOGLE_MAPS_API_KEY from the Google Cloud Console.

As a backup we have the ability to use the Geonames API. However, this API sometimes goes down, so we have it set to use Google by default. For Geonames, here are the credentials:

```
Geonames Credentials:
User: signpost
Email: <EMAIL>
Pass: Ra.....####
```

We use the `swe4r` gem for calculating the time when the Sun was at 88º retrograde. For more information, visit https://www.rubydoc.info/gems/swe4r/0.0.2.

### Using the EphemerisEntries (ephemeris_entry) Table

The timestamps are 6 hours less than UTC. So, you have to add 6 hours (e.g. `@date + 6.hours`) for them to be accurate. I've considered running a script to go through and update every timestamp by 6 hours since it is supposed to be in UTC but have not done this step yet.

### Working with the Database

#### Working with the Database - Local

Open pgAdmin 4. You should have Local PostgreSQL and Render PostgreSQL. If you do not, you can add them (see below for Render).

Go into Local PostgreSQL -> Databases (2) -> signposthd_development -> Schemas (1) -> public -> Tables.

From there you should see, for instance:

```
bodygraphs
calendar_events
ephemeris_entries
public_bodygraph_comments
public_bodygraphs
reminders
schema_migrations
streaming_videos
users
```

Simply Right-Click -> View/Edit Data -> First 100 Rows to see a sample of what is in that table.

#### Advanced Local Database Techniques

You can query the `ephemeris_entries` in intereting ways, for instance, using ChatGPT to come up with SQL queries via Right-Click -> PSQL Tool or Right-Click -> Query Tool. The queries could be based on any of the properties of `ephemeries_entries` such as every time Pluto goes to the next gate, or every time Mars and Venus are in the same gate and line.

#### Working with the Database - Render

Go to the dashboard here and add your IP address under Access Control:

https://dashboard.render.com/d/dpg-cfmgqsla499591eojvu0-a

After adding your IP address, copy the password. If it's your first time connecting to the database, add it first in pgAdmin 4. Otherwise, just go into pgAdmin 4 to the Render database and enter the password when prompted to connect.

### Troubleshooting

#### Wrong Architecture on Render

We deploy to Render and it requires building for x86_64-linux. Run the following to package for this platform:

```
npm run package
```

#### CSS Changes Not Working (e.g. Tailwind classes)

Tailwind classes are generated based on usage and have to be regenerated using `npm run build:css` which is automatically done every time you `./bin/dev` but you can always do it manually if necessary. You can also `npm run precompile` to precompile assets though this should not be necessary. You can also `npm run restart` to clear the cache and restart the server. This runs `rails tmp:cache:clear` which can help sometimes.

#### ConnectionNotEstablished Error

```
ActiveRecord::ConnectionNotEstablished
connection to server on socket "/tmp/.s.PGSQL.5432" failed: No such file or directory
Is the server running locally and accepting connections on that socket?
```

_Quickfix_: `rm /usr/local/var/postgresql@14/postmaster.pid && brew services restart postgresql`.

If that doesn't work, you can try starting:

`brew services start postgresql`

If necessary, remove the pid file first: `rm /usr/local/var/postgresql@14/postmaster.pid`

You may also have to run `brew tap homebrew/core` if it does not recognize postgresql.

You can troubleshoot with `brew services list` and checking error logs with the folowing command:

`cat /usr/local/var/log/<EMAIL>`

#### Postgres Errors

Try removing the postmaster.pid and restarting:

```
rm /opt/homebrew/var/postgresql@14/postmaster.pid
brew services start postgresql
```

Even if it says it fails, it will sometimes still work at this stage. You can try again and see if it tells you to restart. That means it's working.
