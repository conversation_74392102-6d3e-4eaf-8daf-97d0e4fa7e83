# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2023-11-12 05:10:44 UTC using RuboCop version 1.54.2.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 6
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLineAfterGuardClause:
  Exclude:
    - 'bin/bundle'

# Offense count: 5
# This cop supports safe autocorrection (--autocorrect).
Layout/EmptyLines:
  Exclude:
    - 'config/application.rb'
    - 'config/environments/production.rb'
    - 'db/migrate/20230522064429_devise_create_users.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: around, only_before
Layout/EmptyLinesAroundAccessModifier:
  Exclude:
    - 'db/migrate/20231010033338_create_active_storage_tables.active_storage.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines, beginning_only, ending_only
Layout/EmptyLinesAroundClassBody:
  Exclude:
    - 'config/application.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines
Layout/EmptyLinesAroundModuleBody:
  Exclude:
    - 'config/application.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowForAlignment, AllowBeforeTrailingComments, ForceEqualSignAlignment.
Layout/ExtraSpacing:
  Exclude:
    - 'app/controllers/bodygraphs_controller.rb'
    - 'test/controllers/streaming_videos_controller_test.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: normal, indented_internal_methods
Layout/IndentationConsistency:
  Exclude:
    - 'db/migrate/20231010033338_create_active_storage_tables.active_storage.rb'

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: Width, AllowedPatterns.
Layout/IndentationWidth:
  Exclude:
    - 'app/models/streaming_video.rb'
    - 'db/migrate/20231010033338_create_active_storage_tables.active_storage.rb'

# Offense count: 8
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBrackets.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBrackets: space, no_space
Layout/SpaceInsideArrayLiteralBrackets:
  Exclude:
    - 'config/environments/production.rb'
    - 'db/migrate/20231010033338_create_active_storage_tables.active_storage.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideHashLiteralBraces:
  Exclude:
    - 'test/controllers/streaming_videos_controller_test.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowInHeredoc.
Layout/TrailingWhitespace:
  Exclude:
    - 'config/environments/development.rb'
    - 'config/environments/production.rb'

# Offense count: 6
# Configuration parameters: AllowedMethods, AllowedPatterns, CountRepeatedAttributes.
Metrics/AbcSize:
  Max: 56

# Offense count: 9
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
# AllowedMethods: refine
Metrics/BlockLength:
  Max: 133

# Offense count: 1
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/CyclomaticComplexity:
  Max: 9

# Offense count: 7
# Configuration parameters: CountComments, CountAsOne, AllowedMethods, AllowedPatterns.
Metrics/MethodLength:
  Max: 36

# Offense count: 1
# Configuration parameters: AllowedMethods, AllowedPatterns.
Metrics/PerceivedComplexity:
  Max: 9

# Offense count: 1
Naming/ConstantName:
  Exclude:
    - 'app/helpers/static_pages_helper.rb'

# Offense count: 8
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: nested, compact
Style/ClassAndModuleChildren:
  Exclude:
    - 'app/controllers/users/confirmations_controller.rb'
    - 'app/controllers/users/omniauth_callbacks_controller.rb'
    - 'app/controllers/users/passwords_controller.rb'
    - 'app/controllers/users/registrations_controller.rb'
    - 'app/controllers/users/sessions_controller.rb'
    - 'app/controllers/users/unlocks_controller.rb'
    - 'test/channels/application_cable/connection_test.rb'
    - 'test/test_helper.rb'

# Offense count: 25
# Configuration parameters: AllowedConstants.
Style/Documentation:
  Enabled: false

# Offense count: 69
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: always, always_true, never
Style/FrozenStringLiteralComment:
  Enabled: false

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
Style/GlobalStdStream:
  Exclude:
    - 'config/environments/production.rb'

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
Style/IfUnlessModifier:
  Exclude:
    - 'bin/bundle'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: EnforcedStyle.
# SupportedStyles: literals, strict
Style/MutableConstant:
  Exclude:
    - 'app/helpers/static_pages_helper.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinDigits, Strict, AllowedNumbers, AllowedPatterns.
Style/NumericLiterals:
  Exclude:
    - 'db/schema.rb'

# Offense count: 1
# This cop supports safe autocorrection (--autocorrect).
Style/PerlBackrefs:
  Exclude:
    - 'bin/bundle'

# Offense count: 3
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: SafeForConstants.
Style/RedundantFetchBlock:
  Exclude:
    - 'config/puma.rb'

# Offense count: 1
# This cop supports unsafe autocorrection (--autocorrect-all).
# Configuration parameters: RequireEnglish.
# SupportedStyles: use_perl_names, use_english_names, use_builtin_english_names
Style/SpecialGlobalVars:
  EnforcedStyle: use_perl_names

# Offense count: 342
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Enabled: false

# Offense count: 3
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: .
# SupportedStyles: percent, brackets
Style/SymbolArray:
  EnforcedStyle: percent
  MinSize: 10

# Offense count: 2
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: MinSize, WordRegex.
# SupportedStyles: percent, brackets
Style/WordArray:
  EnforcedStyle: brackets

# Offense count: 12
# This cop supports safe autocorrection (--autocorrect).
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, AllowedPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 1355
