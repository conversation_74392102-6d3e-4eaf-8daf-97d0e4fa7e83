require "test_helper"

module Hdhd
  class ConferenceTicketMailerTest < ActionMailer::TestCase
    test "confirmation_email" do
      user = users(:one)
      conference = hdhd_conferences(:one)
      ticket = conference_tickets(:one)
      
      email = ConferenceTicketMailer.with(user: user, conference: conference, ticket: ticket).confirmation_email
      
      assert_emails 1 do
        email.deliver_now
      end
      
      assert_equal ["<EMAIL>"], email.from
      assert_equal [user.email], email.to
      assert_equal "Your HDHD #{conference.year} Conference Ticket", email.subject
    end
  end
end
