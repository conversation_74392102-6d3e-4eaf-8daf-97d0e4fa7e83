require "test_helper"

module Hdhd
  class PurchaseMailerTest < ActionMailer::TestCase
    test "confirmation_email" do
      user = users(:one)
      product = products(:one)
      
      email = PurchaseMailer.with(user: user, product: product).confirmation_email
      
      assert_emails 1 do
        email.deliver_now
      end
      
      assert_equal ["<EMAIL>"], email.from
      assert_equal [user.email], email.to
      assert_equal "Your purchase of #{product.title} was successful!", email.subject
    end
    
    test "account_creation_email" do
      user = users(:one)
      product = products(:one)
      password = "test-password"
      
      email = PurchaseMailer.with(user: user, product: product, password: password).account_creation_email
      
      assert_emails 1 do
        email.deliver_now
      end
      
      assert_equal ["<EMAIL>"], email.from
      assert_equal [user.email], email.to
      assert_equal "Account Created: High Desert Human Design", email.subject
    end
  end
end
