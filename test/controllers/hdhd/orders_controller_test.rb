require "test_helper"

module Hdhd
  class OrdersControllerTest < ActionDispatch::IntegrationTest
    include Devise::Test::IntegrationHelpers

    setup do
      @user = users(:one)
      @product = products(:one)
      sign_in @user
    end

    test "should get success" do
      get success_hdhd_orders_url(product_id: @product.id)
      assert_response :success
    end

    test "should get cancel" do
      get cancel_hdhd_orders_url
      assert_response :success
    end
  end
end
