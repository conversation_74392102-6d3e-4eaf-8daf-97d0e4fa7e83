require "test_helper"

class PublicBodygraphCommentsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @public_bodygraph_comment = public_bodygraph_comments(:one)
  end

  test "should get index" do
    get public_bodygraph_comments_url
    assert_response :success
  end

  test "should get new" do
    get new_public_bodygraph_comment_url
    assert_response :success
  end

  test "should create public_bodygraph_comment" do
    assert_difference("PublicBodygraphComment.count") do
      post public_bodygraph_comments_url, params: { public_bodygraph_comment: { comment_type: @public_bodygraph_comment.comment_type, file_attachment: @public_bodygraph_comment.file_attachment, href: @public_bodygraph_comment.href, public_bodygraph_id: @public_bodygraph_comment.public_bodygraph_id, title: @public_bodygraph_comment.title } }
    end

    assert_redirected_to public_bodygraph_comment_url(PublicBodygraphComment.last)
  end

  test "should show public_bodygraph_comment" do
    get public_bodygraph_comment_url(@public_bodygraph_comment)
    assert_response :success
  end

  test "should get edit" do
    get edit_public_bodygraph_comment_url(@public_bodygraph_comment)
    assert_response :success
  end

  test "should update public_bodygraph_comment" do
    patch public_bodygraph_comment_url(@public_bodygraph_comment), params: { public_bodygraph_comment: { comment_type: @public_bodygraph_comment.comment_type, file_attachment: @public_bodygraph_comment.file_attachment, href: @public_bodygraph_comment.href, public_bodygraph_id: @public_bodygraph_comment.public_bodygraph_id, title: @public_bodygraph_comment.title } }
    assert_redirected_to public_bodygraph_comment_url(@public_bodygraph_comment)
  end

  test "should destroy public_bodygraph_comment" do
    assert_difference("PublicBodygraphComment.count", -1) do
      delete public_bodygraph_comment_url(@public_bodygraph_comment)
    end

    assert_redirected_to public_bodygraph_comments_url
  end
end
