require "test_helper"

module CenterForHumanDesign
  class StaticPagesControllerTest < ActionDispatch::IntegrationTest
    test "should get home" do
      get center_for_human_design_home_url
      assert_response :success
    end

    test "should get about" do
      get center_for_human_design_about_url
      assert_response :success
    end

    test "should get history" do
      get center_for_human_design_history_url
      assert_response :success
    end

    test "should get initiatives" do
      get center_for_human_design_initiatives_url
      assert_response :success
    end

    test "should get terms_and_conditions" do
      get center_for_human_design_terms_and_conditions_url
      assert_response :success
    end

    test "should get privacy_policy" do
      get center_for_human_design_privacy_policy_url
      assert_response :success
    end

    test "should get cookie_policy" do
      get center_for_human_design_cookie_policy_url
      assert_response :success
    end
  end
end
