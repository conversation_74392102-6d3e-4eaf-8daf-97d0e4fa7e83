require "test_helper"

class StreamingVideosControllerTest < ActionDispatch::IntegrationTest
  setup do
    @streaming_video = streaming_videos(:one)
  end

  test "should get index" do
    get streaming_videos_url
    assert_response :success
  end

  test "should get new" do
    get new_streaming_video_url
    assert_response :success
  end

  test "should create streaming_video" do
    assert_difference("StreamingVideo.count") do
      post streaming_videos_url, params: { streaming_video: {  } }
    end

    assert_redirected_to streaming_video_url(StreamingVideo.last)
  end

  test "should show streaming_video" do
    get streaming_video_url(@streaming_video)
    assert_response :success
  end

  test "should get edit" do
    get edit_streaming_video_url(@streaming_video)
    assert_response :success
  end

  test "should update streaming_video" do
    patch streaming_video_url(@streaming_video), params: { streaming_video: {  } }
    assert_redirected_to streaming_video_url(@streaming_video)
  end

  test "should destroy streaming_video" do
    assert_difference("StreamingVideo.count", -1) do
      delete streaming_video_url(@streaming_video)
    end

    assert_redirected_to streaming_videos_url
  end
end
