require "test_helper"

class PublicBodygraphsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @public_bodygraph = public_bodygraphs(:one)
  end

  test "should get index" do
    get public_bodygraphs_url
    assert_response :success
  end

  test "should get new" do
    get new_public_bodygraph_url
    assert_response :success
  end

  test "should create public_bodygraph" do
    assert_difference("PublicBodygraph.count") do
      post public_bodygraphs_url, params: { public_bodygraph: { ajna_defined: @public_bodygraph.ajna_defined, aura_type: @public_bodygraph.aura_type, birth_city: @public_bodygraph.birth_city, birth_country: @public_bodygraph.birth_country, birth_data_collector: @public_bodygraph.birth_data_collector, birth_data_source: @public_bodygraph.birth_data_source, birth_data_source_notes: @public_bodygraph.birth_data_source_notes, birth_date_local: @public_bodygraph.birth_date_local, birth_date_utc: @public_bodygraph.birth_date_utc, birth_name: @public_bodygraph.birth_name, cognition: @public_bodygraph.cognition, definition: @public_bodygraph.definition, design_activations: @public_bodygraph.design_activations, design_date_utc: @public_bodygraph.design_date_utc, design_nodes_tone: @public_bodygraph.design_nodes_tone, determination: @public_bodygraph.determination, environment: @public_bodygraph.environment, g_center_defined: @public_bodygraph.g_center_defined, gender: @public_bodygraph.gender, head_defined: @public_bodygraph.head_defined, incarnation_cross: @public_bodygraph.incarnation_cross, inner_authority: @public_bodygraph.inner_authority, motivation: @public_bodygraph.motivation, name: @public_bodygraph.name, personality_activations: @public_bodygraph.personality_activations, personality_nodes_tone: @public_bodygraph.personality_nodes_tone, profile: @public_bodygraph.profile, rodden_rating: @public_bodygraph.rodden_rating, root_defined: @public_bodygraph.root_defined, sacral_defined: @public_bodygraph.sacral_defined, sense: @public_bodygraph.sense, solar_plexus_defined: @public_bodygraph.solar_plexus_defined, spleen_defined: @public_bodygraph.spleen_defined, throat_defined: @public_bodygraph.throat_defined, timezone: @public_bodygraph.timezone, variable: @public_bodygraph.variable, view: @public_bodygraph.view } }
    end

    assert_redirected_to public_bodygraph_url(PublicBodygraph.last)
  end

  test "should show public_bodygraph" do
    get public_bodygraph_url(@public_bodygraph)
    assert_response :success
  end

  test "should get edit" do
    get edit_public_bodygraph_url(@public_bodygraph)
    assert_response :success
  end

  test "should update public_bodygraph" do
    patch public_bodygraph_url(@public_bodygraph), params: { public_bodygraph: { ajna_defined: @public_bodygraph.ajna_defined, aura_type: @public_bodygraph.aura_type, birth_city: @public_bodygraph.birth_city, birth_country: @public_bodygraph.birth_country, birth_data_collector: @public_bodygraph.birth_data_collector, birth_data_source: @public_bodygraph.birth_data_source, birth_data_source_notes: @public_bodygraph.birth_data_source_notes, birth_date_local: @public_bodygraph.birth_date_local, birth_date_utc: @public_bodygraph.birth_date_utc, birth_name: @public_bodygraph.birth_name, cognition: @public_bodygraph.cognition, definition: @public_bodygraph.definition, design_activations: @public_bodygraph.design_activations, design_date_utc: @public_bodygraph.design_date_utc, design_nodes_tone: @public_bodygraph.design_nodes_tone, determination: @public_bodygraph.determination, environment: @public_bodygraph.environment, g_center_defined: @public_bodygraph.g_center_defined, gender: @public_bodygraph.gender, head_defined: @public_bodygraph.head_defined, incarnation_cross: @public_bodygraph.incarnation_cross, inner_authority: @public_bodygraph.inner_authority, motivation: @public_bodygraph.motivation, name: @public_bodygraph.name, personality_activations: @public_bodygraph.personality_activations, personality_nodes_tone: @public_bodygraph.personality_nodes_tone, profile: @public_bodygraph.profile, rodden_rating: @public_bodygraph.rodden_rating, root_defined: @public_bodygraph.root_defined, sacral_defined: @public_bodygraph.sacral_defined, sense: @public_bodygraph.sense, solar_plexus_defined: @public_bodygraph.solar_plexus_defined, spleen_defined: @public_bodygraph.spleen_defined, throat_defined: @public_bodygraph.throat_defined, timezone: @public_bodygraph.timezone, variable: @public_bodygraph.variable, view: @public_bodygraph.view } }
    assert_redirected_to public_bodygraph_url(@public_bodygraph)
  end

  test "should destroy public_bodygraph" do
    assert_difference("PublicBodygraph.count", -1) do
      delete public_bodygraph_url(@public_bodygraph)
    end

    assert_redirected_to public_bodygraphs_url
  end
end
