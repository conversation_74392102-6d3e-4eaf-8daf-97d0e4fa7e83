require "test_helper"

class Signposthd::EnneagramTriadsControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get enneagram_triads_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Enneagram Triads"
  end

  test "should show triad" do
    get enneagram_triad_url("125"), headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Self-Validation"
  end

  test "should redirect for invalid triad" do
    get enneagram_triad_url("999"), headers: { 'Host' => 'signposthd.com' }
    assert_redirected_to enneagram_triads_path
  end
end
