require "test_helper"

class Signposthd::EnneatypesControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get enneatypes_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Enneatypes"
  end

  test "should show enneatype" do
    get enneatype_url(1), headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Type 1 - The Perfectionist"
  end

  test "should redirect for invalid enneatype" do
    get enneatype_url(10), headers: { 'Host' => 'signposthd.com' }
    assert_redirected_to enneatypes_path
  end
end
