require "test_helper"

class Signposthd::EnneagramBrowserControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get enneagram_browser_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Enneagram Browser"
  end

  test "should display all three categories" do
    get enneagram_browser_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    
    # Check for links to each category
    assert_select "a[href='#{enneatypes_path}']"
    assert_select "a[href='#{enneagram_triads_path}']"
    assert_select "a[href='#{enneagram_trigroups_path}']"
  end
end
