require "test_helper"

class Signposthd::EnneagramTrigroupsControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get enneagram_trigroups_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Enneagram Trigroups"
  end

  test "should show trigroup" do
    get enneagram_trigroup_url("object-relations"), headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Object Relations"
  end

  test "should redirect for invalid trigroup" do
    get enneagram_trigroup_url("invalid-group"), headers: { 'Host' => 'signposthd.com' }
    assert_redirected_to enneagram_trigroups_path
  end
end
