require "test_helper"

class Signposthd::EnneagramStemsControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get enneagram_stems_url, headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Enneagram Stems"
  end

  test "should show stem" do
    get enneagram_stem_url("1-2"), headers: { 'Host' => 'signposthd.com' }
    assert_response :success
    assert_select "h1", "Stem 1-2"
  end

  test "should redirect for invalid stem" do
    get enneagram_stem_url("invalid-stem"), headers: { 'Host' => 'signposthd.com' }
    assert_redirected_to enneagram_stems_path
  end
end
