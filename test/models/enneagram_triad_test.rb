require "test_helper"

class EnneagramTriadTest < ActiveSupport::TestCase
  test "should return all triads" do
    triads = EnneagramTriad.all
    assert_equal 27, triads.count
  end

  test "should find triad by id" do
    triad = EnneagramTriad.find("125")
    assert_not_nil triad
    assert_equal "125", triad.id
    assert_equal "Self-Validation", triad.name
    assert_equal [1, 2, 5], triad.enneatypes
  end

  test "should return nil for invalid id" do
    triad = EnneagramTriad.find("999")
    assert_nil triad
  end

  test "should return enneatype objects" do
    triad = EnneagramTriad.find("125")
    enneatype_objects = triad.enneatype_objects
    assert_equal 3, enneatype_objects.count

    ids = enneatype_objects.map(&:id)
    assert_includes ids, 1
    assert_includes ids, 2
    assert_includes ids, 5
  end

  test "should have correct to_param" do
    triad = EnneagramTriad.find("379")
    assert_equal "379", triad.to_param
  end

  test "should generate statements for enneatype" do
    triad = EnneagramTriad.find("147") # Frustration triad
    statements = triad.statements_for_enneatype(1)
    assert statements.count.positive?
    assert_includes statements.join(" "), "frustrated"
  end

  test "should return qualities with triads" do
    triad = EnneagramTriad.find("137") # Vision triad
    qualities_with_triads = triad.qualities_with_triads
    assert qualities_with_triads.is_a?(Array)

    if qualities_with_triads.any?
      quality = qualities_with_triads.first
      assert quality.key?(:name)
      assert quality.key?(:triad)
      assert quality[:triad].is_a?(EnneagramTriad)
    end
  end

  test "should return secondary qualities" do
    triad = EnneagramTriad.find("137") # Vision triad (1, 3, 7)
    secondary_qualities = triad.secondary_qualities
    assert secondary_qualities.is_a?(Array)

    # Each secondary quality should have exactly one shared enneatype
    secondary_qualities.each do |quality|
      shared_enneatypes = triad.enneatypes & quality[:triad].enneatypes
      assert_equal 1, shared_enneatypes.length
      assert_equal shared_enneatypes.first, quality[:shared_enneatype]
    end
  end

  test "should return missing qualities" do
    triad = EnneagramTriad.find("137") # Vision triad (1, 3, 7)
    missing_qualities = triad.missing_qualities
    assert missing_qualities.is_a?(Array)

    # Each missing quality should have no shared enneatypes
    missing_qualities.each do |quality|
      shared_enneatypes = triad.enneatypes & quality[:triad].enneatypes
      assert_equal 0, shared_enneatypes.length
    end
  end
end
