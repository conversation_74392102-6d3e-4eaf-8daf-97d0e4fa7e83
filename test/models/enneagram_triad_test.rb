require "test_helper"

class EnneagramTriadTest < ActiveSupport::TestCase
  test "should return all triads" do
    triads = EnneagramTriad.all
    assert_equal 27, triads.count
  end

  test "should find triad by id" do
    triad = EnneagramTriad.find("125")
    assert_not_nil triad
    assert_equal "125", triad.id
    assert_equal "Self-Validation", triad.name
    assert_equal [1, 2, 5], triad.enneatypes
  end

  test "should return nil for invalid id" do
    triad = EnneagramTriad.find("999")
    assert_nil triad
  end

  test "should return enneatype objects" do
    triad = EnneagramTriad.find("125")
    enneatype_objects = triad.enneatype_objects
    assert_equal 3, enneatype_objects.count
    
    ids = enneatype_objects.map(&:id)
    assert_includes ids, 1
    assert_includes ids, 2
    assert_includes ids, 5
  end

  test "should have correct to_param" do
    triad = EnneagramTriad.find("379")
    assert_equal "379", triad.to_param
  end

  test "should generate statements for enneatype" do
    triad = EnneagramTriad.find("147") # Frustration triad
    statements = triad.statements_for_enneatype(1)
    assert statements.count > 0
    assert_includes statements.join(" "), "frustrated"
  end
end
