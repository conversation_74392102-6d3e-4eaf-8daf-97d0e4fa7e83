require "test_helper"

class EnneagramTrigroupTest < ActiveSupport::TestCase
  test "should return all trigroups" do
    trigroups = EnneagramTrigroup.all
    assert_equal 9, trigroups.count
  end

  test "should find trigroup by id" do
    trigroup = EnneagramTrigroup.find("object-relations")
    assert_not_nil trigroup
    assert_equal "object-relations", trigroup.id
    assert_equal "Object Relations", trigroup.name
    assert_equal ["147", "258", "369"], trigroup.triads
  end

  test "should return nil for invalid id" do
    trigroup = EnneagramTrigroup.find("invalid-group")
    assert_nil trigroup
  end

  test "should return triad objects" do
    trigroup = EnneagramTrigroup.find("object-relations")
    triad_objects = trigroup.triad_objects
    assert_equal 3, triad_objects.count
    
    ids = triad_objects.map(&:id)
    assert_includes ids, "147"
    assert_includes ids, "258"
    assert_includes ids, "369"
  end

  test "should have correct to_param" do
    trigroup = EnneagramTrigroup.find("group-a-validation-through")
    assert_equal "group-a-validation-through", trigroup.to_param
  end
end
