require "test_helper"

class EnneagramStemTest < ActiveSupport::TestCase
  test "should return all stems" do
    stems = EnneagramStem.all
    assert_equal 27, stems.count
  end

  test "should find stem by id" do
    stem = EnneagramStem.find("1-2")
    assert_not_nil stem
    assert_equal "1-2", stem.id
    assert_equal "Stem 1-2", stem.name
    assert_equal [1, 2], stem.enneatypes
    assert_equal "Body–Heart", stem.centers
  end

  test "should return nil for invalid id" do
    stem = EnneagramStem.find("invalid-stem")
    assert_nil stem
  end

  test "should return enneatype objects" do
    stem = EnneagramStem.find("1-2")
    enneatype_objects = stem.enneatype_objects
    assert_equal 2, enneatype_objects.count
    
    ids = enneatype_objects.map(&:id)
    assert_includes ids, 1
    assert_includes ids, 2
  end

  test "should find stems for triad" do
    triad = EnneagramTriad.find("147")
    stems = EnneagramStem.for_triad(triad)
    
    assert stems.count > 0
    stem_ids = stems.map(&:id)
    assert_includes stem_ids, "1-4"
    assert_includes stem_ids, "1-7"
    assert_includes stem_ids, "4-7"
  end

  test "should have correct to_param" do
    stem = EnneagramStem.find("2-5")
    assert_equal "2-5", stem.to_param
  end
end
