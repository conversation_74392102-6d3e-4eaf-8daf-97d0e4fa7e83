require "test_helper"

class EnneatypeTest < ActiveSupport::TestCase
  test "should return all enneatypes" do
    enneatypes = Enneatype.all
    assert_equal 9, enneatypes.count
  end

  test "should find enneatype by id" do
    enneatype = Enneatype.find(1)
    assert_not_nil enneatype
    assert_equal 1, enneatype.id
    assert_equal "Type 1 - The Perfectionist", enneatype.name
  end

  test "should return nil for invalid id" do
    enneatype = Enneatype.find(10)
    assert_nil enneatype
  end

  test "should return triads for enneatype" do
    enneatype = Enneatype.find(1)
    triads = enneatype.triads
    assert triads.count > 0
    # Enneatype 1 should be in triads like 125, 126, 127, etc.
    triad_ids = triads.map(&:id)
    assert_includes triad_ids, "125"
    assert_includes triad_ids, "126"
    assert_includes triad_ids, "127"
  end

  test "should have correct to_param" do
    enneatype = Enneatype.find(5)
    assert_equal "5", enneatype.to_param
  end
end
