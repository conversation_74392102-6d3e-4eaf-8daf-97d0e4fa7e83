require "application_system_test_case"

class StreamingVideosTest < ApplicationSystemTestCase
  setup do
    @streaming_video = streaming_videos(:one)
  end

  test "visiting the index" do
    visit streaming_videos_url
    assert_selector "h1", text: "Streaming videos"
  end

  test "should create streaming video" do
    visit streaming_videos_url
    click_on "New streaming video"

    click_on "Create Streaming video"

    assert_text "Streaming video was successfully created"
    click_on "Back"
  end

  test "should update Streaming video" do
    visit streaming_video_url(@streaming_video)
    click_on "Edit this streaming video", match: :first

    click_on "Update Streaming video"

    assert_text "Streaming video was successfully updated"
    click_on "Back"
  end

  test "should destroy Streaming video" do
    visit streaming_video_url(@streaming_video)
    click_on "Destroy this streaming video", match: :first

    assert_text "Streaming video was successfully destroyed"
  end
end
