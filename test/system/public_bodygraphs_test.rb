require "application_system_test_case"

class PublicBodygraphsTest < ApplicationSystemTestCase
  setup do
    @public_bodygraph = public_bodygraphs(:one)
  end

  test "visiting the index" do
    visit public_bodygraphs_url
    assert_selector "h1", text: "Public bodygraphs"
  end

  test "should create public bodygraph" do
    visit public_bodygraphs_url
    click_on "New public bodygraph"

    check "<PERSON><PERSON><PERSON> defined" if @public_bodygraph.ajna_defined
    fill_in "Aura type", with: @public_bodygraph.aura_type
    fill_in "Birth city", with: @public_bodygraph.birth_city
    fill_in "Birth country", with: @public_bodygraph.birth_country
    fill_in "Birth data collector", with: @public_bodygraph.birth_data_collector
    fill_in "Birth data source", with: @public_bodygraph.birth_data_source
    fill_in "Birth data source notes", with: @public_bodygraph.birth_data_source_notes
    fill_in "Birth date local", with: @public_bodygraph.birth_date_local
    fill_in "Birth date utc", with: @public_bodygraph.birth_date_utc
    fill_in "Birth name", with: @public_bodygraph.birth_name
    fill_in "Cognition", with: @public_bodygraph.cognition
    fill_in "Definition", with: @public_bodygraph.definition
    fill_in "Design activations", with: @public_bodygraph.design_activations
    fill_in "Design date utc", with: @public_bodygraph.design_date_utc
    fill_in "Design nodes tone", with: @public_bodygraph.design_nodes_tone
    fill_in "Determination", with: @public_bodygraph.determination
    fill_in "Environment", with: @public_bodygraph.environment
    check "G center defined" if @public_bodygraph.g_center_defined
    fill_in "Gender", with: @public_bodygraph.gender
    check "Head defined" if @public_bodygraph.head_defined
    fill_in "Incarnation cross", with: @public_bodygraph.incarnation_cross
    fill_in "Inner authority", with: @public_bodygraph.inner_authority
    fill_in "Motivation", with: @public_bodygraph.motivation
    fill_in "Name", with: @public_bodygraph.name
    fill_in "Personality activations", with: @public_bodygraph.personality_activations
    fill_in "Personality nodes tone", with: @public_bodygraph.personality_nodes_tone
    fill_in "Profile", with: @public_bodygraph.profile
    fill_in "Rodden rating", with: @public_bodygraph.rodden_rating
    check "Root defined" if @public_bodygraph.root_defined
    check "Sacral defined" if @public_bodygraph.sacral_defined
    fill_in "Sense", with: @public_bodygraph.sense
    check "Solar plexus defined" if @public_bodygraph.solar_plexus_defined
    check "Spleen defined" if @public_bodygraph.spleen_defined
    check "Throat defined" if @public_bodygraph.throat_defined
    fill_in "Timezone", with: @public_bodygraph.timezone
    fill_in "Variable", with: @public_bodygraph.variable
    fill_in "View", with: @public_bodygraph.view
    click_on "Create Public bodygraph"

    assert_text "Public bodygraph was successfully created"
    click_on "Back"
  end

  test "should update Public bodygraph" do
    visit public_bodygraph_url(@public_bodygraph)
    click_on "Edit this public bodygraph", match: :first

    check "Ajna defined" if @public_bodygraph.ajna_defined
    fill_in "Aura type", with: @public_bodygraph.aura_type
    fill_in "Birth city", with: @public_bodygraph.birth_city
    fill_in "Birth country", with: @public_bodygraph.birth_country
    fill_in "Birth data collector", with: @public_bodygraph.birth_data_collector
    fill_in "Birth data source", with: @public_bodygraph.birth_data_source
    fill_in "Birth data source notes", with: @public_bodygraph.birth_data_source_notes
    fill_in "Birth date local", with: @public_bodygraph.birth_date_local
    fill_in "Birth date utc", with: @public_bodygraph.birth_date_utc
    fill_in "Birth name", with: @public_bodygraph.birth_name
    fill_in "Cognition", with: @public_bodygraph.cognition
    fill_in "Definition", with: @public_bodygraph.definition
    fill_in "Design activations", with: @public_bodygraph.design_activations
    fill_in "Design date utc", with: @public_bodygraph.design_date_utc
    fill_in "Design nodes tone", with: @public_bodygraph.design_nodes_tone
    fill_in "Determination", with: @public_bodygraph.determination
    fill_in "Environment", with: @public_bodygraph.environment
    check "G center defined" if @public_bodygraph.g_center_defined
    fill_in "Gender", with: @public_bodygraph.gender
    check "Head defined" if @public_bodygraph.head_defined
    fill_in "Incarnation cross", with: @public_bodygraph.incarnation_cross
    fill_in "Inner authority", with: @public_bodygraph.inner_authority
    fill_in "Motivation", with: @public_bodygraph.motivation
    fill_in "Name", with: @public_bodygraph.name
    fill_in "Personality activations", with: @public_bodygraph.personality_activations
    fill_in "Personality nodes tone", with: @public_bodygraph.personality_nodes_tone
    fill_in "Profile", with: @public_bodygraph.profile
    fill_in "Rodden rating", with: @public_bodygraph.rodden_rating
    check "Root defined" if @public_bodygraph.root_defined
    check "Sacral defined" if @public_bodygraph.sacral_defined
    fill_in "Sense", with: @public_bodygraph.sense
    check "Solar plexus defined" if @public_bodygraph.solar_plexus_defined
    check "Spleen defined" if @public_bodygraph.spleen_defined
    check "Throat defined" if @public_bodygraph.throat_defined
    fill_in "Timezone", with: @public_bodygraph.timezone
    fill_in "Variable", with: @public_bodygraph.variable
    fill_in "View", with: @public_bodygraph.view
    click_on "Update Public bodygraph"

    assert_text "Public bodygraph was successfully updated"
    click_on "Back"
  end

  test "should destroy Public bodygraph" do
    visit public_bodygraph_url(@public_bodygraph)
    click_on "Destroy this public bodygraph", match: :first

    assert_text "Public bodygraph was successfully destroyed"
  end
end
