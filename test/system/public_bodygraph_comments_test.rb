require "application_system_test_case"

class PublicBodygraphCommentsTest < ApplicationSystemTestCase
  setup do
    @public_bodygraph_comment = public_bodygraph_comments(:one)
  end

  test "visiting the index" do
    visit public_bodygraph_comments_url
    assert_selector "h1", text: "Public bodygraph comments"
  end

  test "should create public bodygraph comment" do
    visit public_bodygraph_comments_url
    click_on "New public bodygraph comment"

    fill_in "Comment type", with: @public_bodygraph_comment.comment_type
    fill_in "File attachment", with: @public_bodygraph_comment.file_attachment
    fill_in "Href", with: @public_bodygraph_comment.href
    fill_in "Public bodygraph", with: @public_bodygraph_comment.public_bodygraph_id
    fill_in "Title", with: @public_bodygraph_comment.title
    click_on "Create Public bodygraph comment"

    assert_text "Public bodygraph comment was successfully created"
    click_on "Back"
  end

  test "should update Public bodygraph comment" do
    visit public_bodygraph_comment_url(@public_bodygraph_comment)
    click_on "Edit this public bodygraph comment", match: :first

    fill_in "Comment type", with: @public_bodygraph_comment.comment_type
    fill_in "File attachment", with: @public_bodygraph_comment.file_attachment
    fill_in "Href", with: @public_bodygraph_comment.href
    fill_in "Public bodygraph", with: @public_bodygraph_comment.public_bodygraph_id
    fill_in "Title", with: @public_bodygraph_comment.title
    click_on "Update Public bodygraph comment"

    assert_text "Public bodygraph comment was successfully updated"
    click_on "Back"
  end

  test "should destroy Public bodygraph comment" do
    visit public_bodygraph_comment_url(@public_bodygraph_comment)
    click_on "Destroy this public bodygraph comment", match: :first

    assert_text "Public bodygraph comment was successfully destroyed"
  end
end
