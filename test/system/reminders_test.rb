require "application_system_test_case"

class RemindersTest < ApplicationSystemTestCase
  setup do
    @reminder = reminders(:one)
  end

  test "visiting the index" do
    visit reminders_url
    assert_selector "h1", text: "Reminders"
  end

  test "should create reminder" do
    visit reminders_url
    click_on "New reminder"

    fill_in "Description", with: @reminder.description
    fill_in "Timezone", with: @reminder.timezone
    fill_in "Title", with: @reminder.title
    fill_in "User", with: @reminder.user_id
    fill_in "Utc time", with: @reminder.utc_time
    click_on "Create Reminder"

    assert_text "<PERSON>minder was successfully created"
    click_on "Back"
  end

  test "should update <PERSON>minder" do
    visit reminder_url(@reminder)
    click_on "Edit this reminder", match: :first

    fill_in "Description", with: @reminder.description
    fill_in "Timezone", with: @reminder.timezone
    fill_in "Title", with: @reminder.title
    fill_in "User", with: @reminder.user_id
    fill_in "Utc time", with: @reminder.utc_time
    click_on "Update Reminder"

    assert_text "<PERSON><PERSON><PERSON> was successfully updated"
    click_on "Back"
  end

  test "should destroy <PERSON>mind<PERSON>" do
    visit reminder_url(@reminder)
    click_on "Destroy this reminder", match: :first

    assert_text "<PERSON>mind<PERSON> was successfully destroyed"
  end
end
