<%
  def nav_link_classes(path)
    current_page?(path) ?
      "group flex gap-x-3 rounded-md bg-gray-800 p-2 text-sm/6 font-semibold text-white" :
      "group flex gap-x-3 rounded-md p-2 text-sm/6 font-semibold text-gray-400 hover:bg-gray-800 hover:text-white"
  end
%>
<!DOCTYPE html>
<html class="h-full">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-M0TE7FYBFF"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-M0TE7FYBFF');
    </script>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= display_meta_tags site: "High Desert Human Design" %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag 'rails-ujs' %>
    <%- javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
    <%= javascript_include_tag 'constants' %>
    <%= javascript_include_tag 'helpers' %>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2/dist/alpine.min.js" defer></script>
    <%= javascript_importmap_tags %>
    <script type="module">
      import "stimulus";
    </script>
    <style>
      [x-cloak] { display: none !important; }
    </style>
  </head>
  <body x-data="{ mobileMenuOpen: false }">
    <%= render 'shared/notices_and_alerts' %>
    <div>
      <!-- Static sidebar for desktop -->
      <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
          <div class="flex h-16 shrink-0 items-center">
            <a href="/">
              <img class="h-10 w-auto" src="<%= asset_path('hdhd/high-desert-human-design-logo.png') %>" alt="High Desert Human Design">
            </a>
          </div>
          <%= render 'hdhd/partials/navigation_links' %>
        </div>
      </div>
      <div class="lg:pl-72">
        <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <!-- Mobile menu button -->
          <button type="button"
                  class="-m-2.5 p-2.5 text-gray-700 lg:hidden"
                  @click="mobileMenuOpen = !mobileMenuOpen">
            <span class="sr-only">Open sidebar</span>
            <svg class="size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
          <!-- Separator -->
          <div class="h-6 w-px bg-gray-900/10 lg:hidden" aria-hidden="true"></div>
          <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div class="grid flex-1 grid-cols-1"></div>
            <div class="flex items-center gap-x-4 lg:gap-x-6">
              <!-- Notifications -->
              <div class="relative" x-data="{ open: false, hasUnread: <%= current_user.notifications.unread.exists? %> }" data-controller="notifications">
                <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500" @click="open = !open">
                  <span class="sr-only">View notifications</span>
                  <svg class="size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" />
                  </svg>
                  <span
                    x-show="hasUnread"
                    class="absolute top-0 right-0 block size-2 rounded-full bg-red-400 ring-2 ring-white"
                    id="notification-indicator"></span>
                </button>
                <div x-show="open"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none"
                     @click.away="open = false">
                  <% unread_notifications = current_user.notifications.unread.recent.limit(5) %>
                  <% read_notifications = current_user.notifications.read.recent.limit(5) %>
                  <!-- Header with Mark All as Read button -->
                  <div class="px-3 py-2 border-b border-gray-100 flex justify-between items-center">
                    <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
                    <% if unread_notifications.any? %>
                      <%= button_to mark_all_as_read_notifications_path,
                                  method: :post,
                                  class: "text-xs text-indigo-600 hover:text-indigo-800",
                                  form: { data: { turbo: false }, class: "inline" },
                                  data: { action: "click->notifications#markAllAsRead" } do %>
                        Mark all as read
                      <% end %>
                    <% end %>
                  </div>
                  <!-- Unread Notifications -->
                  <div id="unread-notifications">
                    <% if unread_notifications.any? %>
                      <% unread_notifications.each do |notification| %>
                        <div class="notification-item" data-notification-id="<%= notification.id %>">
                          <%= link_to notification.action_url,
                                    class: "block px-3 py-2 hover:bg-gray-50 text-gray-900 border-l-4 border-indigo-500",
                                    data: { notification_id: notification.id, action: "click->notifications#markAsRead" } do %>
                            <p class="font-medium"><%= notification.title %></p>
                            <p class="text-sm"><%= notification.message %></p>
                            <p class="text-xs text-gray-500 mt-1"><%= time_ago_in_words(notification.created_at) %> ago</p>
                          <% end %>
                        </div>
                      <% end %>
                    <% else %>
                      <div class="px-3 py-2 text-gray-500 text-sm">
                        You have no new notifications.
                      </div>
                    <% end %>
                  </div>
                  <!-- Past Notifications -->
                  <% if read_notifications.any? %>
                    <div class="border-t border-gray-100 mt-2 pt-2">
                      <h4 class="px-3 py-1 text-xs font-medium text-gray-500 uppercase tracking-wider">Past Notifications</h4>
                      <div id="read-notifications">
                        <% read_notifications.each do |notification| %>
                          <%= link_to notification.action_url,
                                    class: "block px-3 py-2 hover:bg-gray-50 text-gray-500" do %>
                            <p class="font-medium"><%= notification.title %></p>
                            <p class="text-sm"><%= notification.message %></p>
                            <p class="text-xs text-gray-400 mt-1"><%= time_ago_in_words(notification.created_at) %> ago</p>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
              <!-- Separator -->
              <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10" aria-hidden="true"></div>
              <!-- Profile dropdown -->
              <div class="relative" x-data="{ open: false }">
                <button type="button" class="-m-1.5 flex items-center p-1.5" id="user-menu-button" aria-expanded="false" aria-haspopup="true" @click="open = !open">
                  <span class="sr-only">Open user menu</span>
                  <% if current_user.avatar.attached? %>
                    <%= image_tag current_user.avatar, class: "size-8 rounded-full bg-gray-50" %>
                  <% else %>
                    <% name_parts = current_user.name.to_s.split(" ") %>
                    <% initials = if name_parts.size > 1 %>
                    <%= "#{name_parts[0][0]}#{name_parts[-1][0]}" %>
                  <% else %>
                    <%= current_user.email[0].upcase %>
                  <% end %>
                  <div class="size-8 rounded-full bg-gray-50 flex items-center justify-center text-lg font-semibold text-gray-900">
                    <%= initials %>
                  </div>
                <% end %>
                <span class="hidden lg:flex lg:items-center">
                  <span class="ml-4 text-sm/6 font-semibold text-gray-900" aria-hidden="true">
                    <%= current_user.name.present? ? current_user.name : current_user.email %>
                  </span>
                </span>
              </button>
              <!-- Dropdown menu -->
              <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100"
       x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95"
       class="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1"
       @click.away="open = false">
                <%= link_to "Your profile",
                            user_profile_path,
                            class: "block px-3 py-1 text-sm/6 #{current_page?(user_profile_path) ? 'bg-gray-100 text-gray-900' : 'text-gray-900 hover:bg-gray-50'}",
                            role: "menuitem",
                            tabindex: "-1",
                            id: "user-menu-item-0" %>
                <%= button_to "Sign out",
                            destroy_user_session_path,
                            method: :delete,
                            class: "block w-full text-left px-3 py-1 text-sm/6 text-gray-900",
                            role: "menuitem",
                            tabindex: "-1",
                            id: "user-menu-item-1" %>
              </div>
            </div>
          </div>
        </div>
      </div>
      <main class="py-10" style="min-height: 900px;">
        <div class="px-4 sm:px-6 lg:px-8">
          <%= yield %>
        </div>
      </main>
      <footer class="bg-gray-900 text-white py-8 mt-12">
        <div class="container mx-auto px-6">
          <div class="flex flex-col md:flex-row justify-between">
            <div class="mb-6 md:mb-0">
              <h3 class="text-lg font-semibold mb-2">High Desert Human Design</h3>
              <p class="text-gray-300">Annual Human Design Conference in Santa Fe, New Mexico</p>
            </div>
            <div>
              <h3 class="text-lg font-semibold mb-2">Quick Links</h3>
              <ul class="text-gray-300">
                <li class="mb-1"><a href="<%= hdhd_home_path %>" class="hover:text-white">Home</a></li>
                <li class="mb-1"><a href="<%= hdhd_about_path %>" class="hover:text-white">About</a></li>
                <li class="mb-1"><a href="<%= hdhd_terms_and_conditions_path %>" class="hover:text-white">Terms and Conditions</a></li>
              </ul>
            </div>
          </div>
          <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
            <p>&copy; <%= Date.today.year %> High Desert Human Design, LLC. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
    <!-- Mobile menu (off-canvas) -->
    <div x-cloak
         x-show="mobileMenuOpen"
         class="relative z-50 lg:hidden"
         role="dialog"
         aria-modal="true">
      <!-- Background backdrop -->
      <div x-show="mobileMenuOpen"
           x-transition:enter="transition-opacity ease-linear duration-300"
           x-transition:enter-start="opacity-0"
           x-transition:enter-end="opacity-100"
           x-transition:leave="transition-opacity ease-linear duration-300"
           x-transition:leave-start="opacity-100"
           x-transition:leave-end="opacity-0"
           class="fixed inset-0 bg-gray-900/80"></div>
      <div class="fixed inset-0 flex">
        <!-- Sliding panel -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="relative mr-16 flex w-full max-w-xs flex-1">
          <!-- Close button -->
          <div class="absolute right-0 top-0 -mr-12 pt-2">
            <button type="button"
                    class="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                    @click="mobileMenuOpen = false">
              <span class="sr-only">Close sidebar</span>
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <!-- Mobile menu content -->
          <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-2 ring-1 ring-white/10">
            <div class="flex h-16 shrink-0 items-center">
              <img class="h-8 w-auto" src="<%= asset_path('hdhd/high-desert-human-design-logo.png') %>" alt="High Desert Human Design">
            </div>
            <%= render 'hdhd/partials/navigation_links' %>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16499138278"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'AW-16499138278');
  </script>
</body>
</html>
