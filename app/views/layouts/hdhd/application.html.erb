<!DOCTYPE html>
<html>
  <head>
    <!-- Favicon -->
    <%= favicon_link_tag 'hdhd/favicon/favicon.ico' %>
    <link rel="apple-touch-icon" sizes="180x180" href="<%= asset_path('hdhd/favicon/apple-touch-icon.png') %>">
    <link rel="icon" type="image/png" sizes="32x32" href="<%= asset_path('hdhd/favicon/favicon-32x32.png') %>">
    <link rel="icon" type="image/png" sizes="16x16" href="<%= asset_path('hdhd/favicon/favicon-16x16.png') %>">
    <link rel="manifest" href="<%= asset_path('hdhd/favicon/site.webmanifest') %>">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-M0TE7FYBFF"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-M0TE7FYBFF');
    </script>
    <% unless user_signed_in? %>
      <script src="https://accounts.google.com/gsi/client" async defer></script>
      <script>
        window.onload = function () {
          console.log("Initializing Google Sign-In...");

          try {
            // Log the client ID being used (without revealing the full ID)
            const clientId = '<%= ENV['GOOGLE_CLIENT_ID'] %>';
            console.log("Using Google client ID: " + clientId.substring(0, 5) + "..." + clientId.substring(clientId.length - 5));

            // Check if we're on a localhost domain
            const currentOrigin = window.location.origin;
            console.log("Current origin: " + currentOrigin);

            // For local development with custom domains, we need to use a special approach
            // because Google OAuth only allows standard domains like localhost:3000
            const isLocalDevelopment = currentOrigin.includes('localhost') ||
                                      currentOrigin.includes('.localhost') ||
                                      currentOrigin.includes('127.0.0.1');

            if (isLocalDevelopment) {
              console.log("Local development environment detected.");
              console.log("For Google Sign-In to work locally, make sure 'http://localhost:3000' is added to your Google OAuth client's authorized JavaScript origins.");
              console.log("You may need to access the site via http://localhost:3000 instead of a custom domain.");
            }

            // Initialize Google Sign-In with FedCM support
            google.accounts.id.initialize({
              client_id: clientId,
              callback: handleGoogleSignInResponse,
              use_fedcm_for_prompt: true, // Enable FedCM
              auto_select: false,
              cancel_on_tap_outside: true,
              context: 'signin',
              ux_mode: 'popup',
              login_uri: '<%= request.base_url %>/auth/google_login',
              nonce: '<%= SecureRandom.hex(16) %>',
              prompt_parent_id: 'g_id_onload'
            });

            // Add a hidden container for Google One Tap
            const container = document.createElement('div');
            container.id = 'g_id_onload';
            container.style.position = 'fixed';
            container.style.top = '10px';
            container.style.right = '10px';
            container.style.zIndex = '1000';
            container.style.visibility = 'hidden'; // Hide the container
            document.body.appendChild(container);

            // Also show the One Tap prompt
            google.accounts.id.prompt();
          } catch (error) {
            console.error("Error initializing Google Sign-In:", error);

            // Log error but don't show a fallback button
            console.error("Google One Tap initialization failed. Users will need to sign in through the standard login page.");

            // Show an error message
            console.warn("Google Sign-In is not available. Please check that your domain is authorized in the Google Cloud Console.");
            console.warn("Add this origin to your Google OAuth client: " + window.location.origin);

            // For local development, suggest using localhost:3000
            if (window.location.hostname.includes('.localhost')) {
              const localhostUrl = window.location.href.replace(window.location.hostname, 'localhost:3000');
              console.warn("For local development, try accessing the site via: " + localhostUrl);

              // Add a special notice for local development
              const notice = document.createElement('div');
              notice.style.position = 'fixed';
              notice.style.bottom = '10px';
              notice.style.left = '10px';
              notice.style.right = '10px';
              notice.style.backgroundColor = '#f8d7da';
              notice.style.color = '#721c24';
              notice.style.padding = '10px';
              notice.style.borderRadius = '4px';
              notice.style.zIndex = '1000';
              notice.style.textAlign = 'center';
              notice.innerHTML = 'Google Sign-In requires <strong>localhost:3000</strong> for local development. <a href="' + localhostUrl + '" style="color: #721c24; text-decoration: underline;">Click here to switch</a>.';
              document.body.appendChild(notice);
            }
          }
        };

        function handleGoogleSignInResponse(response) {
          console.log("Google Sign-In Response received");

          if (!response || !response.credential) {
            console.error("Invalid response from Google Sign-In");
            return;
          }

          // Get CSRF token
          const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
          console.log("CSRF Token present:", !!csrfToken);

          // Send the credential to the server
          console.log("Sending credential to server...");
          fetch("/auth/google_login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRF-Token": csrfToken
            },
            body: JSON.stringify({ token: response.credential })
          })
          .then(response => {
            console.log("Response status:", response.status);
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log("Backend response:", data);
            if (data.success) {
              console.log("Login successful, redirecting to dashboard...");
              window.location.href = data.redirect_url || "/dashboard";
            } else {
              console.error("Login failed:", data.error);
              alert("Login failed: " + (data.error || "Unknown error"));
            }
          })
          .catch(error => {
            console.error("Error during authentication:", error);
            alert("Authentication error: " + error.message);
          });
        }
      </script>
    <% end %>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <% if !user_signed_in? %>
      <meta name="csrf-token" content="<%= form_authenticity_token %>">
    <% end %>
    <%= display_meta_tags site: "High Desert Human Design", title: @page_title %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag 'rails-ujs' %>
    <%- javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
    <%= javascript_include_tag 'constants' %>
    <%= javascript_include_tag 'helpers' %>
    <%= javascript_include_tag 'cookie_consent' %>
    <%= javascript_importmap_tags %>
    <script>
      // JavaScript to toggle the mobile menu
      function toggleMobileMenu() {
        const menu = document.getElementById("mobile-menu");
        menu.classList.toggle("hidden");
      }
    </script>
  </head>
  <body class="bg-black">
    <%= render 'shared/notices_and_alerts' %>
    <!-- Blurred background fixed to the bottom -->
    <div class="absolute inset-x-0 bottom-0 -z-10 transform-gpu overflow-hidden blur-3xl" aria-hidden="true">
      <div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
    </div>
    <header class="absolute inset-x-0 top-0 z-50">
      <nav class="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div class="hidden sm:flex sm:flex-1">
          <a href="/" class="-m-1.5 p-1.5">
            <span class="sr-only">High Desert Human Design</span>
            <%= image_tag "hdhd/high-desert-human-design-logo.png", class:"w-auto h-10" %>
          </a>
        </div>
        <!-- Hamburger Icon -->
        <div class="sm:hidden">
          <button onclick="toggleMobileMenu()" class="text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
        <div class="hidden sm:flex sm:gap-x-12">
          <a href="https://quantumcollective.my.canva.site/high-desert-human-design-2025" class="text-sm/6 font-semibold text-white">HDHD 2025</a>
          <a href="/previous-years" class="text-sm/6 font-semibold text-white">Previous Years</a>
          <a href="/shop" class="text-sm/6 font-semibold text-white">Shop</a>
          <a href="/about" class="text-sm/6 font-semibold text-white">About</a>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:justify-end">
          <% if user_signed_in? %>
            <a href="/dashboard" class="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-white hover:bg-gray-800">Dashboard</a>
          <% else %>
            <a href="<%= new_user_session_path %>" class="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-white hover:bg-gray-800">Log in <span aria-hidden="true">&rarr;</span></a>
          <% end %>
        </div>
      </nav>
      <!-- Mobile Menu (Initially Hidden) -->
      <div id="mobile-menu" class="hidden block sm:hidden" role="dialog" aria-modal="true">
        <div class="inset-y-0 z-50 w-full overflow-y-auto bg-gray-900 px-6 py-6 md:max-w-sm md:ring-1 md:ring-white/10">
          <div class="flow-root">
            <div class="-my-6 divide-y divide-gray-500/25">
              <div class="space-y-2 py-6">
                <a href="https://quantumcollective.my.canva.site/high-desert-human-design-2025" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-white hover:bg-gray-800">HDHD 2025</a>
                <a href="/previous-years" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-white hover:bg-gray-800">Previous Years</a>
                <a href="/shop" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-white hover:bg-gray-800">Shop</a>
                <a href="/about" class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-white hover:bg-gray-800">About</a>
              </div>
              <div class="py-6">
                <% if user_signed_in? %>
                  <a href="/dashboard" class="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-white hover:bg-gray-800">Dashboard</a>
                <% else %>
                  <a href="<%= new_user_session_path %>" class="-mx-3 block rounded-lg px-3 py-2.5 text-base/7 font-semibold text-white hover:bg-gray-800">Log in</a>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
    <main style="margin-top: 96px;">
      <%= yield %>
    </main>
    <footer class="bg-gray-900 text-white py-8 mt-20">
      <div class="container mx-auto px-6">
        <div class="flex flex-col md:flex-row justify-between">
          <div class="mb-6 md:mb-0">
            <h3 class="text-lg font-semibold mb-2">High Desert Human Design</h3>
            <p class="text-gray-300">Annual Human Design Conference in Santa Fe, New Mexico</p>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-2">Quick Links</h3>
            <ul class="text-gray-300">
              <li class="mb-1"><a href="<%= hdhd_home_path %>" class="hover:text-white">Home</a></li>
              <li class="mb-1"><a href="<%= hdhd_about_path %>" class="hover:text-white">About</a></li>
              <li class="mb-1"><a href="<%= hdhd_privacy_policy_path %>" class="hover:text-white">Privacy Policy</a></li>
              <li class="mb-1"><a href="<%= hdhd_cookie_policy_path %>" class="hover:text-white">Cookie Policy</a></li>
              <li class="mb-1"><a href="<%= hdhd_terms_and_conditions_path %>" class="hover:text-white">Terms and Conditions</a></li>
            </ul>
          </div>
        </div>
        <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300">
          <p>&copy; <%= Date.today.year %> High Desert Human Design, LLC. All rights reserved.</p>
        </div>
      </div>
    </footer>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16499138278"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'AW-16499138278');
    </script>
  </body>
</html>
