<!DOCTYPE html>
<html class="h-full bg-black">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-M0TE7FYBFF"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-M0TE7FYBFF');
    </script>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%
=begin%>
    TODO: Add favicon
    <%= favicon_link_tag "admin/favicon.png" %>
    <%
=end
%>
    <%= display_meta_tags site: ENV["website_title"] %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag 'rails-ujs' %>
    <%- javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
    <%= javascript_include_tag 'constants' %>
    <%= javascript_include_tag 'helpers' %>
    <%= javascript_importmap_tags %>
  </head>
  <body class="h-full">
    <main>
      <% if notice.present? %>
        <p class="flash-message py-2 px-3 bg-green-50 mb-5 text-green-500 font-medium rounded-lg" id="notice"><%= notice %></p>
      <% end %>
      <% if alert.present? %>
        <p class="flash-message alert p-4 text-center text-green-500"><%= alert %></p>
      <% end %>
      <!-- 
      <div style="background-image: url('<%= image_path('streaming-videos-bg.gif') %>'); background-size: cover; background-position: center; position: absolute; left: 0; top: 0; right: 0; bottom: 0; opacity: .3;">
      </div>
      -->
      <!-- Main Section -->
      <%= yield %>
    </main>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-16499138278"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'AW-16499138278');
    </script>
  </body>
</html>
