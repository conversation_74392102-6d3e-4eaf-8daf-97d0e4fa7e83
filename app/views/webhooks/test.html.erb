<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-4">Stripe Webhook Test</h1>
  <p class="mb-4">This page is for testing the Stripe webhook endpoint.</p>
  <p class="mb-4">Webhook URL: <code class="bg-gray-100 p-1 rounded">https://www.highdeserthumandesign.com/webhooks/stripe</code></p>
  <p class="text-sm text-gray-600 mb-4">Note: Make sure to use the www version of the domain as shown above, since the non-www version redirects to www.</p>
  <p class="mb-4">Current environment: <code class="bg-gray-100 p-1 rounded"><%= Rails.env %></code></p>
  <p class="mb-4">Webhook secret configured: <code class="bg-gray-100 p-1 rounded"><%= Rails.env.production? ? (ENV['STRIPE_WEBHOOK_PRODUCTION_SECRET'].present? ? 'Yes' : 'No') : (ENV['STRIPE_WEBHOOK_SECRET'].present? ? 'Yes' : 'No') %></code></p>
  <div class="mt-8 p-4 bg-yellow-50 border-l-4 border-yellow-400">
    <h2 class="text-lg font-semibold mb-2">Testing Instructions</h2>
    <ol class="list-decimal pl-5 space-y-2">
      <li>Go to your Stripe Dashboard > Developers > Webhooks</li>
      <li>Click "Add endpoint"</li>
      <li>Enter the webhook URL shown above</li>
      <li>Select the events you want to listen for (at minimum: checkout.session.completed)</li>
      <li>Click "Add endpoint"</li>
      <li>Use the "Send test webhook" button to test the endpoint</li>
    </ol>
  </div>
</div>
