<%
  # This partial renders a bodygraph with configurable options
  # Required parameters:
  # - id: A unique identifier for this bodygraph instance
  # - personality_activations_json: JSON of personality activations
  # - design_activations_json: JSON of design activations
  #
  # Optional parameters:
  # - mode: 'all', 'design', 'personality', or 'incarnation-cross' (default: 'all')
  # - show_metadata: Whether to show type, authority, etc. (default: false)
  # - metadata: Hash containing :aura_type, :inner_authority, :profile, :definition, :incarnation_cross
  # - width: Width of the bodygraph (default: 430)
  # - height: Height of the bodygraph container (default: 682)
  # - bg_opacity: Opacity of the background image (default: 0.3)
  # - center_colors: Custom colors for centers (optional)
  # - center_opacity: Opacity for center gradients (default: 0.9)

  # Set defaults
  id = local_assigns[:id] || SecureRandom.hex(4)
  mode = local_assigns[:mode] || 'all'
  show_metadata = local_assigns[:show_metadata] || false
  metadata = local_assigns[:metadata] || {}
  width = local_assigns[:width] || 430
  height = local_assigns[:height] || 682
  bg_opacity = local_assigns[:bg_opacity] || 0.3

  # Default center colors - darker brown for throat, spleen, solarplexus, and root, darker red for sacral
  default_center_colors = ['#F9F6C4', '#48BB78', '#3D2A1F', '#3D2A1F', '#F56565', '#F9F6C4', '#3D2A1F', '#B71C1C', '#3D2A1F']
  center_colors = local_assigns[:center_colors] || default_center_colors

  # Default center opacity
  center_opacity = local_assigns[:center_opacity] || 0.9
%>
<div class="bodygraph-renderer relative" style="width: <%= width %>px; height: <%= height %>px;">
  <object class="absolute" style="left: -70px; top: 0; opacity: <%= bg_opacity %>;" id="bodygraph-bg-<%= id %>" type="image/svg+xml" data="<%= image_url('bodygraph-bg.svg') %>" width="<%= width * 3.7 %>px"></object>
  <object class="absolute" style="left: 0; top: 0;" id="bodygraph-<%= id %>" type="image/svg+xml" data="<%= image_url('bodygraph-blank.svg') %>" width="<%= width %>px"></object>
</div>
<% if show_metadata && metadata.present? %>
  <div class="mt-4 grid grid-cols-2 gap-4 w-full">
    <% if metadata[:aura_type].present? %>
      <div>
        <dt class="text-sm font-medium text-gray-500">Type</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= metadata[:aura_type] %></dd>
      </div>
    <% end %>
    <% if metadata[:inner_authority].present? %>
      <div>
        <dt class="text-sm font-medium text-gray-500">Authority</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= metadata[:inner_authority] %></dd>
      </div>
    <% end %>
    <% if metadata[:profile].present? %>
      <div>
        <dt class="text-sm font-medium text-gray-500">Profile</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= metadata[:profile] %></dd>
      </div>
    <% end %>
    <% if metadata[:definition].present? %>
      <div>
        <dt class="text-sm font-medium text-gray-500">Definition</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= metadata[:definition] %></dd>
      </div>
    <% end %>
    <% if metadata[:incarnation_cross].present? %>
      <div class="col-span-2">
        <dt class="text-sm font-medium text-gray-500">Incarnation Cross</dt>
        <dd class="mt-1 text-sm text-gray-900"><%= metadata[:incarnation_cross] %></dd>
      </div>
    <% end %>
  </div>
<% end %>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const bodygraphId = '<%= id %>';
    const bodygraphObj = document.getElementById(`bodygraph-${bodygraphId}`);
    const displayMode = '<%= mode %>';

    // Parse the activation data
    const personalityActivations = <%= raw personality_activations_json %>;
    const designActivations = <%= raw design_activations_json %>;

    // Prepare data structures for rendering
    const activatedGates = {};
    const personalityGates = {};
    const designGates = {};
    const allActivations = [];

    // Process personality activations
    Object.keys(personalityActivations).forEach(planet => {
      const gate = personalityActivations[planet].g;
      const line = personalityActivations[planet].l;
      activatedGates[gate] = true;
      personalityGates[gate] = true;

      allActivations.push({
        planet: planet,
        g: gate,
        l: line,
        personalityOrDesign: 'personality'
      });
    });

    // Process design activations
    Object.keys(designActivations).forEach(planet => {
      const gate = designActivations[planet].g;
      const line = designActivations[planet].l;
      activatedGates[gate] = true;
      designGates[gate] = true;

      allActivations.push({
        planet: planet,
        g: gate,
        l: line,
        personalityOrDesign: 'design'
      });
    });

    // Define centers and their channels
    const centersMapping = [
      [64, 61, 'Head', 'Ajna'],
      [61, 24, 'Ajna', 'Throat'],
      [63, 4, 'Head', 'Ajna'],
      [4, 63, 'Ajna', 'Head'],
      [17, 62, 'Ajna', 'Throat'],
      [62, 17, 'Throat', 'Ajna'],
      [47, 64, 'Head', 'Ajna'],
      [64, 47, 'Ajna', 'Head'],
      [11, 56, 'Ajna', 'Throat'],
      [56, 11, 'Throat', 'Ajna'],
      [43, 23, 'Ajna', 'Throat'],
      [23, 43, 'Throat', 'Ajna'],
      [1, 8, 'G', 'Throat'],
      [8, 1, 'Throat', 'G'],
      [20, 57, 'Throat', 'Spleen'],
      [57, 20, 'Spleen', 'Throat'],
      [16, 48, 'Throat', 'Spleen'],
      [48, 16, 'Spleen', 'Throat'],
      [35, 36, 'Throat', 'SolarPlexus'],
      [36, 35, 'SolarPlexus', 'Throat'],
      [12, 22, 'Throat', 'SolarPlexus'],
      [22, 12, 'SolarPlexus', 'Throat'],
      [45, 21, 'Throat', 'Ego'],
      [21, 45, 'Ego', 'Throat'],
      [31, 7, 'Throat', 'G'],
      [7, 31, 'G', 'Throat'],
      [33, 13, 'Throat', 'G'],
      [13, 33, 'G', 'Throat'],
      [10, 20, 'G', 'Throat'],
      [20, 10, 'Throat', 'G'],
      [10, 57, 'G', 'Spleen'],
      [57, 10, 'Spleen', 'G'],
      [10, 34, 'G', 'Sacral'],
      [34, 10, 'Sacral', 'G'],
      [15, 5, 'G', 'Sacral'],
      [5, 15, 'Sacral', 'G'],
      [2, 14, 'G', 'Sacral'],
      [14, 2, 'Sacral', 'G'],
      [29, 46, 'Sacral', 'G'],
      [46, 29, 'G', 'Sacral'],
      [39, 55, 'Ego', 'Throat'],
      [55, 39, 'Throat', 'Ego'],
      [26, 44, 'Ego', 'Spleen'],
      [44, 26, 'Spleen', 'Ego'],
      [40, 37, 'Ego', 'SolarPlexus'],
      [37, 40, 'SolarPlexus', 'Ego'],
      [51, 25, 'Ego', 'G'],
      [25, 51, 'G', 'Ego'],
      [32, 54, 'Spleen', 'Root'],
      [54, 32, 'Root', 'Spleen'],
      [28, 38, 'Spleen', 'Root'],
      [38, 28, 'Root', 'Spleen'],
      [18, 58, 'Spleen', 'Root'],
      [58, 18, 'Root', 'Spleen'],
      [50, 27, 'Spleen', 'Sacral'],
      [27, 50, 'Sacral', 'Spleen'],
      [6, 59, 'SolarPlexus', 'Sacral'],
      [59, 6, 'Sacral', 'SolarPlexus'],
      [52, 9, 'Root', 'Sacral'],
      [9, 52, 'Sacral', 'Root'],
      [53, 42, 'Root', 'Sacral'],
      [42, 53, 'Sacral', 'Root'],
      [60, 3, 'Root', 'Sacral'],
      [3, 60, 'Sacral', 'Root'],
      [19, 49, 'Root', 'SolarPlexus'],
      [49, 19, 'SolarPlexus', 'Root'],
      [41, 30, 'Root', 'SolarPlexus'],
      [30, 41, 'SolarPlexus', 'Root']
    ];

    bodygraphObj.addEventListener('load', function() {
      const svgDoc = this.contentDocument;
      if (!svgDoc) return;

      // Create defs if it doesn't exist
      const SVG_NS = 'http://www.w3.org/2000/svg';
      let defs = svgDoc.querySelector('defs');
      if (!defs) {
        defs = svgDoc.createElementNS(SVG_NS, 'defs');
        svgDoc.querySelector('svg').appendChild(defs);
      }

      // Create gradients for centers
      const centers = ['Head', 'Ajna', 'Throat', 'Spleen', 'Ego', 'G', 'SolarPlexus', 'Sacral', 'Root'];
      const colors = <%= raw center_colors.to_json %>;

      centers.forEach((center, index) => {
        const centerElement = svgDoc.getElementById(center);
        if (!centerElement) return;

        // Create a linear gradient element
        const gradient = svgDoc.createElementNS(SVG_NS, 'linearGradient');
        gradient.setAttribute('id', `${center.toLowerCase()}Gradient`);
        gradient.setAttribute('x1', '0%');
        gradient.setAttribute('y1', '0%');
        gradient.setAttribute('x2', '0%');
        gradient.setAttribute('y2', '140%');

        // Create the gradient stops
        const stop1 = svgDoc.createElementNS(SVG_NS, 'stop');
        stop1.setAttribute('offset', '0%');
        stop1.setAttribute('stop-color', colors[index]);
        stop1.setAttribute('stop-opacity', '<%= center_opacity %>');

        const stop2 = svgDoc.createElementNS(SVG_NS, 'stop');
        stop2.setAttribute('offset', '100%');
        stop2.setAttribute('stop-color', colors[index]);
        stop2.setAttribute('stop-opacity', '<%= center_opacity / 3 %>');

        // Add the stops to the gradient
        gradient.appendChild(stop1);
        gradient.appendChild(stop2);

        // Add the gradient to the defs
        defs.appendChild(gradient);
      });

      // Create gradients for channels
      const gradientForHorizontalChannels = svgDoc.createElementNS(SVG_NS, 'linearGradient');
      gradientForHorizontalChannels.setAttribute('id', 'gradientForHorizontalChannels');
      gradientForHorizontalChannels.setAttribute('x1', '0%');
      gradientForHorizontalChannels.setAttribute('y1', '0%');
      gradientForHorizontalChannels.setAttribute('x2', '100%');
      gradientForHorizontalChannels.setAttribute('y2', '0%');

      const stop1 = svgDoc.createElementNS(SVG_NS, 'stop');
      stop1.setAttribute('offset', '0%');
      stop1.setAttribute('stop-color', 'black');

      const stop2 = svgDoc.createElementNS(SVG_NS, 'stop');
      stop2.setAttribute('offset', '100%');
      stop2.setAttribute('stop-color', '#A44344');

      gradientForHorizontalChannels.appendChild(stop1);
      gradientForHorizontalChannels.appendChild(stop2);
      defs.appendChild(gradientForHorizontalChannels);

      // Create additional gradients for special channels
      function createGradient(id, x1, y1, x2, y2) {
        const gradient = svgDoc.createElementNS(SVG_NS, "linearGradient");
        gradient.setAttribute("id", id);
        gradient.setAttribute("x1", x1);
        gradient.setAttribute("y1", y1);
        gradient.setAttribute("x2", x2);
        gradient.setAttribute("y2", y2);

        const stop1 = svgDoc.createElementNS(SVG_NS, "stop");
        stop1.setAttribute("offset", "50%");
        stop1.setAttribute("style", "stop-color: #A44344; stop-opacity: 1");

        const stop2 = svgDoc.createElementNS(SVG_NS, "stop");
        stop2.setAttribute("offset", "50%");
        stop2.setAttribute("style", "stop-color: black; stop-opacity: 1");

        gradient.appendChild(stop1);
        gradient.appendChild(stop2);

        defs.appendChild(gradient);
      }

      // Add special gradients for complex channel rendering
      createGradient("gradientForVerticalChannels", "0%", "0%", "100%", "0%");
      createGradient("gradientForDiagonalChannels", "0%", "0%", "100%", "100%");
      createGradient("gradientForSpleenRootChannels", "100%", "0%", "4%", "100%");
      createGradient("gradientForSolarPlexusRootChannels", "4%", "0%", "100%", "100%");
      createGradient("gradientFor59_6", "9%", "25%", "50%", "100%");
      createGradient("gradientFor50_27", "50%", "0%", "9%", "75%");
      createGradient("gradientFor25_51", "100%", "0%", "4%", "100%");
      createGradient("gradientForGate34", "100%", "0%", "4%", "100%");
      createGradient("gradientForGate10Connect", "100%", "90%", "0%", "0%");

      // Determine which activations to show based on mode
      let activationsToShow, activatedGatesToShow;
      if (displayMode === 'all') {
        activationsToShow = allActivations;
        activatedGatesToShow = activatedGates;
      } else if (displayMode === 'design') {
        activationsToShow = allActivations.filter(a => a.personalityOrDesign === 'design');
        activatedGatesToShow = designGates;
      } else if (displayMode === 'personality') {
        activationsToShow = allActivations.filter(a => a.personalityOrDesign === 'personality');
        activatedGatesToShow = personalityGates;
      } else if (displayMode === 'incarnation-cross') {
        // Only show Sun and Earth for both personality and design
        const sunEarthActivations = [];
        if (personalityActivations['Sun']) sunEarthActivations.push({...personalityActivations['Sun'], personalityOrDesign: 'personality', planet: 'Sun'});
        if (personalityActivations['Earth']) sunEarthActivations.push({...personalityActivations['Earth'], personalityOrDesign: 'personality', planet: 'Earth'});
        if (designActivations['Sun']) sunEarthActivations.push({...designActivations['Sun'], personalityOrDesign: 'design', planet: 'Sun'});
        if (designActivations['Earth']) sunEarthActivations.push({...designActivations['Earth'], personalityOrDesign: 'design', planet: 'Earth'});

        activationsToShow = sunEarthActivations;
        activatedGatesToShow = {};
        sunEarthActivations.forEach(a => activatedGatesToShow[a.g] = true);
      }

      // Activate gates
      activationsToShow.forEach((activation) => {
        const gate = activation.g;
        const hangingGate = svgDoc.getElementById(`Gate${gate}`);
        const gateText = svgDoc.getElementById(`GateText${gate}`);
        const gateBackground = svgDoc.getElementById(`GateTextBg${gate}`);

        if (hangingGate && gateText && gateBackground) {
          let color = '';
          if (displayMode === 'all' && personalityGates[gate] && designGates[gate]) {
            // Both personality and design activate this gate
            if (gate == 10) {
              color = 'url(#gradientForHorizontalChannels)';
            } else if ([50, 27].includes(parseInt(gate))) {
              color = 'url(#gradientFor50_27)';
            } else if ([6, 59].includes(parseInt(gate))) {
              color = 'url(#gradientFor59_6)';
            } else if ([16, 48, 57, 20].includes(parseInt(gate))) {
              color = 'url(#gradientForGate10Connect)';
            } else if ([32, 54, 28, 38, 58, 18].includes(parseInt(gate))) {
              color = 'url(#gradientForSpleenRootChannels)';
            } else if ([34].includes(parseInt(gate))) {
              color = 'url(#gradientForGate34)';
            } else if ([25, 51].includes(parseInt(gate))) {
              color = 'url(#gradientFor25_51)';
            } else if ([44, 26, 45, 21, 12, 22, 35, 36, 37, 40].includes(parseInt(gate))) {
              color = 'url(#gradientForDiagonalChannels)';
            } else if ([19, 49, 39, 55, 41, 30].includes(parseInt(gate))) {
              color = 'url(#gradientForSolarPlexusRootChannels)';
            } else {
              color = 'url(#gradientForVerticalChannels)';
            }
          } else if (personalityGates[gate] && (displayMode === 'all' || displayMode === 'personality' ||
                    (displayMode === 'incarnation-cross' && ['Sun', 'Earth'].includes(activation.planet)))) {
            color = 'black';
          } else if (designGates[gate] && (displayMode === 'all' || displayMode === 'design' ||
                    (displayMode === 'incarnation-cross' && ['Sun', 'Earth'].includes(activation.planet)))) {
            color = '#A44344';
          }

          hangingGate.style.fill = color;
          gateText.style.fill = '#343434';

          const gateBackgroundPath = gateBackground.querySelector('path');
          const gateBackgroundCircle = gateBackground.querySelector('circle');
          if (gateBackgroundPath) gateBackgroundPath.style.fill = '#EFEFEF';
          if (gateBackgroundCircle) gateBackgroundCircle.style.fill = '#EFEFEF';
        }
      });

      // Activate centers based on channels
      centersMapping.forEach(mapping => {
        const [gate1, gate2, center1Id, center2Id] = mapping;
        if (activatedGatesToShow[gate1] && activatedGatesToShow[gate2]) {
          const center1 = svgDoc.getElementById(center1Id);
          const center2 = svgDoc.getElementById(center2Id);

          if (center1 && center2) {
            const center1Path = center1.querySelector('path');
            const center2Path = center2.querySelector('path');

            if (center1Path) center1Path.style.fill = `url(#${center1Id.toLowerCase()}Gradient)`;
            if (center2Path) center2Path.style.fill = `url(#${center2Id.toLowerCase()}Gradient)`;
          }
        }
      });

      // Special case for GateSpan
      if ((activatedGatesToShow[34] && activatedGatesToShow[20]) ||
          (activatedGatesToShow[34] && activatedGatesToShow[10]) ||
          (activatedGatesToShow[57] && activatedGatesToShow[20])) {
        const gateSpan = svgDoc.getElementById('GateSpan');
        const gateConnect10 = svgDoc.getElementById('GateConnect10');
        const gateConnect34 = svgDoc.getElementById('GateConnect34');

        if (gateSpan) gateSpan.style.fill = displayMode === 'design' ? '#A44344' : 'black';
        if (gateConnect10) gateConnect10.style.fill = displayMode === 'design' ? '#A44344' : 'black';
        if (gateConnect34) gateConnect34.style.fill = displayMode === 'design' ? '#A44344' : 'black';
      }
    });
  });
</script>
