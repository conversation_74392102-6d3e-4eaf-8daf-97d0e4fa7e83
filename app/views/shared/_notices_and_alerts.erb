<% if notice.present? || alert.present? %>
  <div id="notification-container" class="absolute top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md flex flex-col items-center gap-2">
    <% if notice.present? %>
      <div id="notice" class="flash-message w-full py-3 px-4 bg-black/75 backdrop-blur rounded-lg shadow-lg">
        <p class="text-green-400 font-medium text-center"><%= notice %></p>
      </div>
    <% end %>
    <% if alert.present? %>
      <div id="alert" class="flash-message w-full py-3 px-4 bg-black/75 backdrop-blur rounded-lg shadow-lg">
        <p class="text-red-400 font-medium text-center"><%= alert %></p>
      </div>
    <% end %>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const notifications = document.querySelectorAll('.flash-message');

      notifications.forEach(notification => {
        // Fade out and slide up after 3 seconds
        setTimeout(() => {
          notification.style.transition = 'all 0.5s ease-in-out';
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-100%)';

          // Remove from DOM after animation
          setTimeout(() => {
            notification.remove();

            // Remove container if no more notifications
            const container = document.getElementById('notification-container');
            if (container && !container.querySelector('.flash-message')) {
              container.remove();
            }
          }, 500);
        }, 3000);
      });
    });
  </script>
<% end %>
