<% unless user_signed_in? %>
  <meta name="csrf-token" content="<%= form_authenticity_token %>">
  
  <!-- Simple Google Sign-In button -->
  <div style="position: fixed; top: 10px; right: 10px; z-index: 1000;">
    <div id="g_id_onload"
         data-client_id="<%= ENV['GOOGLE_CLIENT_ID'] %>"
         data-context="signin"
         data-ux_mode="popup"
         data-callback="handleGoogleSignInResponse"
         data-auto_prompt="true">
    </div>
    <div id="g_id_signin" class="g_id_signin"
         data-type="standard"
         data-size="large"
         data-theme="outline"
         data-text="sign_in_with"
         data-shape="rectangular"
         data-logo_alignment="left">
    </div>
  </div>
  
  <script>
    // Initialize Google Sign-In when the page loads
    window.onload = function() {
      console.log("Window loaded, initializing Google Sign-In on localhost:3000...");
      console.log("Current origin: " + window.location.origin);
    };
    
    // Callback function for Google Sign-In
    function handleGoogleSignInResponse(response) {
      console.log("Google Sign-In Response received");
      
      if (!response || !response.credential) {
        console.error("Invalid response from Google Sign-In");
        return;
      }

      // Get CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
      console.log("CSRF Token present:", !!csrfToken);

      // Send the credential to the server
      console.log("Sending credential to server...");
      fetch("/auth/google_login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken
        },
        body: JSON.stringify({ token: response.credential })
      })
      .then(response => {
        console.log("Response status:", response.status);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        console.log("Backend response:", data);
        if (data.success) {
          console.log("Login successful, redirecting to dashboard...");
          window.location.href = data.redirect_url || "/dashboard";
        } else {
          console.error("Login failed:", data.error);
          alert("Login failed: " + (data.error || "Unknown error"));
        }
      })
      .catch(error => {
        console.error("Error during authentication:", error);
        alert("Authentication error: " + error.message);
      });
    }
  </script>
<% end %>

<div class="bg-gray-900 py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl text-center">
      <h2 class="text-base font-semibold leading-7 text-indigo-400">Development Directory</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">Choose a domain to visit</p>
      <% if user_signed_in? %>
        <p class="mt-2 text-xl text-white">Signed in as: <%= current_user.email %></p>
        <p class="mt-2">
          <%= button_to "Sign out", destroy_user_session_path, method: :delete, class: "text-indigo-400 hover:text-indigo-300 bg-transparent border-none cursor-pointer" %>
        </p>
      <% else %>
        <p class="mt-2 text-xl text-white">Not signed in</p>
        <p class="mt-2">
          <%= link_to "Sign in", new_user_session_path, class: "text-indigo-400 hover:text-indigo-300" %>
        </p>
      <% end %>
    </div>
    <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
      <div class="flex gap-x-4 rounded-xl bg-white/5 p-6 ring-1 ring-inset ring-white/10">
        <div class="text-base leading-7">
          <h3 class="font-semibold text-white">Signpost</h3>
          <p class="mt-2 text-gray-300">The only tool for Human Design professionals</p>
          <p class="mt-4">
            <%= link_to "Visit Signpost →", "http://signposthd.localhost:3000", class: "text-sm font-semibold leading-6 text-indigo-400" %>
          </p>
        </div>
      </div>
      <div class="flex gap-x-4 rounded-xl bg-white/5 p-6 ring-1 ring-inset ring-white/10">
        <div class="text-base leading-7">
          <h3 class="font-semibold text-white">High Desert Human Design</h3>
          <p class="mt-2 text-gray-300">Annual Human Design Conference</p>
          <p class="mt-4">
            <%= link_to "Visit HDHD →", "http://highdeserthumandesign.localhost:3000", class: "text-sm font-semibold leading-6 text-indigo-400" %>
          </p>
        </div>
      </div>
      <div class="flex gap-x-4 rounded-xl bg-white/5 p-6 ring-1 ring-inset ring-white/10">
        <div class="text-base leading-7">
          <h3 class="font-semibold text-white">Center for Human Design</h3>
          <p class="mt-2 text-gray-300">Center for Human Design Organization</p>
          <p class="mt-4">
            <%= link_to "Visit Center for Human Design →", "http://centerforhumandesign.localhost:3000", class: "text-sm font-semibold leading-6 text-indigo-400" %>
          </p>
        </div>
      </div>
      <div class="flex gap-x-4 rounded-xl bg-white/5 p-6 ring-1 ring-inset ring-white/10">
        <div class="text-base leading-7">
          <h3 class="font-semibold text-white">Santa Fe Human Design Library</h3>
          <p class="mt-2 text-gray-300">Santa Fe Human Design Library</p>
          <p class="mt-4">
            <%= link_to "Visit SFHDL →", "http://sfhdl.localhost:3000", class: "text-sm font-semibold leading-6 text-indigo-400" %>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
<%= debug @user %>
<% if @user.avatar.attached? %>
  <%= image_tag @user.avatar, class: "size-8 rounded-full bg-gray-50" %>
<% end %>
