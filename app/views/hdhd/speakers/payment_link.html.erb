<div class="bg-white py-10">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:mx-0 mb-8">
      <h1 class="text-3xl font-bold tracking-tight text-gray-900">Create Payment Link</h1>
      <p class="mt-2 text-lg leading-8 text-gray-600">Generate a payment link for your clients to pay you directly.</p>
    </div>
    <% unless @speaker.stripe_connected? %>
      <div class="rounded-md bg-yellow-50 p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Stripe account not connected</h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>Please connect your Stripe account first to create payment links. <a href="<%= edit_speaker_path(@speaker) %>" class="font-medium text-yellow-700 underline hover:text-yellow-600">Go back to your profile</a> to connect your Stripe account.</p>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2">
        <div class="px-4 py-6 sm:p-8">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Payment Details</h2>
          <p class="mt-1 text-sm leading-6 text-gray-600">Create a payment link for <%= @speaker.full_name %>.</p>
          <%= form_tag create_payment_link_hdhd_speaker_path(@speaker), method: :post, class: "mt-6 max-w-md" do %>
            <div class="space-y-6">
              <div>
                <label for="amount" class="block text-sm font-medium leading-6 text-gray-900">Amount (USD)</label>
                <div class="relative mt-2 rounded-md shadow-sm">
                  <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <span class="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <%= number_field_tag :amount, nil, step: '0.01', class: "block w-full rounded-md border-0 py-1.5 pl-7 pr-12 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6", placeholder: "0.00", required: true %>
                  <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                    <span class="text-gray-500 sm:text-sm">USD</span>
                  </div>
                </div>
              </div>
              <div>
                <label for="description" class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                <div class="mt-2">
                  <%= text_field_tag :description, nil, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6", placeholder: "e.g. Coaching Session with #{@speaker.full_name}" %>
                </div>
                <p class="mt-2 text-sm text-gray-500">This will appear on the payment page and receipt.</p>
              </div>
              <div class="flex items-center justify-end">
                <%= link_to "Cancel", edit_speaker_path(@speaker), class: "text-sm font-semibold leading-6 text-gray-900 mr-4" %>
                <%= submit_tag "Generate Payment Link", class: "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      <% if @payment_link %>
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2 mt-8">
          <div class="px-4 py-6 sm:p-8">
            <h2 class="text-base font-semibold leading-7 text-gray-900">Your Payment Link</h2>
            <p class="mt-1 text-sm leading-6 text-gray-600">Share this link with your client to receive payment.</p>
            <div class="mt-6">
              <div class="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600">
                <input type="text" id="payment-link" value="<%= @payment_link.url %>" class="block flex-1 border-0 bg-transparent py-1.5 pl-3 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6" readonly>
                <button type="button" onclick="copyToClipboard()" class="inline-flex items-center rounded-r-md border border-l-0 border-gray-300 px-3 sm:text-sm bg-gray-50 text-gray-500 hover:bg-gray-100">
                  <svg class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                  Copy
                </button>
              </div>
              <p class="mt-2 text-sm text-gray-500" id="copy-message"></p>
            </div>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
<script>
  function copyToClipboard() {
    const paymentLink = document.getElementById('payment-link');
    paymentLink.select();
    navigator.clipboard.writeText(paymentLink.value).then(() => {
      const message = document.getElementById('copy-message');
      message.textContent = 'Link copied to clipboard!';
      message.classList.add('text-green-600');
      setTimeout(() => {
        message.textContent = '';
      }, 3000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
      const message = document.getElementById('copy-message');
      message.textContent = 'Failed to copy. Please try selecting and copying manually.';
      message.classList.add('text-red-600');
    });
  }
</script>