<div id="password-protection" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50">
  <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
    <div class="flex flex-col items-center mb-6">
      <% if @speaker.profile_picture.attached? %>
        <%= image_tag @speaker.profile_picture, class: "h-20 w-20 rounded-full object-cover mb-4" %>
      <% else %>
        <div class="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center mb-4">
          <svg class="h-10 w-10 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        </div>
      <% end %>
      <h2 class="text-2xl font-bold mb-2 text-gray-900"><%= @speaker.full_name %>'s Protected Content</h2>
    </div>
    <p class="mb-6 text-gray-600 text-center">This page is password protected. Please enter the password provided by <%= @speaker.full_name %> to access this content.</p>
    <div class="mb-4">
      <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
      <input type="password" id="password-input" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
    </div>
    <div class="flex justify-end">
      <button id="submit-password" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Submit
      </button>
    </div>
    <div id="password-error" class="mt-4 text-red-600 hidden">
      Incorrect password. Please try again.
    </div>
  </div>
</div>
<div id="content" class="hidden bg-white min-h-screen">
  <div class="container mx-auto py-10 px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl bg-white rounded-xl shadow-sm p-8">
      <div class="flex flex-col sm:flex-row items-center justify-center mb-8 border-b border-gray-100 pb-8">
        <% if @speaker.profile_picture.attached? %>
          <%= image_tag @speaker.profile_picture, class: "h-28 w-28 rounded-full object-cover mb-4 sm:mb-0 sm:mr-6 ring-4 ring-indigo-50" %>
        <% else %>
          <div class="h-28 w-28 rounded-full bg-gray-200 flex items-center justify-center mb-4 sm:mb-0 sm:mr-6 ring-4 ring-indigo-50">
            <svg class="h-14 w-14 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
        <% end %>
        <div class="text-center sm:text-left sm:ml-6">
          <h1 class="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900"><%= @speaker.full_name %></h1>
          <p class="text-lg text-indigo-600 mt-1">Friends & Family Special Offer</p>
        </div>
      </div>
      <div class="bg-gradient-to-br from-indigo-50 to-white rounded-xl p-8 mb-8 shadow-sm">
        <div class="text-center">
          <h2 class="text-2xl font-semibold text-gray-900 mb-4">Special 10% Discount</h2>
          <p class="text-lg text-gray-700 mb-6">
            <%= @speaker.full_name %> is offering a special 10% discount to friends and family for HDHD <%= Date.today.year %>.
          </p>
          <div class="flex flex-col items-center justify-center mb-6 bg-white rounded-lg p-6 shadow-sm max-w-xs mx-auto">
            <div class="flex items-center justify-center w-full mb-4">
              <span class="text-lg text-gray-500 line-through mr-3">$497</span>
              <span class="text-3xl font-bold text-indigo-600">$447.30</span>
            </div>
            <span class="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
              <svg class="-ml-1 mr-1.5 h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              Save $49.70
            </span>
          </div>
          <% if @speaker.stripe_connected? %>
            <div class="mt-8">
              <a href="#" id="checkout-button" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Reserve Your Spot Now
              </a>
              <p class="text-xs text-gray-500 mt-3">By purchasing, you agree to our <a href="/terms-and-conditions" class="text-indigo-600 hover:text-indigo-800">Terms</a> and <a href="/hdhd-conference-attendee-agreement" class="text-indigo-600 hover:text-indigo-800">Attendee Agreement</a></p>
            </div>
            <script src="https://js.stripe.com/v3/"></script>
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                <% if @speaker.stripe_publishable_key.present? %>
                  // Initialize Stripe with the connected account's publishable key
              const stripe = Stripe('<%= @speaker.stripe_publishable_key %>', {
                stripeAccount: '<%= @speaker.stripe_user_id %>'
              });

              const checkoutButton = document.getElementById('checkout-button');

              checkoutButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Show loading indicator
                checkoutButton.disabled = true;
                checkoutButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Processing...';

                // Create a checkout session for the discounted price
                fetch('/hdhd/stripe/payment/create-checkout-session', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                  },
                  body: JSON.stringify({
                    price_amount: 44730, // in cents
                    speaker_id: <%= @speaker.id %>,
                    description: 'HDHD <%= Date.today.year %> Conference - Friends & Family Discount'
                  })
                })
                .then(function(response) {
                  if (!response.ok) {
                    throw new Error('Network response was not ok');
                  }
                  return response.json();
                })
                .then(function(session) {
                  console.log('Session created:', session);
                  return stripe.redirectToCheckout({ sessionId: session.id });
                })
                .then(function(result) {
                  if (result.error) {
                    throw new Error(result.error.message);
                  }
                })
                .catch(function(error) {
                  console.error('Error:', error);
                  alert('There was an error processing your payment: ' + error.message);
                  // Reset button
                  checkoutButton.disabled = false;
                  checkoutButton.innerHTML = '<svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" /></svg> Reserve Your Spot Now';
                });
              });
                          <% else %>
              console.error('Stripe publishable key is missing');
              const checkoutButton = document.getElementById('checkout-button');
              if (checkoutButton) {
                checkoutButton.addEventListener('click', function(e) {
                  e.preventDefault();
                  alert('Payment processing is currently unavailable. Please try again later.');
                });
              }
                          <% end %>
                        });
            </script>
          <% else %>
            <div class="mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-100 shadow-sm">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-yellow-700 font-medium">Payment option not available</p>
                  <p class="text-yellow-600 text-sm mt-1">Please contact <%= @speaker.full_name %> directly for registration assistance.</p>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-500">This special offer is exclusively for friends and family of <%= @speaker.full_name %>.</p>
        <p class="text-sm text-gray-500 mt-2">For questions, please contact <%= @speaker.full_name %> directly.</p>
      </div>
    </div>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const correctPassword = '<%= @speaker.friends_and_family_password %>';
        const passwordProtection = document.getElementById('password-protection');
        const content = document.getElementById('content');
        const passwordInput = document.getElementById('password-input');
        const submitButton = document.getElementById('submit-password');
        const passwordError = document.getElementById('password-error');

        // Check if password is stored in session storage
        const storedPassword = sessionStorage.getItem('ff_password_<%= @speaker.id %>');
        if (storedPassword === correctPassword) {
          passwordProtection.classList.add('hidden');
          content.classList.remove('hidden');
        }

        // Handle password submission
        submitButton.addEventListener('click', checkPassword);
        passwordInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            checkPassword();
          }
        });

        function checkPassword() {
          const enteredPassword = passwordInput.value;

          if (enteredPassword === correctPassword) {
            // Store password in session storage
            sessionStorage.setItem('ff_password_<%= @speaker.id %>', enteredPassword);

            // Show content
            passwordProtection.classList.add('hidden');
            content.classList.remove('hidden');
            passwordError.classList.add('hidden');
          } else {
            // Show error
            passwordError.classList.remove('hidden');
            passwordInput.value = '';
          }
        }
      });
    </script>
