<% content_for :page_title do %>
  <span class="text-gray-600">Edit Speaker Profile</span>
<% end %>
<p class="text-lg leading-8 text-gray-600 mb-6">Update your information for the HDHD conference.</p>
<form action="<%= speaker_path(@speaker) %>" method="post" enctype="multipart/form-data" class="space-y-8">
  <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
  <%= hidden_field_tag "_method", "patch" %>
  <% f = ActionView::Helpers::FormBuilder.new(:hdhd_speaker, @speaker, self, {}) %>
  <% if @speaker.errors.any? %>
    <div class="rounded-md bg-red-50 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">There were <%= pluralize(@speaker.errors.count, "error") %> with your submission</h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% @speaker.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2">
    <div class="px-4 py-6 sm:p-8">
      <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div class="sm:col-span-4">
          <%= f.label :full_name, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_field :full_name, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>
        <div class="sm:col-span-4">
          <%= f.label :email, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.email_field :email, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>
        <div class="col-span-full">
          <%= f.label :bio, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_area :bio, rows: 6, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            <p class="mt-2 text-sm text-gray-500">Write a few sentences about yourself. This will be displayed on your speaker profile.</p>
          </div>
        </div>
        <div class="sm:col-span-3">
          <%= f.label :birth_date_local, "Birth Date", class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.datetime_field :birth_date_local, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            <p class="mt-2 text-sm text-gray-500">Your local birth date and time for Human Design calculations.</p>
          </div>
        </div>
        <div class="sm:col-span-3">
          <%= f.label :birth_location, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_field :birth_location, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            <p class="mt-2 text-sm text-gray-500">City, State/Province, Country</p>
          </div>
        </div>
        <div class="sm:col-span-2">
          <%= f.label :aura_type, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.select :aura_type,
                  ["Generator", "Manifesting Generator", "Manifestor", "Projector", "Reflector"],
                  { include_blank: "Select Aura Type" },
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>
        <div class="sm:col-span-2">
          <%= f.label :inner_authority, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.select :inner_authority,
                  ["Emotional", "Sacral", "Splenic", "Ego", "G Center", "None/Outer", "Lunar"],
                  { include_blank: "Select Authority" },
                  class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>
        <div class="sm:col-span-2">
          <%= f.label :profile, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_field :profile, placeholder: "e.g. 1/3, 5/1", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
        </div>
        <div class="col-span-full">
          <%= f.label :websites, "Website URLs", class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_field :websites, placeholder: "https://example.com, https://another-site.com", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            <p class="mt-2 text-sm text-gray-500">Enter your website URLs, separated by commas.</p>
          </div>
        </div>
        <div class="col-span-full">
          <%= f.label :website_titles, class: "block text-sm font-medium leading-6 text-gray-900" %>
          <div class="mt-2">
            <%= f.text_field :website_titles, placeholder: "My Personal Site, My Company", class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            <p class="mt-2 text-sm text-gray-500">Enter titles for your websites, separated by commas. The order should match your URLs above.</p>
          </div>
        </div>
        <div class="col-span-full">
          <div class="flex items-center justify-between">
            <%= f.label :profile_picture, class: "block text-sm font-medium leading-6 text-gray-900" %>
            <% if @speaker.profile_picture.attached? %>
              <div class="text-xs text-gray-500">Current image attached</div>
            <% end %>
          </div>
          <div class="mt-2 flex items-center gap-x-3">
            <% if @speaker.profile_picture.attached? %>
              <%= image_tag @speaker.profile_picture, class: "h-12 w-12 rounded-full object-cover" %>
            <% else %>
              <svg class="h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18.685 19.097A9.723 9.723 0 0021.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 003.065 7.097A9.716 9.716 0 0012 21.75a9.716 9.716 0 006.685-2.653zm-12.54-1.285A7.486 7.486 0 0112 15a7.486 7.486 0 015.855 2.812A8.224 8.224 0 0112 20.25a8.224 8.224 0 01-5.855-2.438zM15.75 9a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" clip-rule="evenodd" />
              </svg>
            <% end %>
            <%= f.file_field :profile_picture, class: "rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" %>
          </div>
          <p class="mt-2 text-xs text-gray-500">JPG, PNG or GIF up to 10MB</p>
        </div>
        <div class="col-span-full">
          <div class="flex items-center justify-between">
            <%= f.label :company_logo, class: "block text-sm font-medium leading-6 text-gray-900" %>
            <% if @speaker.company_logo.attached? %>
              <div class="text-xs text-gray-500">Current logo attached</div>
            <% end %>
          </div>
          <div class="mt-2 flex items-center gap-x-3">
            <% if @speaker.company_logo.attached? %>
              <%= image_tag @speaker.company_logo, class: "max-h-12 max-w-[100px]" %>
            <% else %>
              <svg class="h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 01-.375.65 2.249 2.249 0 000 3.898.75.75 0 01.375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 17.625v-3.026a.75.75 0 01.374-.65 2.249 2.249 0 000-3.898.75.75 0 01-.374-.65V6.375zm15-1.125a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V6a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75zm-.75 3a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0v-.75a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0V18a.75.75 0 001.5 0v-.75zM6 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H6.75A.75.75 0 016 12zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clip-rule="evenodd" />
              </svg>
            <% end %>
            <%= f.file_field :company_logo, class: "rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" %>
          </div>
          <p class="mt-2 text-xs text-gray-500">JPG, PNG or GIF up to 10MB</p>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
      <%= link_to "Cancel", speaker_path(@speaker), class: "text-sm font-semibold leading-6 text-gray-900" %>
      <%= f.submit "Save", class: "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
    </div>
  </div>
  <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2 mt-8">
    <div class="px-4 py-6 sm:p-8">
      <h2 class="text-base font-semibold leading-7 text-gray-900">Friends & Family Ticket Sales</h2>
      <p class="mt-1 text-sm leading-6 text-gray-600">Track tickets sold through your Friends & Family page.</p>
      <%
        current_year = Date.today.year
        conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", current_year).first
        if conference
          tickets = @speaker.friends_and_family_tickets_for_year(current_year)
          tickets_count = tickets.count
      %>
      <div class="mt-6 overflow-hidden bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6 bg-gray-50">
          <h3 class="text-lg font-medium leading-6 text-gray-900">HDHD <%= current_year %> Ticket Sales</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">Tickets sold through your Friends & Family page.</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Total Tickets Sold</dt>
              <dd class="mt-1 text-3xl font-semibold text-gray-900"><%= tickets_count %></dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Fee Structure</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <span class="inline-flex items-center rounded-md bg-green-100 px-2.5 py-0.5 text-sm font-medium text-green-800">First 3 tickets: 100% to you</span>
                <span class="mt-1 inline-flex items-center rounded-md bg-blue-100 px-2.5 py-0.5 text-sm font-medium text-blue-800">Additional tickets: 50% split</span>
              </dd>
            </div>
          </dl>
          <% if tickets.any? %>
            <div class="mt-8">
              <h4 class="text-sm font-medium text-gray-500 mb-3">Recent Ticket Purchases</h4>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-300">
                  <thead>
                    <tr>
                      <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Date</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">User</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Price</th>
                      <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Platform Fee</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                    <% tickets.order(created_at: :desc).limit(5).each do |ticket| %>
                      <tr>
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0"><%= ticket.created_at.strftime("%b %-d, %Y") %></td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><%= ticket.user.email %></td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">$<%= sprintf("%.2f", ticket.price) %></td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                          <% if ticket.platform_fee_percentage.to_f > 0 %>
                            <%= ticket.platform_fee_percentage.to_i %>%
                          <% else %>
                            <span class="text-green-600">None</span>
                          <% end %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          <% else %>
            <div class="mt-6 text-center py-8 px-4 bg-gray-50 rounded-lg">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No tickets sold yet</h3>
              <p class="mt-1 text-sm text-gray-500">Share your Friends & Family page to start selling tickets.</p>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
<div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2 mt-8">
  <div class="px-4 py-6 sm:p-8">
    <h2 class="text-base font-semibold leading-7 text-gray-900">Stripe Integration</h2>
    <p class="mt-1 text-sm leading-6 text-gray-600">Connect your Stripe account to receive payments directly from clients.</p>
    <div class="mt-6">
      <% if @speaker.stripe_connected? %>
        <div class="rounded-md bg-green-50 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">Stripe account connected</h3>
              <div class="mt-2 text-sm text-green-700">
                <p>Your Stripe account is successfully connected. You can now create payment links and receive payments directly.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-6 border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-900">Friends & Family Page</h3>
          <p class="mt-1 text-sm text-gray-600">Share a 10% off coupon on your Friends & Family Page.</p>
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <label for="friends_and_family_password" class="block text-sm font-medium text-gray-700">Password</label>
              <button type="button" id="generate-password" class="text-xs text-indigo-600 hover:text-indigo-500">Generate New Password</button>
            </div>
            <div class="mt-1 relative rounded-md shadow-sm">
              <% if @speaker.friends_and_family_password.blank? %>
                <% @speaker.friends_and_family_password = @speaker.generate_friends_and_family_password %>
                <% # Save the generated password immediately if it was blank %>
                <% @speaker.save(validate: false) if @speaker.friends_and_family_password_changed? %>
              <% end %>
              <%= f.text_field :friends_and_family_password, class: "block w-full rounded-md border-gray-300 pr-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
              <div id="password-saved-message" class="text-green-600 text-xs mt-1 hidden">Password saved successfully!</div>
            </div>
            <p class="mt-2 text-xs text-gray-500">This password will be required to access your friends & family page.</p>
          </div>
        </div>
        <div class="mt-6 flex flex-col sm:flex-row gap-4">
          <%= link_to hdhd_speaker_friends_and_family_path(@speaker), class: "inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600", target: "_blank" do %>
            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
            </svg>
            View Friends & Family Page
          <% end %>
        </div>
        <!-- Link to unlink confirmation page -->
        <div class="mt-4">
          <%= link_to hdhd_speaker_stripe_unlink_confirmation_path(@speaker), class: "inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
            <svg class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM6.75 9.25a.75.75 0 000 1.5h6.5a.75.75 0 000-1.5h-6.5z" clip-rule="evenodd" />
            </svg>
            Unlink Stripe Account
          <% end %>
        </div>
      <% else %>
        <div class="rounded-md bg-blue-50 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">Connect your Stripe account</h3>
              <div class="mt-2 text-sm text-blue-700">
                <p>Connect your Stripe account to receive payments directly from clients. If you don't have a Stripe account yet, you'll be prompted to create one.</p>
              </div>
            </div>
          </div>
        </div>
        <%= link_to hdhd_speaker_stripe_oauth_path(@speaker), class: "inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 4.25A2.25 2.25 0 015.25 2h5.5A2.25 2.25 0 0113 4.25v2a.75.75 0 01-1.5 0v-2a.75.75 0 00-.75-.75h-5.5a.75.75 0 00-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 00.75-.75v-2a.75.75 0 011.5 0v2A2.25 2.25 0 0110.75 18h-5.5A2.25 2.25 0 013 15.75V4.25z" clip-rule="evenodd" />
            <path fill-rule="evenodd" d="M6 10a.75.75 0 01.75-.75h9.546l-1.048-.943a.75.75 0 111.004-1.114l2.5 2.25a.75.75 0 010 1.114l-2.5 2.25a.75.75 0 11-1.004-1.114l1.048-.943H6.75A.75.75 0 016 10z" clip-rule="evenodd" />
          </svg>
          Connect with Stripe
        <% end %>
      <% end %>
    </div>
  </div>
</div>
</form>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Set up password generation and saving
    const generatePasswordButton = document.getElementById('generate-password');
    const passwordInput = document.getElementById('hdhd_speaker_friends_and_family_password');
    const form = document.querySelector('form[action="<%= speaker_path(@speaker) %>"]');
    let lastSavedPassword = passwordInput ? passwordInput.value : '';

    // Function to save the password via AJAX
    function savePassword(newPassword) {
      // Don't save if the password hasn't changed
      if (newPassword === lastSavedPassword) {
        return Promise.resolve();
      }

      lastSavedPassword = newPassword;

      const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
      const formData = new FormData();
      formData.append('_method', 'patch');
      formData.append('hdhd_speaker[friends_and_family_password]', newPassword);

      return fetch(`/speakers/${<%= @speaker.id %>}`, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': csrfToken
        },
        body: formData
      })
      .then(response => {
        if (response.ok) {
          console.log('Password saved successfully');
          // Show success message
          const savedMessage = document.getElementById('password-saved-message');
          if (savedMessage) {
            savedMessage.classList.remove('hidden');
            // Hide the message after 3 seconds
            setTimeout(() => {
              savedMessage.classList.add('hidden');
            }, 3000);
          }
          return true;
        } else {
          console.error('Failed to save password');
          return false;
        }
      })
      .catch(error => {
        console.error('Error saving password:', error);
        return false;
      });
    }

    if (generatePasswordButton && passwordInput) {
      // Save password when focus leaves the input field (blur event)
      passwordInput.addEventListener('blur', function() {
        savePassword(passwordInput.value);
      });

      // Generate and save a new password when the button is clicked
      generatePasswordButton.addEventListener('click', function() {
        // List of words to choose from
        const wordList = ['acceptance', 'activity', 'arousing', 'art', 'alertness', 'aliveness', 'aloneness', 'ambition', 'army', 'assimilation', 'awareness', 'behavioral', 'beginnings', 'behavior', 'bonding', 'body', 'cauldron', 'caution', 'challenge', 'change', 'clarity', 'clinging', 'coming', 'completion', 'companionship', 'confusion', 'connection', 'conscience', 'conscientiousness',  'constellation', 'contemplation', 'contribution', 'continuity', 'core', 'correction', 'creativity', 'crisis', 'caring', 'deep', 'deliverance', 'depth', 'detail', 'determination', 'development', 'direction', 'doubt', 'driver', 'duration', 'egotist', 'emotional', 'emoting', 'empowered', 'empowering', 'empowerment', 'energy', 'enthusiasm', 'entire', 'essential', 'experience', 'extremes', 'fatefulness', 'feelings', 'feeling', 'fighter', 'fire', 'first', 'fixed', 'focus', 'form', 'formulization', 'fellowship', 'focused', 'fuel', 'friction', 'friendship', 'gatherer', 'gathering', 'gate', 'gentle', 'great', 'growth', 'guarding', 'hearer', 'humanity', 'hunter', 'huntress', 'ideas', 'idealism', 'ignition', 'implementation', 'impulse', 'inaction', 'incarnation', 'increase', 'innocence', 'insight', 'inspired', 'inspiration', 'intimacy', 'intuition', 'interaction', 'inner', 'joyous', 'journey', 'kali', 'keeping', 'leader', 'leading', 'learning', 'liberation', 'light', 'liars', 'life', 'living', 'logic', 'love', 'loyalty', 'luck', 'maiden', 'maintaining', 'manifestation', 'marrying', 'measure', 'mental', 'might', 'modesty', 'moment', 'mystery', 'mutation', 'now', 'nourishment', 'openness', 'opinions', 'opponent', 'opposition', 'ordering', 'organization', 'overcome', 'patience', 'pattern', 'peace', 'perseverance', 'personnel', 'possession', 'power', 'pressure', 'process', 'progress', 'prometheus', 'prophets', 'provocateur', 'provocation', 'pushing', 'quality', 'rationalizing', 'realization', 'realizing', 'receptive', 'recognition', 'rejection', 'releasing', 'relationship', 'repetition', 'resistance', 'resource', 'retreat', 'returning', 'revolution', 'rhythm', 'rhythms', 'rise', 'role', 'rooted', 'sacred', 'saying', 'secrets', 'self', 'sensitivity', 'sharing', 'shock', 'skills', 'small', 'social', 'solar', 'solitary', 'source', 'spirit', 'spiritual', 'stimulation', 'stillness', 'stubbornness', 'strength', 'storyteller', 'structure', 'taming', 'temple', 'transition', 'tribe', 'truth', 'unconditional', 'unique', 'unity', 'values', 'view', 'virtue', 'vitality', 'waiting', 'wanderer', 'wanting', 'warrior', 'well', 'wheel', 'will', 'wisdom', 'words', 'work', 'youthful', 'beat', 'alpha', 'concentration', 'awakening', 'exploration', 'perfected', 'form', 'curiosity', 'the', 'prodigal', 'the', 'wavelength', 'judgment', 'synthesis', 'channel', 'charisma', 'brainwave', 'structuring', 'initiation', 'surrender', 'preservation', 'struggle', 'discovery', 'transformation', 'transitoriness', 'community', 'maturation', 'abstraction'];

        // Select two random words
        const word1 = wordList[Math.floor(Math.random() * wordList.length)];
        let word2 = wordList[Math.floor(Math.random() * wordList.length)];

        // Make sure the words are different
        while (word1 === word2) {
          word2 = wordList[Math.floor(Math.random() * wordList.length)];
        }

        // Set the new password
        const newPassword = `${word1}-${word2}`;
        passwordInput.value = newPassword;

        // Save the new password
        savePassword(newPassword);
      });
    }
  });
</script>
