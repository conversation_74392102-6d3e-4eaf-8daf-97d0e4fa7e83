<div class="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
  <div class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
       style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)">
  </div>
</div>
<div class="bg-white py-24">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:mx-0">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 class="text-pretty text-4xl font-semibold tracking-tight text-gray-900 sm:text-5xl"><%= @speaker.full_name %></h2>
          <p class="mt-2 text-lg text-gray-600"><%= @speaker.role.humanize %></p>
        </div>
        <% if current_user && (@speaker.owned_by?(current_user) || current_user.email == "<EMAIL>") %>
          <%= link_to "Edit Profile", edit_speaker_path(@speaker), class: "mt-4 md:mt-0 inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
        <% end %>
      </div>
    </div>
    <div class="mx-auto mt-10 grid max-w-7xl grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-3">
      <!-- Left column: Profile picture and links -->
      <div class="lg:col-span-1">
        <div class="rounded-2xl bg-gray-50 p-6 shadow-sm">
          <div class="flex flex-col items-center">
            <% if @speaker.profile_picture.attached? %>
              <%= image_tag @speaker.profile_picture, class: "size-48 rounded-full object-cover", alt: @speaker.full_name %>
            <% else %>
              <div class="size-48 rounded-full bg-gray-200 flex items-center justify-center">
                <span class="text-gray-400 text-4xl"><%= @speaker.first_name[0] %><%= @speaker.last_name[0] %></span>
              </div>
            <% end %>
            <% if @speaker.websites.present? && @speaker.website_titles.present? %>
              <div class="mt-8 w-full">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Connect with <%= @speaker.first_name %></h3>
                <ul class="space-y-3">
                  <%
                    websites = @speaker.websites.split(',').map(&:strip)
                    titles = @speaker.website_titles.split(',').map(&:strip)

                    websites.zip(titles).each do |url, title|
                  %>
                  <li>
                    <a href="<%= url.start_with?('http') ? url : "https://#{url}" %>" target="_blank" rel="noopener noreferrer"
                         class="flex items-center text-indigo-600 hover:text-indigo-500">
                      <span class="mr-2">
                        <% if title.downcase.include?('twitter') || title.downcase.include?('x') %>
                          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                          </svg>
                        <% elsif title.downcase.include?('linkedin') %>
                          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z" clip-rule="evenodd" />
                          </svg>
                        <% elsif title.downcase.include?('instagram') %>
                          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
                          </svg>
                        <% elsif title.downcase.include?('facebook') %>
                          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
                          </svg>
                        <% elsif title.downcase.include?('youtube') %>
                          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd" />
                          </svg>
                        <% else %>
                          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                          </svg>
                        <% end %>
                      </span>
                      <%= title %>
                    </a>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
          <% if @speaker.conferences.any? %>
            <div class="mt-8 w-full">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Conferences</h3>
              <ul class="space-y-2">
                <% @speaker.conferences.order(start_date: :desc).each do |conference| %>
                  <li>
                    <a href="<%= conference_path(conference) %>" class="text-indigo-600 hover:text-indigo-500">
                      HDHD <%= conference.year %>
                    </a>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    <!-- Right column: Bio and other information -->
    <div class="lg:col-span-2">
      <div class="rounded-2xl bg-gray-50 p-6 shadow-sm">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">About</h3>
        <% if @speaker.bio.present? %>
          <div class="prose prose-indigo max-w-none">
            <%= simple_format(@speaker.bio) %>
          </div>
        <% else %>
          <p class="text-gray-500 italic">No bio available.</p>
        <% end %>
        <% if @speaker.aura_type.present? || @speaker.inner_authority.present? || @speaker.profile.present? %>
          <div class="mt-8 border-t border-gray-200 pt-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Human Design</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <% if @speaker.aura_type.present? %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Aura Type</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= @speaker.aura_type %></dd>
                </div>
              <% end %>
              <% if @speaker.inner_authority.present? %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Inner Authority</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= @speaker.inner_authority %></dd>
                </div>
              <% end %>
              <% if @speaker.profile.present? %>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Profile</dt>
                  <dd class="mt-1 text-sm text-gray-900"><%= @speaker.profile %></dd>
                </div>
              <% end %>
            </dl>
            <% if @speaker.birth_date_utc.present? && @speaker.birth_date_local.present? && @speaker.birth_location.present? && @bodygraph_data.present? %>
              <div class="mt-6">
                <%= render partial: 'hdhd/shared/bodygraph_widget', locals: { speaker: @speaker, bodygraph_data: @bodygraph_data } %>
              </div>
            <% end %>
          </div>
        <% end %>
        <% if @speaker.company_logo.attached? %>
          <div class="mt-8 border-t border-gray-200 pt-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Company</h3>
            <div class="flex items-center">
              <%= image_tag @speaker.company_logo.variant(resize_to_limit: [200, 100]), class: "max-h-16" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
</div>
