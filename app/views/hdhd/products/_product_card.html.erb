<div class="bg-white rounded-lg shadow-lg overflow-hidden">
  <%= image_tag product.image.attached? ? url_for(product.image) : "default-image.jpg",
                alt: product.title,
                class: "w-full" %>
  <div class="p-6">
    <h2 class="text-2xl font-bold mb-4"><%= product.title %></h2>
    <p class="text-gray-700 mb-4"><%= product.short_description %></p>
    <div class="flex items-center justify-between">
      <% if current_user && current_user.purchased_products.exists?(product: product) %>
        <span class="bg-green-100 text-green-800 px-4 py-2 rounded-full font-semibold">
          In Library
        </span>
        <ul class="mt-2">
          <li>Download Links:</li>
          <% product.download_links.each do |link| %>
            <li>
              <a href="<%= link.url %>" class="text-blue-500 underline" target="_blank">
                <%= link.title %>
              </a>
            </li>
          <% end %>
        </ul>
      <% else %>
        <span class="text-2xl font-bold text-blue-600">
          <%= format_currency(product.price) %>
        </span>
        <%= button_to "Buy Now",
                      checkout_hdhd_order_path(product),
                      method: :post,
                      class: "bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-200" %>
      <% end %>
    </div>
  </div>
</div>
