<div class="container mx-auto px-4">
  <h1 class="text-3xl font-bold text-center my-6">Products</h1>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <% @products.each do |product| %>
      <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <%= image_tag product.thumbnail.attached? ? url_for(product.thumbnail) : "default-image.jpg", alt: product.title, class: "w-full h-48 object-cover" %>
        <div class="p-4">
          <h2 class="text-xl font-semibold"><%= product.title %></h2>
          <p class="text-gray-600"><%= product.short_description %></p>
          <% if current_user && current_user.purchased_products.exists?(product: product) %>
            <span class="bg-green-100 text-green-800 px-4 py-2 mr-4 rounded-full font-semibold">
              In Library
            </span>
          <% else %>
            <p class="text-lg font-bold mt-2"><%= format_price(product.price) %></p>
          <% end %>
          <a href="<%= hdhd_product_path(product) %>" class="mt-4 inline-block bg-purple-500 text-white px-4 py-2 rounded-lg">View Product</a>
        </div>
      </div>
    <% end %>
  </div>
</div>
