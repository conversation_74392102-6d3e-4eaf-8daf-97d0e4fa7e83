<%
  # This partial renders a bodygraph for a speaker
  # It requires the following parameters:
  # - speaker: The Hdhd::Speaker object to render the bodygraph for
  # - bodygraph_data: The data returned from SpeakerBodygraphService

  # Only render if we have the necessary data
  if speaker.birth_date_utc.present? && speaker.birth_date_local.present? && speaker.birth_location.present? && bodygraph_data.present?
    # Extract data for easier access
    personality_activations_json = bodygraph_data[:personality_activations_json].to_json
    design_activations_json = bodygraph_data[:design_activations_json].to_json

    # Define metadata for the bodygraph
    metadata = {
      aura_type: bodygraph_data[:aura_type],
      inner_authority: bodygraph_data[:inner_authority],
      profile: bodygraph_data[:profile],
      definition: bodygraph_data[:definition],
      incarnation_cross: bodygraph_data[:incarnation_cross]
    }

    # Define center colors - darker brown for throat, spleen, solarplexus, and root, darker red for sacral
    center_colors = ['#F9F6C4', '#48BB78', '#3D2A1F', '#3D2A1F', '#F56565', '#F9F6C4', '#3D2A1F', '#B71C1C', '#3D2A1F']

    # Define center opacity setting
    center_opacity = 0.9
%>
<div class="bodygraph-widget rounded-2xl bg-gray-50 py-6 shadow-sm">
  <h3 class="text-xl font-semibold text-gray-900 mb-4">Bodygraph</h3>
  <div class="flex flex-col items-center">
    <%= render partial: 'shared/bodygraph_renderer', locals: {
      id: "speaker-#{speaker.id}",
      personality_activations_json: personality_activations_json,
      design_activations_json: design_activations_json,
      mode: 'all',
      show_metadata: true,
      metadata: metadata,
      center_colors: center_colors,
      center_opacity: center_opacity
    } %>
  </div>
</div>
<% end %>
