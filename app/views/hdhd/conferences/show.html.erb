<div class="bg-white py-8">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:mx-0">
      <h1 class="text-pretty text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-2">
        HDHD <%= @conference.year %>
      </h1>
      <p class="text-lg text-gray-600">
        <%= @conference.start_date.strftime("%B %d") %> - <%= (@conference.start_date + 5.days).strftime("%B %d, %Y") %>
      </p>
    </div>
    <% if @conference.product.present? %>
      <div class="bg-white py-8">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl">
            <%= render partial: "hdhd/products/product_card", locals: { product: @conference.product } %>
          </div>
        </div>
      </div>
    <% end %>
    <% Hdhd::Speaker::ROL<PERSON>.each do |role| %>
      <% speakers = @conference.speakers.where(role: role) %>
      <% if speakers.any? %>
        <div class="bg-white py-12">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl lg:mx-0">
              <h2 class="text-pretty text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
                <%= role.titleize.pluralize %>
              </h2>
            </div>
            <ul role="list" class="mx-auto mt-10 grid max-w-2xl grid-cols-2 gap-x-8 gap-y-12 text-center sm:grid-cols-3 md:grid-cols-4 lg:mx-0 lg:max-w-none lg:grid-cols-5 xl:grid-cols-6">
              <% speakers.each do |speaker| %>
                <li>
                  <a href="<%= speaker_path(speaker) %>">
                    <% if speaker.profile_picture.attached? %>
                      <div class="mx-auto size-24 rounded-full overflow-hidden shadow-md">
                        <%= image_tag speaker.profile_picture, class: "h-full w-full object-cover", alt: speaker.full_name %>
                      </div>
                    <% else %>
                      <div class="mx-auto size-24 rounded-full bg-gray-200 flex items-center justify-center shadow-md">
                        <span class="text-gray-500 text-xl font-semibold"><%= speaker.full_name.to_s[0] || "?" %></span>
                      </div>
                    <% end %>
                  </a>
                  <h3 class="mt-4 text-base/7 font-semibold tracking-tight text-gray-900">
                    <a href="<%= speaker_path(speaker) %>" class="hover:text-indigo-600 transition-colors"><%= speaker.full_name %></a>
                  </h3>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      <% end %>
    <% end %>
    <!-- Scheduled Events Section -->
    <% if @conference.scheduled_events.any? %>
      <div class="bg-white py-12">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:mx-0">
            <h2 class="text-pretty text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
              Schedule
            </h2>
            <p class="mt-4 text-lg/8 text-gray-600">
              Explore our lineup of talks, workshops, performances, and more.
            </p>
            <div class="mt-6">
              <%= link_to "View Full Schedule", hdhd_scheduled_events_path(conference_year: @conference.year), class: "rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
            </div>
          </div>
          <!-- Events Summary Cards -->
          <div class="mt-10 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
            <%
              event_type_icons = {
                'talk' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>',
                'workshop' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" /></svg>',
                'performance' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" /></svg>',
                'special_event' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>',
                'interview' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" /></svg>',
                'panel' => '<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>'
              }
            %>
            <% Hdhd::ScheduledEvent::EVENT_TYPES.each do |event_type| %>
              <% count = @conference.scheduled_events.where(event_type: event_type).count %>
              <% if count > 0 %>
                <div class="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
                  <div class="p-5">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 bg-<%= event_type_color(event_type) %>-100 rounded-md p-3">
                        <div class="h-6 w-6 text-<%= event_type_color(event_type) %>-600">
                          <%= event_type_icons[event_type].html_safe %>
                        </div>
                      </div>
                      <div class="ml-5 w-0 flex-1">
                        <dl>
                          <dt class="text-sm font-medium text-gray-500 truncate">
                            <%= event_type.titleize.pluralize %>
                          </dt>
                          <dd>
                            <div class="text-lg font-medium text-gray-900">
                              <%= count %>
                            </div>
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                  <div class="bg-gray-50 px-5 py-3">
                    <div class="text-sm">
                      <a href="#<%= event_type %>" class="font-medium text-<%= event_type_color(event_type) %>-600 hover:text-<%= event_type_color(event_type) %>-900">View all</a>
                    </div>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
          <!-- Events by Type -->
          <div class="mt-12">
            <% Hdhd::ScheduledEvent::EVENT_TYPES.each do |event_type| %>
              <% events = @conference.scheduled_events.where(event_type: event_type).order(start_date: :asc) %>
              <% if events.any? %>
                <div class="mb-16" id="<%= event_type %>">
                  <div class="flex items-center">
                    <h3 class="text-2xl font-bold text-gray-900">
                      <%= event_type.titleize.pluralize %>
                    </h3>
                    <span class="ml-3 inline-flex items-center rounded-md bg-<%= event_type_color(event_type) %>-50 px-2 py-1 text-xs font-medium text-<%= event_type_color(event_type) %>-700 ring-1 ring-inset ring-<%= event_type_color(event_type) %>-600/20">
                      <%= events.count %> <%= events.count == 1 ? 'event' : 'events' %>
                    </span>
                  </div>
                  <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8">
                    <% events.each do |event| %>
                      <div class="group relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
                        <div class="aspect-h-1 aspect-w-2 w-full overflow-hidden">
                          <% if event.cover_image.attached? %>
                            <%= image_tag event.cover_image, class: "h-full w-full object-cover object-center" %>
                          <% else %>
                            <div class="flex h-full items-center justify-center bg-<%= event_type_color(event_type) %>-50">
                              <div class="text-<%= event_type_color(event_type) %>-400">
                                <%= event_type_icons[event_type].html_safe %>
                              </div>
                            </div>
                          <% end %>
                        </div>
                        <div class="p-4">
                          <div>
                            <h4 class="text-base font-semibold text-gray-900 line-clamp-2">
                              <%= link_to event.title, hdhd_scheduled_event_path(event), class: "hover:text-indigo-600" %>
                            </h4>
                            <p class="mt-1 text-sm text-gray-500">
                              <%= event.start_date.strftime("%A, %B %d • %l:%M %p") %>
                            </p>
                            <% if event.speakers.any? %>
                              <p class="mt-1 text-sm text-gray-500 line-clamp-1">
                                With: <%= event.speakers.map(&:full_name).join(', ') %>
                              </p>
                            <% end %>
                          </div>
                          <div class="mt-3 flex justify-end">
                            <%= link_to "View Details", hdhd_scheduled_event_path(event), class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
