<div class="bg-gray-900">
  <div class="absolute top-0 left-0 w-full h-screen bg-black z-0">
    <img id="hero-image"
       src="<%= asset_path('hdhd/hdhd-hero-image-' + (rand(1..9).to_s) + '.jpg') %>"
       alt="Hero Image"
       class="absolute w-full h-screen object-cover transition-opacity duration-1000 opacity-20" />
  </div>
  <script>
    document.addEventListener("DOMContentLoaded", () => {
        const images = [
      "<%= asset_path('hdhd/hdhd-hero-image-1.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-2.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-3.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-4.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-5.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-6.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-7.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-8.jpg') %>",
      "<%= asset_path('hdhd/hdhd-hero-image-9.jpg') %>"
        ];

        // Shuffle the images randomly on each page load
        const shuffledOrder = [...images].sort(() => Math.random() - 0.5);

        let currentIndex = 0;
        const heroImage = document.getElementById("hero-image");

        function changeImage() {
      currentIndex = (currentIndex + 1) % shuffledOrder.length;
      heroImage.style.opacity = "0";

      setTimeout(() => {
          heroImage.src = shuffledOrder[currentIndex];
          heroImage.style.opacity = ".2";
      }, 600); // Match fade duration
        }

        setInterval(changeImage, 12000);
    });
  </script>
  <div class="mx-auto max-w-7xl px-6 lg:px-8 relative z-10">
    <div class="mx-auto max-w-2xl py-32">
      <div class="hidden sm:mb-8 sm:flex sm:justify-center">
        <div class="relative rounded-full px-3 py-1 text-sm/6 text-gray-400 ring-1 ring-white/10 hover:ring-white/20">
          Request for Proposals: 2025 Speakers and Facilitators <a href="https://forms.gle/mnX6xTU3WGdkSp9w9" class="font-semibold text-white"><span class="absolute inset-0" aria-hidden="true"></span>Read more <span aria-hidden="true">&rarr;</span></a>
        </div>
      </div>
      <div class="text-center">
        <h1 class="text-balance text-5xl font-semibold tracking-tight text-white sm:text-7xl">High Desert Human Design 2025</h1>
        <p class="mt-8 text-pretty text-lg font-medium text-gray-400 sm:text-xl/8">September 17 - 21, 2025</p>
        <p class="mt-8 text-4xl font-bold text-center text-white">$497</p>
        <div class="mt-10 flex items-center justify-center gap-x-6">
          <% if user_signed_in? %>
            <% conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Time.now.year).first %>
            <% if conference && current_user.has_ticket_for_year?(conference.year) %>
              <a href="<%= dashboard_path %>" class="rounded-md bg-green-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-500">View Your Ticket</a>
            <% else %>
              <a href="<%= new_hdhd_conference_ticket_path(conference_year: Time.now.year) %>" class="rounded-md bg-red-500 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400">Purchase Ticket</a>
            <% end %>
          <% else %>
            <a href="<%= new_user_session_path %>" class="rounded-md bg-red-500 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-red-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-400">Sign In to Purchase</a>
          <% end %>
          <a href="https://quantumcollective.my.canva.site/high-desert-human-design-2025" class="text-sm/6 font-semibold text-white">Learn more <span aria-hidden="true">→</span></a>
        </div>
        <div class="mt-4 text-center">
          <p class="text-xs text-gray-400">By purchasing, you agree to our <a href="/terms-and-conditions" class="text-gray-300 hover:text-white">Terms</a> and <a href="/hdhd-conference-attendee-agreement" class="text-gray-300 hover:text-white">Attendee Agreement</a></p>
        </div>
      </div>
    </div>
  </div>
  <div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]" aria-hidden="true">
    <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
  </div>
</div>