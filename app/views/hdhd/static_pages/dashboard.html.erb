<div class="bg-white rounded-2xl">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10">
    <div class="mx-auto max-w-2xl lg:mx-0">
      <h1 class="text-3xl font-bold tracking-tight text-gray-900">Dashboard</h1>
      <p class="mt-2 text-lg leading-8 text-gray-600">Welcome to your High Desert Human Design dashboard.</p>
    </div>
    <div class="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
      <!-- Quick Links -->
      <div class="rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900">Quick Links</h3>
        <div class="mt-4 space-y-3">
          <%= link_to schedule_path, class: "block text-sm text-indigo-600 hover:text-indigo-500" do %>
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              View Schedule
            </div>
          <% end %>
          <%= link_to printouts_path, class: "block text-sm text-indigo-600 hover:text-indigo-500" do %>
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Download Materials
            </div>
          <% end %>
          <%= link_to map_path, class: "block text-sm text-indigo-600 hover:text-indigo-500" do %>
            <div class="flex items-center">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              View Map
            </div>
          <% end %>
          <% if current_user.speaker.present? %>
            <%= link_to edit_speaker_path(current_user.speaker), class: "block text-sm text-indigo-600 hover:text-indigo-500" do %>
              <div class="flex items-center">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Edit Speaker Profile
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
      <!-- Upcoming Sessions -->
      <%# <div class="rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900">Today's Sessions</h3>
        <div class="mt-4 space-y-3">
          <div class="text-sm text-gray-600">
            <p class="font-medium">9:00 AM - Opening Ceremony</p>
            <p class="text-xs text-gray-500">Main Hall</p>
          </div>
          <div class="text-sm text-gray-600">
            <p class="font-medium">10:30 AM - Introduction to Human Design</p>
            <p class="text-xs text-gray-500">Room 101</p>
          </div>
          <div class="text-sm text-gray-600">
            <p class="font-medium">2:00 PM - Advanced Topics</p>
            <p class="text-xs text-gray-500">Workshop Room</p>
          </div>
        </div>
      </div> %>
      <!-- Announcements -->
      <div class="rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900">Announcements</h3>
        <div class="mt-4 space-y-3">
          <div class="text-sm text-gray-600">
            <p class="font-medium">Speaker Applications Open</p>
            <p class="text-xs text-gray-500"><%= link_to "Apply now", "https://forms.gle/mnX6xTU3WGdkSp9w9", class: "text-blue-600 underline hover:text-blue-800" %> to be a presenter or workshop facilitator at HDHD <%= @current_year %>.</p>
          </div>
          <div class="text-sm text-gray-600">
            <p class="font-medium">Volunteer Applications Open</p>
            <p class="text-xs text-gray-500">Looking to join our volunteer team? Please fill out our <%= link_to "Volunteer Questionnaire", "https://forms.gle/g2QFmMrnFL4VN1Ss5", class: "text-blue-600 underline hover:text-blue-800" %>.</p>
          </div>
        </div>
      </div>
    </div>
    <!-- Additional Content Section -->
    <div class="mt-8 space-y-8">
      <% if @conference %>
        <div class="rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900">Conference Ticket</h3>
          <div class="mt-4">
            <% if current_user.has_ticket_for_year?(@conference.year) %>
              <% ticket = current_user.conference_tickets.for_year(@conference.year).first %>
              <div class="rounded-md bg-green-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">HDHD <%= @conference.year %> Ticket Status: <%= ticket.status.capitalize %></h3>
                    <div class="mt-2 text-sm text-green-700">
                      <% if ticket.status == 'complimentary' %>
                        <p>You have received a complimentary ticket for the HDHD <%= @conference.year %> conference.</p>
                      <% else %>
                        <p>You have successfully purchased a ticket for the HDHD <%= @conference.year %> conference.</p>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% else %>
              <div class="rounded-md bg-yellow-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">HDHD <%= @conference.year %> Ticket Status: Not yet purchased</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                      <p>You haven't purchased a ticket for the HDHD <%= @conference.year %> conference yet.</p>
                      <p class="mt-2">
                        <%= link_to "Purchase", new_hdhd_conference_ticket_path(conference_year: @conference.year), class: "font-medium text-yellow-700 underline hover:text-yellow-600" %>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
      <div class="rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900">Resources</h3>
        <div class="mt-4 prose prose-sm max-w-none">
          <p>Access all conference materials, schedules, and resources through the navigation menu. If you need assistance, don't hesitate to <a class="text-blue-600" href="mailto:<EMAIL>">reach out to Jonah</a>.</p>
        </div>
      </div>
      <% if @conference %>
        <div class="rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900">Countdown to HDHD <%= @conference.start_date.year %></h3>
          <div class="mt-4">
            <div id="countdown" class="text-3xl font-bold text-indigo-600"></div>
            <p class="mt-2 text-sm text-gray-500">
              Conference starts: <%= @conference.start_date.strftime("%B %-d, %Y at %I:%M %p") %> UTC
            </p>
          </div>
        </div>
        <script>
          function updateCountdown() {
            const conferenceDate = new Date("<%= @conference.start_date.strftime('%Y-%m-%d %H:%M:%S UTC') %>");
            const now = new Date();
            const difference = conferenceDate - now;

            if (difference < 0) {
              document.getElementById('countdown').innerHTML = 'Conference has started!';
              return;
            }

            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            let countdownText = '';
            if (days > 0) countdownText += `${days}d `;
            if (hours > 0 || days > 0) countdownText += `${hours}h `;
            if (minutes > 0 || hours > 0 || days > 0) countdownText += `${minutes}m `;
            countdownText += `${seconds}s`;

            document.getElementById('countdown').innerHTML = countdownText;
          }

          // Update countdown immediately and then every second
          updateCountdown();
          setInterval(updateCountdown, 1000);
        </script>
      <% else %>
        <div class="rounded-lg border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900">HDHD <%= Time.now.year %></h3>
          <div class="mt-4 text-sm text-gray-500">
            Conference details coming soon.
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
