<div class="bg-white -mx-4 sm:-mx-6 lg:-mx-8 -mt-10 -mb-10 px-4 sm:px-6 lg:px-8 py-10">
  <div class="mx-auto">
    <!-- Back to schedule link -->
    <div class="mb-8 max-w-4xl mx-auto">
      <%= link_to hdhd_scheduled_events_path(conference_year: @scheduled_event.conference.year), class: "inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500" do %>
        <svg class="-ml-1 mr-1 h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M18 10a.75.75 0 01-.75.75H4.66l2.1 1.95a.75.75 0 11-1.02 1.1l-3.5-3.25a.75.75 0 010-1.1l3.5-3.25a.75.75 0 111.02 1.1l-2.1 1.95h12.59A.75.75 0 0118 10z" clip-rule="evenodd" />
        </svg>
        Back to Schedule
      <% end %>
    </div>
    <!-- Event header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between max-w-4xl mx-auto">
      <div>
        <span class="inline-flex items-center rounded-md bg-<%= event_type_color(@scheduled_event.event_type) %>-50 px-2 py-1 text-xs font-medium text-<%= event_type_color(@scheduled_event.event_type) %>-700 ring-1 ring-inset ring-<%= event_type_color(@scheduled_event.event_type) %>-600/20">
          <%= @scheduled_event.event_type.titleize %>
        </span>
        <h1 class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"><%= @scheduled_event.title %></h1>
      </div>
      <div class="mt-4 md:mt-0 flex flex-col items-start md:items-end">
        <div class="text-lg text-gray-700">
          <%= @scheduled_event.start_date.strftime("%A, %B %d, %Y") %>
        </div>
        <div class="text-lg text-gray-700">
          <%= @scheduled_event.start_date.strftime("%l:%M %p") %> - <%= @scheduled_event.end_date.strftime("%l:%M %p") %>
        </div>
        <div class="text-sm text-gray-500">
          Duration: <%= @scheduled_event.duration_hours %> hours
        </div>
      </div>
    </div>
    <!-- Event content -->
    <div class="mt-12 grid grid-cols-1 gap-x-8 gap-y-10 lg:grid-cols-3 max-w-4xl mx-auto">
      <!-- Main content -->
      <div class="lg:col-span-2">
        <% if @scheduled_event.cover_image.attached? %>
          <div class="mb-8 rounded-lg overflow-hidden">
            <%= image_tag @scheduled_event.cover_image, class: "w-full h-auto object-cover" %>
          </div>
        <% end %>
        <% if @scheduled_event.description.present? %>
          <div class="prose prose-indigo max-w-none">
            <%= simple_format(@scheduled_event.description) %>
          </div>
        <% end %>
      </div>
      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <% if @scheduled_event.speakers.any? %>
          <div class="rounded-lg bg-gray-50 p-6">
            <h2 class="text-base font-semibold leading-7 text-gray-900">
              <%= @scheduled_event.speakers.count > 1 ? 'Presenters' : 'Presenter' %>
            </h2>
            <ul class="mt-6 space-y-6">
              <% @scheduled_event.speakers.each do |speaker| %>
                <li class="flex gap-x-4">
                  <% if speaker.profile_picture.attached? %>
                    <%= image_tag speaker.profile_picture, class: "h-12 w-12 flex-none rounded-full bg-gray-50 object-cover" %>
                  <% else %>
                    <div class="h-12 w-12 flex-none rounded-full bg-gray-200 flex items-center justify-center">
                      <span class="text-gray-500 text-lg"><%= speaker.full_name.to_s[0] || "?" %></span>
                    </div>
                  <% end %>
                  <div class="min-w-0 flex-auto">
                    <p class="text-sm font-semibold leading-6 text-gray-900">
                      <%= link_to speaker.full_name, speaker_path(speaker), class: "hover:text-indigo-600" %>
                    </p>
                    <p class="mt-1 text-xs leading-5 text-gray-500"><%= speaker.role.titleize %></p>
                  </div>
                </li>
              <% end %>
            </ul>
            <div class="mt-6 border-t border-gray-900/5 pt-6">
              <a href="#" class="text-sm font-semibold leading-6 text-indigo-600 hover:text-indigo-500">
                View all speakers <span aria-hidden="true">→</span>
              </a>
            </div>
          </div>
        <% end %>
        <div class="mt-6 rounded-lg bg-gray-50 p-6">
          <h2 class="text-base font-semibold leading-7 text-gray-900">Conference Details</h2>
          <dl class="mt-4 space-y-4 text-sm leading-6 text-gray-600">
            <div>
              <dt class="font-medium text-gray-900">Conference</dt>
              <dd class="mt-1">
                <%= link_to "HDHD #{@scheduled_event.conference.year}", conference_path(@scheduled_event.conference), class: "text-indigo-600 hover:text-indigo-500" %>
              </dd>
            </div>
            <div>
              <dt class="font-medium text-gray-900">Location</dt>
              <dd class="mt-1">High Desert Human Design Conference Center</dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>
