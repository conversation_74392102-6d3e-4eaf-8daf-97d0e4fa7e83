<div class="bg-white -mx-4 sm:-mx-6 lg:-mx-8 -mt-10 -mb-10 px-4 sm:px-6 lg:px-8 py-10">
  <div class="mx-auto">
    <div class="mx-auto max-w-4xl">
      <h2 class="text-pretty text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
        HDHD <%= @conference.year %> Schedule
      </h2>
      <p class="mt-4 text-lg/8 text-gray-600">
        Explore the schedule for the <%= @conference.year %> High Desert Human Design conference.
      </p>
    </div>
    <!-- Event Type Filters -->
    <div class="mt-10 flex flex-wrap gap-2">
      <%= link_to "All Events", hdhd_scheduled_events_path(conference_year: @conference.year),
          class: "rounded-full px-4 py-2 text-sm font-medium #{params[:event_type].blank? ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}" %>
      <% Hdhd::ScheduledEvent::EVENT_TYPES.each do |event_type| %>
        <%= link_to event_type.titleize, hdhd_scheduled_events_path(conference_year: @conference.year, event_type: event_type),
            class: "rounded-full px-4 py-2 text-sm font-medium #{params[:event_type] == event_type ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}" %>
      <% end %>
    </div>
    <!-- Schedule by Day -->
    <div class="mt-16">
      <% if @events_by_day.empty? %>
        <div class="text-center py-12">
          <p class="text-lg text-gray-600">No events scheduled yet. Check back soon!</p>
        </div>
      <% else %>
        <% @events_by_day.each do |day, events| %>
          <div class="mb-16">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">
              <%= day.strftime("%A, %B %d, %Y") %>
            </h3>
            <div class="space-y-8">
              <% events.each do |event| %>
                <div class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl overflow-hidden">
                  <div class="flex flex-col md:flex-row">
                    <% if event.cover_image.attached? %>
                      <div class="md:w-1/3">
                        <%= image_tag event.cover_image, class: "h-48 md:h-full w-full object-cover" %>
                      </div>
                    <% end %>
                    <div class="p-6 flex-1 <%= event.cover_image.attached? ? 'md:w-2/3' : 'w-full' %>">
                      <div class="flex items-center justify-between">
                        <span class="inline-flex items-center rounded-md bg-<%= event_type_color(event.event_type) %>-50 px-2 py-1 text-xs font-medium text-<%= event_type_color(event.event_type) %>-700 ring-1 ring-inset ring-<%= event_type_color(event.event_type) %>-600/20">
                          <%= event.event_type.titleize %>
                        </span>
                        <span class="text-sm text-gray-500">
                          <%= event.start_date.strftime("%l:%M %p") %> - <%= event.end_date.strftime("%l:%M %p") %>
                          (<%= event.duration_hours %> hours)
                        </span>
                      </div>
                      <h4 class="mt-2 text-xl font-semibold text-gray-900">
                        <%= link_to event.title, hdhd_scheduled_event_path(event), class: "hover:text-indigo-600" %>
                      </h4>
                      <% if event.description.present? %>
                        <p class="mt-2 text-gray-600 line-clamp-2">
                          <%= event.description %>
                        </p>
                      <% end %>
                      <% if event.speakers.any? %>
                        <div class="mt-4">
                          <h5 class="text-sm font-medium text-gray-700">Featuring:</h5>
                          <div class="mt-2 flex flex-wrap gap-2">
                            <% event.speakers.each do |speaker| %>
                              <%= link_to speaker_path(speaker), class: "group flex items-center" do %>
                                <% if speaker.profile_picture.attached? %>
                                  <%= image_tag speaker.profile_picture, class: "h-8 w-8 rounded-full object-cover mr-2" %>
                                <% else %>
                                  <div class="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                                    <span class="text-gray-500 text-xs"><%= speaker.full_name.to_s[0] || "?" %></span>
                                  </div>
                                <% end %>
                                <span class="text-sm text-gray-700 group-hover:text-indigo-600"><%= speaker.full_name %></span>
                              <% end %>
                            <% end %>
                          </div>
                        </div>
                      <% end %>
                      <div class="mt-4">
                        <%= link_to "View Details", hdhd_scheduled_event_path(event), class: "text-sm font-medium text-indigo-600 hover:text-indigo-500" %>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  </div>
</div>
