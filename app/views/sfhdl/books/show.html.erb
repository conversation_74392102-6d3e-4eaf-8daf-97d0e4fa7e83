<div class="bg-white py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl">
      <div class="flex flex-col md:flex-row gap-8">
        <div class="md:w-1/3">
          <% if @book.cover.attached? %>
            <%= image_tag @book.cover, class: "w-full h-auto rounded-lg shadow-lg" %>
          <% else %>
            <div class="w-full aspect-[3/4] bg-gray-200 rounded-lg shadow-lg flex items-center justify-center">
              <svg class="h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
              </svg>
            </div>
          <% end %>
        </div>
        
        <div class="md:w-2/3">
          <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"><%= @book.title %></h1>
          
          <% if @book.authors.present? %>
            <div class="mt-4">
              <h2 class="text-lg font-semibold text-gray-900">
                Author<%= @book.authors.count > 1 ? 's' : '' %>:
              </h2>
              <ul class="mt-2 space-y-1">
                <% @book.authors.each do |author| %>
                  <li>
                    <a href="<%= sfhdl_author_path(author) %>" class="text-indigo-600 hover:text-indigo-500">
                      <%= author.name %>
                    </a>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
          
          <div class="mt-6 border-t border-gray-200 pt-6">
            <dl class="divide-y divide-gray-200">
              <% if @book.publication_date.present? %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt class="text-sm font-medium text-gray-500">Publication Date</dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <%= @book.publication_date.strftime("%B %d, %Y") %>
                  </dd>
                </div>
              <% end %>
              
              <% if @book.number_of_pages.present? %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt class="text-sm font-medium text-gray-500">Number of Pages</dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <%= @book.number_of_pages %>
                  </dd>
                </div>
              <% end %>
              
              <% if @book.acquisition_date.present? %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt class="text-sm font-medium text-gray-500">Acquisition Date</dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <%= @book.acquisition_date.strftime("%B %d, %Y") %>
                  </dd>
                </div>
              <% end %>
              
              <% if @book.physical_location.present? %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt class="text-sm font-medium text-gray-500">Physical Location</dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <%= @book.physical_location %>
                  </dd>
                </div>
              <% end %>
              
              <% if @book.donor.present? %>
                <div class="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
                  <dt class="text-sm font-medium text-gray-500">Donated By</dt>
                  <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                    <%= @book.donor.name %>
                  </dd>
                </div>
              <% end %>
            </dl>
          </div>
          
          <% if @book.description.present? %>
            <div class="mt-6 border-t border-gray-200 pt-6">
              <h2 class="text-lg font-semibold text-gray-900">Description</h2>
              <div class="mt-4 prose prose-indigo prose-lg text-gray-600">
                <%= @book.description %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      
      <div class="mt-12 flex justify-center">
        <a href="<%= books_path %>" class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
          Back to Books
        </a>
      </div>
    </div>
  </div>
</div>
