<div class="bg-white py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:text-center">
      <h2 class="text-base font-semibold leading-7 text-indigo-600">Books</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Human Design Books</p>
      <p class="mt-6 text-lg leading-8 text-gray-600">Browse our collection of physical books on Human Design.</p>
    </div>
    
    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
      <% if @books.present? %>
        <ul role="list" class="grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          <% @books.each do |book| %>
            <li class="overflow-hidden rounded-xl border border-gray-200">
              <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
                <% if book.cover.attached? %>
                  <%= image_tag book.cover, class: "h-12 w-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10" %>
                <% else %>
                  <div class="h-12 w-12 flex-none rounded-lg bg-indigo-500 text-white flex items-center justify-center">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                    </svg>
                  </div>
                <% end %>
                <div class="text-sm font-medium leading-6 text-gray-900"><%= book.title %></div>
              </div>
              <dl class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
                <% if book.authors.present? %>
                  <div class="flex justify-between gap-x-4 py-3">
                    <dt class="text-gray-500">Author<%= book.authors.count > 1 ? 's' : '' %></dt>
                    <dd class="text-gray-700">
                      <%= book.authors.map(&:name).join(', ') %>
                    </dd>
                  </div>
                <% end %>
                <% if book.publication_date.present? %>
                  <div class="flex justify-between gap-x-4 py-3">
                    <dt class="text-gray-500">Published</dt>
                    <dd class="text-gray-700"><%= book.publication_date.year %></dd>
                  </div>
                <% end %>
                <% if book.number_of_pages.present? %>
                  <div class="flex justify-between gap-x-4 py-3">
                    <dt class="text-gray-500">Pages</dt>
                    <dd class="text-gray-700"><%= book.number_of_pages %></dd>
                  </div>
                <% end %>
                <div class="pt-3">
                  <a href="<%= book_path(book) %>" class="text-indigo-600 hover:text-indigo-500">View Details →</a>
                </div>
              </dl>
            </li>
          <% end %>
        </ul>
      <% else %>
        <div class="text-center py-12">
          <p class="text-lg text-gray-600">No books found in the database.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>
