<div class="bg-white py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:text-center">
      <h2 class="text-base font-semibold leading-7 text-indigo-600">Author Profile</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"><%= @author.name %></p>
      
      <div class="mt-8 flex justify-center">
        <% if @author.profile_picture.attached? %>
          <%= image_tag @author.profile_picture, class: "h-32 w-32 rounded-full object-cover" %>
        <% else %>
          <div class="h-32 w-32 rounded-full bg-indigo-500 text-white flex items-center justify-center text-4xl">
            <%= @author.name.first.upcase %>
          </div>
        <% end %>
      </div>
      
      <% if @author.email.present? %>
        <p class="mt-4 text-lg text-gray-600"><%= @author.email %></p>
      <% end %>
      
      <% if @author.biography.present? %>
        <div class="mt-8 text-left">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Biography</h3>
          <p class="text-base text-gray-600"><%= @author.biography %></p>
        </div>
      <% end %>
    </div>
    
    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
      <h3 class="text-2xl font-bold tracking-tight text-gray-900 mb-8">Works by <%= @author.name %></h3>
      
      <% if @books.present? %>
        <div class="mb-12">
          <h4 class="text-xl font-semibold text-gray-900 mb-6">Books</h4>
          <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <% @books.each do |book| %>
              <li class="col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow">
                <div class="flex w-full items-center justify-between space-x-6 p-6">
                  <div class="flex-1 truncate">
                    <div class="flex items-center space-x-3">
                      <h3 class="truncate text-sm font-medium text-gray-900"><%= book.title %></h3>
                    </div>
                    <p class="mt-1 truncate text-sm text-gray-500"><%= book.publication_date&.year %></p>
                  </div>
                  <% if book.cover.attached? %>
                    <%= image_tag book.cover, class: "h-16 w-16 flex-shrink-0 rounded-md bg-gray-200" %>
                  <% else %>
                    <div class="h-16 w-16 flex-shrink-0 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                      <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                      </svg>
                    </div>
                  <% end %>
                </div>
                <div>
                  <div class="-mt-px flex divide-x divide-gray-200">
                    <div class="flex w-0 flex-1">
                      <a href="<%= book_path(book) %>" class="relative -mr-px inline-flex w-0 flex-1 items-center justify-center gap-x-3 rounded-bl-lg border border-transparent py-4 text-sm font-semibold text-gray-900">
                        View Details
                      </a>
                    </div>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
      
      <% if @ebooks.present? %>
        <div class="mb-12">
          <h4 class="text-xl font-semibold text-gray-900 mb-6">Ebooks</h4>
          <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <% @ebooks.each do |ebook| %>
              <li class="col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow">
                <div class="flex w-full items-center justify-between space-x-6 p-6">
                  <div class="flex-1 truncate">
                    <div class="flex items-center space-x-3">
                      <h3 class="truncate text-sm font-medium text-gray-900"><%= ebook.title %></h3>
                    </div>
                    <p class="mt-1 truncate text-sm text-gray-500"><%= ebook.publication_date&.year %></p>
                  </div>
                  <% if ebook.cover.attached? %>
                    <%= image_tag ebook.cover, class: "h-16 w-16 flex-shrink-0 rounded-md bg-gray-200" %>
                  <% else %>
                    <div class="h-16 w-16 flex-shrink-0 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                      <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                      </svg>
                    </div>
                  <% end %>
                </div>
                <div>
                  <div class="-mt-px flex divide-x divide-gray-200">
                    <div class="flex w-0 flex-1">
                      <a href="<%= ebook_path(ebook) %>" class="relative -mr-px inline-flex w-0 flex-1 items-center justify-center gap-x-3 rounded-bl-lg border border-transparent py-4 text-sm font-semibold text-gray-900">
                        View Details
                      </a>
                    </div>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
      
      <!-- Similar sections for CDs, Tapes, Audios, and Videos -->
      <% if @cds.present? || @tapes.present? || @audios.present? || @videos.present? %>
        <div class="mb-12">
          <h4 class="text-xl font-semibold text-gray-900 mb-6">Other Media</h4>
          <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <% (@cds + @tapes + @audios + @videos).each do |media| %>
              <li class="col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow">
                <div class="flex w-full items-center justify-between space-x-6 p-6">
                  <div class="flex-1 truncate">
                    <div class="flex items-center space-x-3">
                      <h3 class="truncate text-sm font-medium text-gray-900"><%= media.title %></h3>
                    </div>
                    <p class="mt-1 truncate text-sm text-gray-500">
                      <%= media.class.name %> • <%= media.publication_date&.year %>
                    </p>
                  </div>
                  <% if media.cover.attached? %>
                    <%= image_tag media.cover, class: "h-16 w-16 flex-shrink-0 rounded-md bg-gray-200" %>
                  <% else %>
                    <div class="h-16 w-16 flex-shrink-0 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                      <% if media.is_a?(Cd) || media.is_a?(Tape) || media.is_a?(Audio) %>
                        <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" />
                        </svg>
                      <% else %>
                        <svg class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 01-1.125-1.125M3.375 19.5h1.5C5.496 19.5 6 18.996 6 18.375m-3.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-1.5A1.125 1.125 0 0118 18.375M20.625 4.5H3.375m17.25 0c.621 0 1.125.504 1.125 1.125M20.625 4.5h-1.5C18.504 4.5 18 5.004 18 5.625m3.75 0v1.5c0 .621-.504 1.125-1.125 1.125M3.375 4.5c-.621 0-1.125.504-1.125 1.125M3.375 4.5h1.5C5.496 4.5 6 5.004 6 5.625m-3.75 0v1.5c0 .621.504 1.125 1.125 1.125m0 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m1.5-3.75C5.496 8.25 6 7.746 6 7.125v-1.5M4.875 8.25C5.496 8.25 6 8.754 6 9.375v1.5m0-5.25v5.25m0-5.25C6 5.004 6.504 4.5 7.125 4.5h9.75c.621 0 1.125.504 1.125 1.125m1.125 2.625h1.5m-1.5 0A1.125 1.125 0 0118 7.125v-1.5m1.125 2.625c-.621 0-1.125.504-1.125 1.125v1.5m2.625-2.625c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125M18 5.625v5.25M7.125 12h9.75m-9.75 0A1.125 1.125 0 016 10.875M7.125 12C6.504 12 6 12.504 6 13.125m0-2.25C6 11.496 5.496 12 4.875 12M18 10.875c0 .621-.504 1.125-1.125 1.125M18 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m-12 5.25v-5.25m0 5.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125m-12 0v-1.5c0-.621-.504-1.125-1.125-1.125M18 18.375v-5.25m0 5.25v-1.5c0-.621.504-1.125 1.125-1.125M18 13.125v1.5c0 .621.504 1.125 1.125 1.125M18 13.125c0-.621.504-1.125 1.125-1.125M6 13.125v1.5c0 .621-.504 1.125-1.125 1.125M6 13.125C6 12.504 5.496 12 4.875 12m-1.5 0h1.5m-1.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M19.125 12h1.5m0 0c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h1.5m14.25 0h1.5" />
                        </svg>
                      <% end %>
                    </div>
                  <% end %>
                </div>
                <div>
                  <div class="-mt-px flex divide-x divide-gray-200">
                    <div class="flex w-0 flex-1">
                      <a href="<%= polymorphic_path(media) %>" class="relative -mr-px inline-flex w-0 flex-1 items-center justify-center gap-x-3 rounded-bl-lg border border-transparent py-4 text-sm font-semibold text-gray-900">
                        View Details
                      </a>
                    </div>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
      
      <% if @books.blank? && @ebooks.blank? && @cds.blank? && @tapes.blank? && @audios.blank? && @videos.blank? %>
        <div class="text-center py-12">
          <p class="text-lg text-gray-600">No works found for this author.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>
