<div class="bg-white py-24 sm:py-32">
  <div class="mx-auto max-w-7xl px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:text-center">
      <h2 class="text-base font-semibold leading-7 text-indigo-600">Authors</h2>
      <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Human Design Authors</p>
      <p class="mt-6 text-lg leading-8 text-gray-600">Explore the authors who have contributed to the field of Human Design.</p>
    </div>
    
    <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
      <% if @authors.present? %>
        <ul role="list" class="grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          <% @authors.each do |author| %>
            <li class="overflow-hidden rounded-xl border border-gray-200">
              <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
                <% if author.profile_picture.attached? %>
                  <%= image_tag author.profile_picture, class: "h-12 w-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10" %>
                <% else %>
                  <div class="h-12 w-12 flex-none rounded-lg bg-indigo-500 text-white flex items-center justify-center">
                    <%= author.name.first.upcase %>
                  </div>
                <% end %>
                <div class="text-sm font-medium leading-6 text-gray-900"><%= author.name %></div>
              </div>
              <dl class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
                <div class="flex justify-between gap-x-4 py-3">
                  <dt class="text-gray-500">Email</dt>
                  <dd class="text-gray-700"><%= author.email.present? ? author.email : "N/A" %></dd>
                </div>
                <div class="flex justify-between gap-x-4 py-3">
                  <dt class="text-gray-500">Works</dt>
                  <dd class="flex items-start gap-x-2">
                    <div class="text-gray-700"><%= author.books.count + author.ebooks.count + author.cds.count + author.tapes.count + author.audios.count + author.videos.count %></div>
                  </dd>
                </div>
                <div class="pt-3">
                  <a href="<%= sfhdl_author_path(author) %>" class="text-indigo-600 hover:text-indigo-500">View Profile →</a>
                </div>
              </dl>
            </li>
          <% end %>
        </ul>
      <% else %>
        <div class="text-center py-12">
          <p class="text-lg text-gray-600">No authors found in the database.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>
