<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 bg-white py-10 rounded-2xl">
  <div class="mx-auto max-w-3xl">
    <div class="mb-8">
      <h1 class="text-2xl font-bold tracking-tight text-gray-900">Profile Settings</h1>
      <p class="mt-2 text-sm text-gray-600">Manage your personal information and preferences.</p>
    </div>
    <%= form_with(model: @user, url: users_settings_path, method: :patch, class: "space-y-8", local: true) do |f| %>
      <div class="space-y-8">
        <div>
          <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
            <div class="sm:col-span-6">
              <label class="block text-sm font-medium text-gray-700">Avatar</label>
              <div class="mt-2 flex items-center">
                <% if @user.avatar.attached? %>
                  <%= image_tag @user.avatar, class: "h-12 w-12 rounded-full object-cover" %>
                <% else %>
                  <span class="h-12 w-12 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  </span>
                <% end %>
                <div class="ml-4">
                  <%= f.file_field :avatar,
                      class: "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" %>
                </div>
              </div>
            </div>
            <div class="sm:col-span-4">
              <label class="block text-sm font-medium text-gray-700">Email</label>
              <div class="mt-1">
                <p class="text-sm text-gray-900"><%= @user.email %></p>
              </div>
            </div>
            <div class="sm:col-span-4">
              <%= f.label :name, class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1">
                <%= f.text_field :name, class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pt-6">
        <div class="flex justify-between items-center">
          <div>
            <% if current_user.speaker.present? %>
              <div class="text-sm text-gray-700">
                <span class="font-medium">Connected Speaker Profile:</span> <%= current_user.speaker.full_name %>
              </div>
            <% else %>
              <%= link_to "Connect Speaker Profile", new_users_speaker_connection_path, class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" %>
            <% end %>
          </div>
          <div>
            <%= f.submit "Save Changes", class: "ml-3 inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
