<div class="bg-white rounded-2xl">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10">
    <div class="mx-auto max-w-2xl lg:mx-0">
      <h1 class="text-3xl font-bold tracking-tight text-gray-900">Your Profile</h1>
      <p class="mt-2 text-lg leading-8 text-blue-600 underline"><%= link_to "Edit", edit_users_settings_path %></p>
    </div>
    <div class="mt-10">
      <div class="space-y-12">
        <div class="border-b border-gray-900/10 pb-12">
          <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="col-span-full">
              <div class="flex items-center gap-x-8">
                <% if current_user.avatar.attached? %>
                  <%= image_tag current_user.avatar, class: "size-24 rounded-lg object-cover" %>
                <% else %>
                  <span class="size-24 rounded-lg bg-gray-100 flex items-center justify-center text-2xl font-semibold text-gray-600">
                    <%= current_user.name.to_s[0] || current_user.email[0].upcase %>
                  </span>
                <% end %>
              </div>
            </div>
            <div class="sm:col-span-4">
              <div class="mt-6">
                <dl class="divide-y divide-gray-100">
                  <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt class="text-sm font-medium leading-6 text-gray-900">Name</dt>
                    <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                      <%= current_user.name %>
                    </dd>
                  </div>
                  <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt class="text-sm font-medium leading-6 text-gray-900">Email</dt>
                    <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                      <%= current_user.email %>
                    </dd>
                  </div>
                  <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt class="text-sm font-medium leading-6 text-gray-900">Member since</dt>
                    <dd class="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                      <%= current_user.created_at.strftime("%B %d, %Y") %>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>