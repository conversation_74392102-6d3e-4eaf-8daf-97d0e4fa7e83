<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 bg-white py-10 rounded-2xl">
  <div class="mx-auto max-w-3xl">
    <div class="mb-8">
      <h1 class="text-2xl font-bold tracking-tight text-gray-900">Connect Speaker Profile</h1>
      <p class="mt-2 text-sm text-gray-600">Connect your account to your speaker profile.</p>
    </div>

    <% if @speaker.nil? %>
      <div class="bg-gray-50 p-6 rounded-lg mb-6">
        <p class="text-gray-700 mb-4">Please enter the email address of the speaker profile you would like to connect.</p>
        
        <%= form_with(url: users_speaker_connection_path, method: :post, class: "space-y-4", local: true) do |f| %>
          <div>
            <%= f.label :email, "Email Address", class: "block text-sm font-medium text-gray-700" %>
            <div class="mt-1">
              <%= f.email_field :email, class: "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm", required: true %>
            </div>
          </div>
          
          <div class="flex justify-end">
            <%= f.submit "Connect Profile", class: "inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
          </div>
        <% end %>
      </div>
      
      <div class="text-sm text-gray-500">
        <p>If you run into any issues connecting your speaker profile, please contact <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-900">Jonah</a>.</p>
      </div>
    <% end %>
  </div>
</div>
