<div class="container mx-auto px-4 py-8">
  <div class="max-w-7xl mx-auto">
    <h1 class="text-3xl font-bold text-white mb-8">My Library</h1>
    <% if @purchased_products.any? %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr">
        <% @purchased_products.each do |purchase| %>
          <div class="bg-white rounded-lg shadow-lg overflow-hidden min-w-[400px]">
            <% if purchase.product.image.attached? %>
              <%= image_tag url_for(purchase.product.image), 
                    alt: purchase.product.title, 
                    class: "w-full h-48 object-cover" %>
            <% end %>
            <div class="p-6">
              <h2 class="text-xl font-semibold text-gray-900 mb-2">
                <%= purchase.product.title %>
              </h2>
              <p class="text-gray-600 mb-4">
                <%= purchase.product.short_description %>
              </p>
              <div class="space-y-3">
                <h3 class="font-medium text-gray-900">Download Links:</h3>
                <ul class="space-y-2">
                  <% purchase.product.download_links.each do |link| %>
                    <li class="flex items-center">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                      </svg>
                      <%= link_to link.title,
                            link.url,
                            class: "text-blue-600 hover:text-blue-800 hover:underline",
                            target: "_blank" %>
                    </li>
                  <% end %>
                </ul>
              </div>
              <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-500">
                  Purchased on: <%= purchase.created_at.strftime("%B %d, %Y") %>
                </p>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <h3 class="text-lg font-medium text-gray-900 mb-2">No purchases yet</h3>
        <p class="text-gray-500 mb-6">Browse our products to get started</p>
        <%= link_to "View Products", shop_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
      </div>
    <% end %>
  </div>
</div>
