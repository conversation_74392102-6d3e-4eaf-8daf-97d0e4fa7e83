<script src="https://accounts.google.com/gsi/client" async defer></script>
<script>
  function handleCredentialResponse(response) {
    console.log("Encoded JWT ID token: " + response.credential);

    fetch("/auth/google_login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify({ token: response.credential })
    })
    .then(response => response.json())
    .then(data => {
      console.log("Backend response:", data);
      if (data.success) {
        window.location.href = "/dashboard";
      } else {
        alert("Login failed");
      }
    })
    .catch(error => console.error("Error:", error));
  }

  window.onload = function () {
    google.accounts.id.initialize({
      client_id: "<%= ENV['GOOGLE_CLIENT_ID'] %>",
      callback: handleCredentialResponse,
    });
    google.accounts.id.renderButton(
      document.getElementById("g_id_signin"),
      { theme: "outline", size: "large" }
    );
  };
</script>
<meta name="csrf-token" content="<%= form_authenticity_token %>">
<div id="g_id_signin"></div>
