<div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">Create an account</h2>
  </div>
  <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]">
    <div class="bg-white px-6 py-12 shadow sm:rounded-lg sm:px-12">
      <%= render "devise/shared/google_sign_in_button" %>
      <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
        <% if resource.errors.any? %>
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-5 rounded relative" role="alert">
            <strong class="font-bold">Uh oh!</strong>
            <% resource.errors.full_messages.each do |msg| %>
              <span class="block sm:inline"><%= msg %>.</span><br>
            <% end %>
          </div>
        <% end %>
        <div class="space-y-6">
          <div>
            <%= f.label :email, class: "block text-sm font-medium leading-6 text-gray-900" %>
            <div class="mt-2">
              <%= f.email_field :email, autofocus: true, autocomplete: "email", required: true, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            </div>
          </div>
          <div>
            <%= f.label :password, class: "block text-sm font-medium leading-6 text-gray-900" %>
            <div class="mt-2">
              <%= f.password_field :password, autocomplete: "new-password", required: true, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
            </div>
          </div>
          <div class="mt-2">
            <%= f.label :password_confirmation, class: "block text-sm font-medium leading-6 text-gray-900" %>
            <%= f.password_field :password_confirmation, autocomplete: "new-password", required: true, class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" %>
          </div>
          <div class="mt-4 text-sm text-gray-600">
            <p>After creating your account, you will need to confirm your email address. We'll send you a confirmation link to activate your account.</p>
          </div>
          <div class="mt-4">
            <%= f.submit "Create account", class: "flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
          </div>
        </div>
      <% end %>
    </div>
    <p class="mt-10 text-center text-sm text-gray-500">
      Already have an account?
      <%= link_to 'Sign in', new_user_session_path, class: 'font-semibold leading-6 text-indigo-600 hover:text-indigo-500' %><br />
    </p>
  </div>
</div>
<div class="sm:mx-auto sm:w-full sm:max-w-md flex items-center justify-center space-x-4">
  <%= image_tag "logo.svg", class:"h-10 w-auto", alt: "Signpost" %>
  <%= image_tag "hdhd/high-desert-human-design-logo-mandala-black-grey-bg.png", class:"w-auto", alt: "High Desert Human Design" %>
  <%= image_tag "centerforhumandesign/the-center-for-human-design-logo.png", class:"w-auto", alt: "High Desert Human Design" %>
  <%= image_tag "sfhdl/sfhdl-logo.png", class:"w-auto", alt: "High Desert Human Design" %>
</div>
