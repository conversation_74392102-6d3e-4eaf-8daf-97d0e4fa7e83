<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Enneagram Trigroups</h1>
      <p class="mt-2 text-sm text-gray-700">Groupings of triads that share common characteristics and themes.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to enneagram_browser_path, class: "block rounded-md bg-purple-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Trigroup</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Triads</th>
                <th scope="col" class="relative px-6 py-3"><span class="sr-only">View</span></th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% @enneagram_trigroups.each do |trigroup| %>
                <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location='<%= enneagram_trigroup_path(trigroup) %>'">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <%= trigroup.id.split('-').map(&:capitalize).join(' ') %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= trigroup.name %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <% trigroup.triads.each_with_index do |triad_id, index| %>
                      <%= link_to triad_id, enneagram_triad_path(triad_id), class: "text-green-600 hover:text-green-900", onclick: "event.stopPropagation()" %><%= ", " unless index == trigroup.triads.length - 1 %>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <%= link_to "View", enneagram_trigroup_path(trigroup), class: "text-purple-600 hover:text-purple-900", onclick: "event.stopPropagation()" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <!-- Grid view for mobile -->
  <div class="mt-8 grid grid-cols-1 gap-4 sm:hidden">
    <% @enneagram_trigroups.each do |trigroup| %>
      <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
              <span class="text-xs font-medium text-white"><%= trigroup.id.split('-').first.first.upcase %></span>
            </div>
          </div>
          <div class="min-w-0 flex-1">
            <span class="absolute inset-0" aria-hidden="true"></span>
            <p class="text-sm font-medium text-gray-900"><%= trigroup.name %></p>
            <p class="truncate text-sm text-gray-500">Triads: <%= trigroup.triads.join(', ') %></p>
          </div>
          <div class="flex-shrink-0">
            <%= link_to enneagram_trigroup_path(trigroup), class: "inline-flex items-center rounded-md bg-white px-2.5 py-1.5 text-xs font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
              View
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
