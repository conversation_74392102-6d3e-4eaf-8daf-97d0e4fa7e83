<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900"><%= @enneagram_trigroup.name %></h1>
      <p class="mt-2 text-sm text-gray-700">Trigroup details and component triads.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-3">
      <%= link_to enneagram_trigroups_path, class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        ← Back to Trigroups
      <% end %>
      <%= link_to enneagram_browser_path, class: "inline-flex items-center rounded-md bg-purple-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Trigroup Details -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-16 w-16 rounded-full bg-purple-500 flex items-center justify-center">
              <span class="text-sm font-bold text-white"><%= @enneagram_trigroup.id.split('-').first.first.upcase %></span>
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg font-medium text-gray-900"><%= @enneagram_trigroup.name %></h3>
            <p class="text-sm text-gray-500"><%= @enneagram_trigroup.id.split('-').map(&:capitalize).join(' ') %></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Component Triads -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Component Triads</h2>
    <p class="text-sm text-gray-600 mb-4">The triads that belong to this trigroup:</p>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <% @enneagram_trigroup.triad_objects.each do |triad| %>
        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
          <%= link_to "", enneagram_triad_path(triad), class: "absolute inset-0" %>
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                <span class="text-xs font-medium text-white"><%= triad.id %></span>
              </div>
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900">The <%= triad.id %> Triad</p>
              <p class="text-xs text-gray-500"><%= triad.name %></p>
              <p class="text-xs text-gray-500">
                Types: <%= triad.enneatypes.join(', ') %>
              </p>
            </div>
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Trigroup Composition -->
  <div class="mt-8">
    <div class="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Trigroup Composition</h3>
      <div class="flex items-center space-x-4">
        <span class="text-sm text-gray-600">This trigroup consists of triads:</span>
        <div class="flex space-x-2">
          <% @enneagram_trigroup.triads.each do |triad_id| %>
            <%= link_to triad_id, enneagram_triad_path(triad_id), class: "inline-flex items-center rounded-full bg-green-100 px-3 py-0.5 text-sm font-medium text-green-800 hover:bg-green-200" %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
