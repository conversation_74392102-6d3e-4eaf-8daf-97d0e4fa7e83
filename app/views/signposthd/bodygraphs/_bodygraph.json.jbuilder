json.extract! bodygraph, :id, :name, :birth_name, :birth_date_local, :birth_date_utc, :design_date_utc, :birth_country, :birth_city, :type, :inner_authority, :definition, :profile, :incarnation_cross, :determination, :environment, :view, :motivation, :cognition, :sense, :variable, :gates, :channels, :personality_activations, :design_activations, :head_defined, :ajna_defined, :throat_defined, :spleen_defined, :solar_plexus_defined, :g_center_defined, :sacral_defined, :root_defined, :ego_defined, :personality_nodes_tone, :design_nodes_tone, :user_id, :created_at, :updated_at, :birth_date, :birth_time, :birth_data_source, :birth_data_source_notes, :birth_data_source, :birth_data_collector, :rodden_rating, :gender, :notable_for, :profession, :famous, :historical_event, :portrait, :all_activated_gates
json.url bodygraph_url(bodygraph, format: :json)
