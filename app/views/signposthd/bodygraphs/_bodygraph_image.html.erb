<%
  # Define center colors - using the original Signpost colors
  center_colors = ['#F9F6C4', '#48BB78', '#3D2A1F', '#3D2A1F', '#F56565', '#F9F6C4', '#3D2A1F', '#B71C1C', '#3D2A1F']

  # Define center opacity setting
  center_opacity = 0.9

  # Generate a unique ID for this bodygraph instance
  unique_id = "bodygraph-#{activations_to_show}-#{SecureRandom.hex(4)}"
%>
<div style="position: relative; left: 99px;">
  <%= render partial: 'shared/bodygraph_renderer', locals: {
    id: unique_id,
    personality_activations_json: personalityActivations.to_json,
    design_activations_json: designActivations.to_json,
    mode: activations_to_show,
    show_metadata: false,
    center_colors: center_colors,
    center_opacity: center_opacity
  } %>
</div>
<script>
  // This script adds the gate labels to the bodygraph
  document.addEventListener('DOMContentLoaded', function() {
    const activationsToShow = '<%= activations_to_show %>';
    let activations;

    if (activationsToShow === 'all') {
      activations = allActivations;
    } else if (activationsToShow === 'design') {
      activations = allActivations.filter(a => a.personalityOrDesign === 'design');
    } else if (activationsToShow === 'personality') {
      activations = allActivations.filter(a => a.personalityOrDesign === 'personality');
    } else if (activationsToShow === 'incarnation-cross') {
      activations = [];
      if (personalityActivations['Sun']) activations.push({...personalityActivations['Sun'], personalityOrDesign: 'personality', planet: 'Sun'});
      if (personalityActivations['Earth']) activations.push({...personalityActivations['Earth'], personalityOrDesign: 'personality', planet: 'Earth'});
      if (designActivations['Sun']) activations.push({...designActivations['Sun'], personalityOrDesign: 'design', planet: 'Sun'});
      if (designActivations['Earth']) activations.push({...designActivations['Earth'], personalityOrDesign: 'design', planet: 'Earth'});
    }

    // Fill in the labels
    activations.forEach(activation => {
      const gate = activation.g;
      const line = activation.l;
      const labelElement = document.getElementById(`bodygraph-label-${activation.personalityOrDesign}-${activation.planet}`);
      if (labelElement) {
        labelElement.innerHTML = `${gate}.${line}`;
      }
    });
  });
</script>
