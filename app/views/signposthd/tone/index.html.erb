<div class="bg-white shadow overflow-hidden sm:rounded-lg">
  <div class="border-t border-gray-200">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tone</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sense</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cognition</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mo<PERSON>ter</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tonal Binary</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Binary</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Awareness Stream</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @tone_data.each do |tone_key, tone| %>
          <tr onclick="window.location='<%= tone_path(tone_key) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone_key %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:sense] %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:cognition] %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:mood_setter] %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:tonal_binary] %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:binary] %></td>
            <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= tone[:awareness_stream] %></td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
              <a href="<%= tone_path(tone_key) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
