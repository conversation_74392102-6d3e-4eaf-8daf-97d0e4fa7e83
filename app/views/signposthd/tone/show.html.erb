  
<div class="max-w-4xl mx-auto py-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Tone Information</h3>
    </div>
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Tone Key</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= params[:tone] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Sense</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:sense] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Cognition</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:cognition] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Mood Setter</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:mood_setter] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Tonal Binary</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:tonal_binary] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Binary</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:binary] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Awareness Stream</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @tone[:awareness_stream] %></dd>
        </div>
      </dl>
    </div>
  </div>
  <div class="mt-6">
    <%= link_to 'Back to All Tones', tones_path, class: 'text-indigo-600 hover:text-indigo-900' %>
  </div>
</div>
