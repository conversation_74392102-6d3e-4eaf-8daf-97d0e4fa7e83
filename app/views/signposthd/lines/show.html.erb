<div class="max-w-4xl mx-auto py-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Line Information</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about Line <%= params[:line] %>.</p>
    </div>
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Line</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= params[:line] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Name</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @line[:name] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Profiles</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @line[:profiles].join(', ') %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Harmonic</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= link_to @line[:harmonic], line_path(@line[:harmonic]), class: 'text-indigo-600 hover:text-indigo-900' %>
          </dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Quality</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @line[:quality] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Triplicity</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @line[:triplicity] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Reactivity</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @line[:reactivity] %></dd>
        </div>
      </dl>
    </div>
  </div>
  <div class="mt-6">
    <%= link_to 'Back to All Lines', lines_path, class: 'text-indigo-600 hover:text-indigo-900' %>
  </div>
</div>
