<table class="min-w-full divide-y divide-gray-200">
  <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Line</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profiles</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harmonic</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quality</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Triplicity</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reactivity</th>
      <th scope="col" class="relative px-6 py-3"><span class="sr-only">Show</span></th>
    </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200">
    <% @lines_data.each do |line_key, line| %>
      <tr onclick="window.location='<%= line_path(line_key) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line_key %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:name] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:profiles].join(', ') %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:harmonic] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:quality] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:triplicity] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= line[:reactivity] %></td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
          <a href="<%= line_path(line_key) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>
