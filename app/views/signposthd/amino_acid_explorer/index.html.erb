<!-- app/views/amino_acid_explorer/index.html.erb -->
<h1>Amino Acid Index</h1>

<!-- Amino Acid List -->
<ul>
  <% @amino_acids_data.each do |acid, data| %>
    <li><%= link_to data[:name], amino_acid_path(acid) %></li>
  <% end %>
</ul>

<!-- Gate Table -->
<h2>Gates</h2>
<div class="flex items-center justify-center mt-4">
  <label class="mr-2">Sort Order:</label>
  <select id="sortOrder" class="border p-1">
    <option value="kingWen">King Wen Order</option>
    <option value="raveMandala">Rave Mandala Order</option>
  </select>
</div>

<table class="border-collapse w-full mt-4">
  <thead>
    <tr>
      <th class="border p-2">Gate</th>
      <th class="border p-2">Nucleic Acid Sequence</th>
      <th class="border p-2">Gate Symbol</th>
    </tr>
  </thead>
  <tbody id="gatesTableBody">
    <!-- Table content will be dynamically populated using JavaScript -->
  </tbody>
</table>

<script>
 // uses iChingHexagramGlyphs from helpers.js
 // uses gateOrder from helpers.js

  const glyphs = iChingHexagramGlyphs;
  const raveMandalaOrder = gateOrder;

  function populateGatesTable(order) {
    const tableBody = document.getElementById('gatesTableBody');
    tableBody.innerHTML = '';

    const sortedGates = order === 'kingWen' ? Object.keys(glyphs) : raveMandalaOrder;

    sortedGates.forEach((gate) => {
      const row = document.createElement('tr');
      const gateSymbol = iChingHexagramGlyphs[gate];
      const nucleicAcidSequence = nucleicAcidSequences[gate];

      row.innerHTML = `
        <td class="border p-2">${gate}</td>
        <td class="border p-2">${nucleicAcidSequence}</td>
        <td class="border p-2">${gateSymbol}</td>
      `;

      tableBody.appendChild(row);
    });
  }

  
  // Initial population with King Wen Order
  populateGatesTable('kingWen');

  // Add event listener for changing sort order
  document.getElementById('sortOrder').addEventListener('change', function () {
    const selectedSortOrder = this.value;
    populateGatesTable(selectedSortOrder);
  });
</script>
