<div class="p-5">
  <div class="relative container mx-auto">
    <h1 class="text-3xl font-bold my-4">Audio Player</h1>
    <h1 class="text-2xl font-bold mb-4"><%= @current_audio[:title] %></h1>
    <audio controls>
      <source src="<%= @current_audio[:url] %>" type="audio/x-m4a">
    </audio>
    <ul class="divide-y divide-gray-200 overflow-y-scroll bg-gray-200 mt-5 rounded-lg relative" style="margin-left: -18px; padding: 18px 18px 56px 18px; max-height: 400px;">
      <% @audio_data.each do |audio| %>
        <li class="py-4">
          <div class="flex items-center justify-between <%= audio[:id] == @current_audio[:id] ? 'underline' : '' %>">
            <div class="text-sm font-semibold text-blue-500"><a href="/audio-player/<%= audio[:id] %>"><%= audio[:title] %></a></div>
            <div class="text-sm">
              <a href="<%= audio[:url] %>" class="text-gray-400 hover:text-blue-600">Download</a>
            </div>
          </div>
        </li>
      <% end %>
    </ul>
      <!-- Add the fade effect -->
      <div class="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-gray-200 to-transparent"></div>
  </div>
</div>