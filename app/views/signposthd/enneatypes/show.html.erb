<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900"><%= @enneatype.name %></h1>
      <p class="mt-2 text-sm text-gray-700">Enneatype <%= @enneatype.id %> details and related triads.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-3">
      <%= link_to enneatypes_path, class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        ← Back to Enneatypes
      <% end %>
      <%= link_to enneagram_browser_path, class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Enneatype Details -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-16 w-16 rounded-full bg-indigo-500 flex items-center justify-center">
              <span class="text-xl font-bold text-white"><%= @enneatype.id %></span>
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg font-medium text-gray-900"><%= @enneatype.name %></h3>
            <p class="text-sm text-gray-500">Enneatype <%= @enneatype.id %></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Related Triads -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Related Triads</h2>
    <p class="text-sm text-gray-600 mb-4">Triads that include Enneatype <%= @enneatype.id %>:</p>
    <% if @enneatype.triads.any? %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <% @enneatype.triads.each do |triad| %>
          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <%= link_to "", enneagram_triad_path(triad), class: "absolute inset-0" %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white"><%= triad.id %></span>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium text-gray-900">The <%= triad.id %> Triad</p>
                <p class="text-xs text-gray-500"><%= triad.name %></p>
                <p class="text-xs text-gray-500">
                  Types: <%= triad.enneatypes.join(', ') %>
                </p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No related triads</h3>
        <p class="mt-1 text-sm text-gray-500">This enneatype is not part of any triads.</p>
      </div>
    <% end %>
  </div>
  <!-- Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from triads that include Enneatype <%= @enneatype.id %>:</p>
    <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
      <% @enneatype.qualities_with_triads.each do |quality_data| %>
        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
          <%= link_to quality_data[:name], enneagram_triad_path(quality_data[:triad]), 
              class: "text-sm font-medium text-green-800 hover:text-green-900" %>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Missing Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Missing Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from triads that do not include Enneatype <%= @enneatype.id %>:</p>
    <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
      <% @enneatype.missing_qualities_with_triads.each do |quality_data| %>
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-3">
          <%= link_to quality_data[:name], enneagram_triad_path(quality_data[:triad]), 
              class: "text-sm font-medium text-gray-600 hover:text-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Trigroups Table -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Trigroups</h2>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <% @enneatype.trigroup_data.each do |trigroup| %>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <%= link_to trigroup[:name], enneagram_trigroup_path(trigroup[:id]), 
                    class: "hover:text-gray-700" %>
              </th>
            <% end %>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% 3.times do |row_index| %>
            <tr>
              <% @enneatype.trigroup_data.each do |trigroup| %>
                <td class="px-3 py-4 whitespace-nowrap text-sm">
                  <% if trigroup[:triads][row_index] %>
                    <% triad = trigroup[:triads][row_index] %>
                    <div class="<%= triad[:belongs_to] ? 'bg-indigo-100 border-indigo-300 text-indigo-800' : 'bg-white border-gray-200 text-gray-700' %> border rounded-lg p-2">
                      <%= link_to triad[:name], enneagram_triad_path(triad[:id]), 
                          class: "text-sm font-medium #{'text-indigo-800 hover:text-indigo-900' if triad[:belongs_to]} #{'text-gray-700 hover:text-gray-900' unless triad[:belongs_to]}" %>
                    </div>
                  <% end %>
                </td>
              <% end %>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
  <!-- Compare To Other Enneatypes -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Compare To Other Enneatypes</h2>
    <div class="mb-6">
      <label for="compare-select" class="block text-sm font-medium text-gray-700 mb-2">Compare to:</label>
      <select id="compare-select" class="block w-48 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
        <% (1..9).each do |type_id| %>
          <option value="<%= type_id %>" <%= 'selected' if type_id == @enneatype.id %>>
            Enneatype <%= type_id %>
          </option>
          <% end %>
        </select>
      </div>
      <!-- Same Qualities -->
      <div id="shared-qualities" class="mb-8">
        <h3 class="text-md font-medium text-gray-900 mb-4">Shared Qualities</h3>
        <div id="same-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Current Has That Other Lacks -->
      <div id="current-unique-section" class="mb-8">
        <h3 id="current-unique-title" class="text-md font-medium text-gray-900 mb-4"></h3>
        <div id="current-unique-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Other Has That Current Lacks -->
      <div id="other-unique-section" class="mb-8">
        <h3 id="other-unique-title" class="text-md font-medium text-gray-900 mb-4"></h3>
        <div id="other-unique-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Neither Have -->
      <div class="mb-8">
        <h3 id="neither-title" class="text-md font-medium text-gray-900 mb-4">Missing Qualities</h3>
        <div id="neither-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
    </div>
    <script>
      // Enneatype qualities data
      const enneatypeQualities = {
        <% (1..9).each do |type_id| %>
          <% enneatype = Enneatype.find(type_id) %>
          <%= type_id %>: {
            qualities: <%= enneatype.qualities_with_triads.map { |q| { name: q[:name], triad_id: q[:triad].id } }.to_json.html_safe %>,
            name: '<%= enneatype.name %>'
          }<%= ',' unless type_id == 9 %>
        <% end %>
      };

      const currentEnneatypeId = <%= @enneatype.id %>;

      function createQualityElement(quality, colorClass) {
        const div = document.createElement('div');
        div.className = `${colorClass} border rounded-lg p-3`;

        const link = document.createElement('a');
        link.href = `/enneagram/triads/${quality.triad_id}`;
        link.className = 'text-sm font-medium hover:underline';
        link.textContent = quality.name;

        div.appendChild(link);
        return div;
      }

      function updateComparison() {
        const compareToId = parseInt(document.getElementById('compare-select').value);
        const currentQualities = enneatypeQualities[currentEnneatypeId].qualities;
        const compareToQualities = enneatypeQualities[compareToId].qualities;

        const currentQualityNames = currentQualities.map(q => q.name);
        const compareToQualityNames = compareToQualities.map(q => q.name);

        // Same qualities
        const sameQualities = currentQualities.filter(q =>
          compareToQualityNames.includes(q.name)
        );

        // Current unique qualities
        const currentUniqueQualities = currentQualities.filter(q =>
          !compareToQualityNames.includes(q.name)
        );

        // Compare to unique qualities
        const compareToUniqueQualities = compareToQualities.filter(q =>
          !currentQualityNames.includes(q.name)
        );

        // Neither have qualities (all qualities minus both sets)
        const allQualities = [];
        Object.values(enneatypeQualities).forEach(type => {
          type.qualities.forEach(q => {
            if (!allQualities.find(existing => existing.name === q.name)) {
              allQualities.push(q);
            }
          });
        });

        const neitherQualities = allQualities.filter(q =>
          !currentQualityNames.includes(q.name) && !compareToQualityNames.includes(q.name)
        );

        // Update Same Qualities
        const sameContainer = document.getElementById('same-qualities');
        sameContainer.innerHTML = '';
        if (sameQualities.length > 0) {
          document.getElementById('shared-qualities').style.display = 'block';
          sameQualities.forEach(quality => {
            sameContainer.appendChild(createQualityElement(quality, 'bg-blue-50 border-blue-200'));
          });
        } else {
          document.getElementById('shared-qualities').style.display = 'none';
        }

        // Update sections visibility and content
        const isSameType = currentEnneatypeId === compareToId;

        // Current unique section
        const currentUniqueSection = document.getElementById('current-unique-section');
        const currentUniqueTitle = document.getElementById('current-unique-title');
        const currentUniqueContainer = document.getElementById('current-unique-qualities');

        if (isSameType || currentUniqueQualities.length === 0) {
          currentUniqueSection.style.display = 'none';
        } else {
          currentUniqueSection.style.display = 'block';
          currentUniqueTitle.textContent = `Qualities ${currentEnneatypeId} Has That ${compareToId} Lacks`;
          currentUniqueContainer.innerHTML = '';
          currentUniqueQualities.forEach(quality => {
            currentUniqueContainer.appendChild(createQualityElement(quality, 'bg-green-50 border-green-200'));
          });
        }

        // Compare to unique section
        const otherUniqueSection = document.getElementById('other-unique-section');
        const otherUniqueTitle = document.getElementById('other-unique-title');
        const otherUniqueContainer = document.getElementById('other-unique-qualities');

        if (isSameType || compareToUniqueQualities.length === 0) {
          otherUniqueSection.style.display = 'none';
        } else {
          otherUniqueSection.style.display = 'block';
          otherUniqueTitle.textContent = `Qualities ${compareToId} Has That ${currentEnneatypeId} Lacks`;
          otherUniqueContainer.innerHTML = '';
          compareToUniqueQualities.forEach(quality => {
            otherUniqueContainer.appendChild(createQualityElement(quality, 'bg-orange-50 border-orange-200'));
          });
        }

        // Neither section
        const neitherContainer = document.getElementById('neither-qualities');

        neitherContainer.innerHTML = '';
        neitherQualities.forEach(quality => {
          neitherContainer.appendChild(createQualityElement(quality, 'bg-gray-50 border-gray-200'));
        });
      }

      // Initialize comparison on page load
      document.addEventListener('DOMContentLoaded', function() {
        updateComparison();

        // Add event listener for dropdown changes
        document.getElementById('compare-select').addEventListener('change', updateComparison);
      });
    </script>
  </div>
