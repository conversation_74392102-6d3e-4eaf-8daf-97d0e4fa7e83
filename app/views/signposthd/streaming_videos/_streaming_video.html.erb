<div id="<%= dom_id streaming_video %>" style="color: white; position: relative;">
  <% if streaming_video.cover.attached? %>
    <%= image_tag rails_blob_path(streaming_video.cover, disposition: "attachment"), style: "min-height: 500px; width: 100%; object-fit: cover; display: flex; align-items: center; justify-content: center; max-height: 620px; overflow: hidden;" %>
  <% else %>
    <%= image_tag 'streaming-video-default-cover.png', style: "min-height: 500px; width: 100%; object-fit: cover; display: flex; align-items: center; justify-content: center;" %>
  <% end %>
  <div class="absolute left-10 lg:left-48" style="top: 240px; max-width: 640px;">
    <h2 style="font-size: 22px; text-transform: uppercase; font-family: Roboto Mono, Andale Mono; font-weight: bold;">
      <%= streaming_video.title %>
    </h2>
    <div style="margin-top: 15px;">
      <% if 1.month.ago <= streaming_video.created_at %>
        <%= image_tag "icon-streaming-video-new-arrival.png", class: "h-8 w-auto", style: "float: left;" %>
      <% end %>
      <% if streaming_video.is_4k %>
        <%= image_tag "icon-streaming-video-4k.png", class: "h-8 w-auto", style: "float: left;" %>
      <% end %>
      <%= image_tag "icon-streaming-video-duration.png", class: "h-8 w-auto ml-3", style: "float: left;" %>
      <span class="h-8 w-auto" style="position: relative; top: 3px">
        <%= "#{streaming_video.runtime_minutes / 60}h#{format('%02d', streaming_video.runtime_minutes % 60)}m" if streaming_video.runtime_minutes.present? %>
      </span>
      <span class="h-8 w-auto ml-3" style="display: none; text-transform: uppercase; position: relative; top: 3px;">
        #profile #5thline #hdhd2023
      </span>
    </div>
    <p style="clear: left; margin-top: 25px; width: 650px; line-height: 1.5;">
      <%= streaming_video.short_description %>
    </p>
    <%= link_to watch_streaming_video_path(streaming_video), target: "_blank", class: "lg:absolute", style: "display: block; margin-top: 10px; right: -190px; top: 60px;" do %>
      <%= image_tag "icon-streaming-video-watch-now.png", style: "height: 42px; width: 134px;" %>
    <% end %>
  </div>
  <% if action_name != "show" and action_name != "landing_page" %>
    <%= link_to "Show this streaming video", streaming_video, class: "text-black rounded-lg my-6 py-3 px-5 bg-gray-100 inline-block font-medium" %>
    <%= link_to 'Edit this streaming video', edit_streaming_video_path(streaming_video), class: "text-black rounded-lg py-3 ml-2 px-5 bg-gray-100 inline-block font-medium" %>
    <hr class="mt-6">
  <% end %>
</div>
