<%= form_with(model: streaming_video, class: "contents") do |f| %>
  <% if @streaming_video.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(@streaming_video.errors.count, "error") %> prohibited this streaming video from being saved:</h2>
      <ul>
        <% @streaming_video.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div class="mt-10 space-y-8 border-b border-gray-900/10 pb-12 sm:space-y-0 sm:divide-y sm:divide-gray-900/10 sm:border-t sm:pb-0">
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :title, class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.text_field :title %>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :is_4k, class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.check_box :is_4k %>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :runtime_minutes, class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.number_field :runtime_minutes %>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :url, class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.text_field :url %>
        <p class="text-gray-400 mt-2 text-xs">Example: https://youtu.be/mRaURFfQJbE</p>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :short_description, class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.text_area :short_description %>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :cover, 'Cover Image', class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.file_field :cover %>
      </div>
    </div>
    <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
      <%= f.label :cover_small, 'Cover Image (Small)', class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
      <div class="mt-2 sm:col-span-2 sm:mt-0">
        <%= f.file_field :cover_small %>
      </div>
    </div>
<div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-6">
  <%= f.label :year, 'Year', class: "block text-sm font-medium leading-6 text-gray-900 sm:pt-1.5" %>
  <div class="mt-2 sm:col-span-2 sm:mt-0">
    <%= f.number_field :year, class: "form-input rounded-md shadow-sm mt-1 block w-full sm:text-sm sm:leading-5" %>
  </div>
</div>
    <div class="actions ml-3 py-6">
      <%= f.submit "Update Streaming Video", class: "bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" %>
    </div>
  </div>
<% end %>
