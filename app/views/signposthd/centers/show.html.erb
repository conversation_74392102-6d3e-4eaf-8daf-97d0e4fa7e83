<div class="max-w-3xl mx-8 py-8">
  <h1 class="font-bold text-4xl mb-6"><%= @center[:name] %> <%= ['G Center', 'Solar Plexus', 'Spleen', 'Sacral'].include?(@center[:name]) ? '' : 'Center' %></h1>
  <div class="mb-8">
    <h2 class="font-semibold text-xl mb-2">Gates:</h2>
    <p>
      <% @center[:gates].each_with_index do |gate, index| %>
        <%= link_to "#{gate}", gate_path(gate), class: "text-blue-500 hover:underline" %><% if index < @center[:gates].size - 1 %>, <% end %>
      <% end %>
    </p>
  </div>
  <div class="mb-8">
    <h2 class="font-semibold text-xl mb-2">Channels:</h2>
    <ul class="list-disc list-inside">
      <% @center[:channels].each do |channel| %>
        <li><%= link_to channel, channel_path(channel), class: "text-blue-500 hover:underline" %></li>
      <% end %>
    </ul>
  </div>
  <div>
    <h2 class="font-semibold text-xl mb-2">Circuits:</h2>
    <p>
      <% @center[:circuits].each_with_index do |circuit, index| %>
        <%= circuit %><% if index < @center[:circuits].size - 1 %>, <% end %>
      <% end %>
    </p>
  </div>
</div>
