<div class="w-full">
  <div id="bodygraphs" class="min-w-full">
    <% if !@bodygraphs.empty? %>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authority</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Definition</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profile</th>
            <th scope="col" class="relative px-6 py-3"><span class="sr-only">Show</span></th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @bodygraphs.each do |bodygraph| %>
            <tr onclick="window.location='<%= public_bodygraph_path(bodygraph) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
              <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= bodygraph.name %></td>
              <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= bodygraph.aura_type %></td>
              <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= bodygraph.inner_authority %></td>
              <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= bodygraph.definition %></td>
              <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= bodygraph.profile %></td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
                <a href="<%= public_bodygraph_path(bodygraph) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
      <div class="mt-6">
        <%= link_to new_bodygraph_path, class: 'button inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 ml-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600' do %>
          <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
          </svg>
          New Bodygraph
        <% end %>
      </div>
    <% end %>
    <% if @bodygraphs.empty? %>
      <div class="text-center pt-6">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <circle cx="12" cy="6" r="2"></circle>
          <path d="M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No bodygraphs</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding your first bodygraph.</p>
        <div class="mt-6">
          <%= link_to new_bodygraph_path, class: 'button inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600' do %>
            <svg class="-ml-0.5 mr-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z" />
            </svg>
            New Bodygraph
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
