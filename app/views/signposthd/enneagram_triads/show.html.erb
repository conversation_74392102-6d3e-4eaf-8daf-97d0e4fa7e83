<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900"><%= @enneagram_triad.name %></h1>
      <p class="mt-2 text-sm text-gray-700">Triad <%= @enneagram_triad.id %> details and component enneatypes.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-3">
      <%= link_to enneagram_triads_path, class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        ← Back to Triads
      <% end %>
      <%= link_to enneagram_browser_path, class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Triad Details -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-16 w-16 rounded-full bg-green-500 flex items-center justify-center">
              <span class="text-sm font-bold text-white"><%= @enneagram_triad.id %></span>
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg font-medium text-gray-900"><%= @enneagram_triad.name %></h3>
            <p class="text-sm text-gray-500">Triad <%= @enneagram_triad.id %></p>
            <p class="text-sm text-gray-600 italic"><%= @enneagram_triad.nickname %></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Component Enneatypes -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Component Enneatypes</h2>
    <p class="text-sm text-gray-600 mb-4">The enneatypes that make up this triad:</p>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <% @enneagram_triad.enneatype_objects.each do |enneatype| %>
        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
          <%= link_to "", enneatype_path(enneatype), class: "absolute inset-0" %>
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center">
                <span class="text-sm font-medium text-white"><%= enneatype.id %></span>
              </div>
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900"><%= enneatype.name %></p>
              <p class="text-xs text-gray-500">Type <%= enneatype.id %></p>
            </div>
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Trigroup -->
  <% if @enneagram_triad.trigroup %>
    <div class="mt-8">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Trigroup</h2>
      <p class="text-sm text-gray-600 mb-4">This triad belongs to:</p>
      <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400 max-w-md">
        <%= link_to "", enneagram_trigroup_path(@enneagram_triad.trigroup), class: "absolute inset-0" %>
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
              <span class="text-xs font-medium text-white"><%= @enneagram_triad.trigroup.id.split('-').first.first.upcase %></span>
            </div>
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900"><%= @enneagram_triad.trigroup.name %></p>
            <p class="text-xs text-gray-500">Trigroup</p>
          </div>
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <!-- Enneatype Statements -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Enneatype Statements</h2>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <% @enneagram_triad.enneatypes.each do |enneatype_id| %>
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center mb-4">
            <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white"><%= enneatype_id %></span>
            </div>
            <h3 class="ml-3 text-lg font-medium text-gray-900">Type <%= enneatype_id %></h3>
          </div>
          <div class="space-y-2">
            <% @enneagram_triad.statements_for_enneatype(enneatype_id).each do |statement| %>
              <p class="text-sm text-gray-700 italic">"<%= statement %>"</p>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Primary Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Primary Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from other triads that share stems with this triad:</p>
    <% qualities = @enneagram_triad.qualities %>
    <% if qualities.any? %>
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
        <% qualities.each do |quality| %>
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <%= link_to quality[:name], enneagram_triad_path(quality[:triad_id]), class: "text-sm font-medium text-green-600 hover:text-green-800" %>
                <span class="text-sm text-gray-500"> via </span>
                <%= link_to quality[:stem_id], enneagram_stem_path(quality[:stem_id]), class: "text-sm font-medium text-orange-600 hover:text-orange-800" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="text-sm text-gray-500">No shared qualities found.</p>
    <% end %>
  </div>
  <!-- Secondary Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Secondary Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from other triads that have a single fix from this triad:</p>
    <% secondary_qualities = @enneagram_triad.secondary_qualities %>
    <% if secondary_qualities.any? %>
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
        <% secondary_qualities.each do |quality| %>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <%= link_to quality[:name], enneagram_triad_path(quality[:triad]), class: "text-sm font-medium text-blue-600 hover:text-blue-800" %>
                <span class="text-sm text-gray-500"> via </span>
                <span class="text-sm font-medium text-indigo-600"><%= quality[:shared_enneatype] %></span>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="text-sm text-gray-500">No secondary qualities found.</p>
    <% end %>
  </div>
  <!-- Missing Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Missing Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from other triads that do not belong to this triad:</p>
    <% missing_qualities = @enneagram_triad.missing_qualities %>
    <% if missing_qualities.any? %>
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
        <% missing_qualities.each do |quality| %>
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <%= link_to quality[:name], enneagram_triad_path(quality[:triad]), class: "text-sm font-medium text-gray-600 hover:text-gray-800" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="text-sm text-gray-500">No missing qualities found.</p>
    <% end %>
  </div>
  <!-- Related Stems -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Related Stems</h2>
    <p class="text-sm text-gray-600 mb-4">Stems that are formed by pairs within this triad:</p>
    <% stems = EnneagramStem.for_triad(@enneagram_triad) %>
    <% if stems.any? %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <% stems.each do |stem| %>
          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <%= link_to "", enneagram_stem_path(stem), class: "absolute inset-0" %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white"><%= stem.id %></span>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium text-gray-900"><%= stem.name %></p>
                <p class="text-xs text-gray-500">
                  <%= stem.centers %> | Types: <%= stem.enneatypes.join(', ') %>
                </p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No related stems</h3>
        <p class="mt-1 text-sm text-gray-500">This triad does not have any related stems.</p>
      </div>
    <% end %>
  </div>
  <!-- Compare To Other Triads -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Compare To Other Triads</h2>
    <div class="mb-6">
      <label for="compare-select" class="block text-sm font-medium text-gray-700 mb-2">Compare to:</label>
      <select id="compare-select" class="block w-48 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm">
        <% EnneagramTriad.all.each do |triad| %>
          <option value="<%= triad.id %>" <%= 'selected' if triad.id == @enneagram_triad.id %>>
            <%= triad.id %> - <%= triad.name %>
          </option>
          <% end %>
        </select>
      </div>
      <!-- Shared Primary Qualities -->
      <div id="shared-primary-qualities" class="mb-8">
        <h3 class="text-md font-medium text-gray-900 mb-4">Shared Primary Qualities</h3>
        <div id="same-primary-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Shared Secondary Qualities -->
      <div id="shared-secondary-qualities" class="mb-8">
        <h3 class="text-md font-medium text-gray-900 mb-4">Shared Secondary Qualities</h3>
        <div id="same-secondary-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Current Has That Other Lacks -->
      <div id="current-unique-section" class="mb-8">
        <h3 id="current-unique-title" class="text-md font-medium text-gray-900 mb-4"></h3>
        <div id="current-unique-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Other Has That Current Lacks -->
      <div id="other-unique-section" class="mb-8">
        <h3 id="other-unique-title" class="text-md font-medium text-gray-900 mb-4"></h3>
        <div id="other-unique-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
      <!-- Qualities Neither Have -->
      <div class="mb-8">
        <h3 id="neither-title" class="text-md font-medium text-gray-900 mb-4">Missing Qualities</h3>
        <div id="neither-qualities" class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Populated by JavaScript -->
        </div>
      </div>
    </div>
    <script>
      // Triad qualities data
      const triadQualities = {
        <% EnneagramTriad.all.each_with_index do |triad, index| %>
          '<%= triad.id %>': {
            primaryQualities: <%= triad.qualities_with_triads.map { |q| { name: q[:name], triad_id: q[:triad].id } }.to_json.html_safe %>,
            secondaryQualities: <%= triad.secondary_qualities.map { |q| { name: q[:name], triad_id: q[:triad].id } }.to_json.html_safe %>,
            name: '<%= triad.name %>'
          }<%= ',' unless index == EnneagramTriad.all.length - 1 %>
        <% end %>
      };

      const currentTriadId = '<%= @enneagram_triad.id %>';

      function createQualityElement(quality, colorClass) {
        const div = document.createElement('div');
        div.className = `${colorClass} border rounded-lg p-3`;

        const link = document.createElement('a');
        link.href = `/enneagram/triads/${quality.triad_id}`;
        link.className = 'text-sm font-medium hover:underline';
        link.textContent = quality.name;

        div.appendChild(link);
        return div;
      }

      function updateComparison() {
        const compareToId = document.getElementById('compare-select').value;
        const currentPrimaryQualities = triadQualities[currentTriadId].primaryQualities;
        const currentSecondaryQualities = triadQualities[currentTriadId].secondaryQualities;
        const compareToPrimaryQualities = triadQualities[compareToId].primaryQualities;
        const compareToSecondaryQualities = triadQualities[compareToId].secondaryQualities;

        // Combine all qualities for each triad
        const currentAllQualities = [...currentPrimaryQualities, ...currentSecondaryQualities];
        const compareToAllQualities = [...compareToPrimaryQualities, ...compareToSecondaryQualities];

        const currentPrimaryNames = currentPrimaryQualities.map(q => q.name);
        const currentSecondaryNames = currentSecondaryQualities.map(q => q.name);
        const currentAllNames = [...currentAllQualities.map(q => q.name), triadQualities[currentTriadId].name];
        const compareToPrimaryNames = compareToPrimaryQualities.map(q => q.name);
        const compareToSecondaryNames = compareToSecondaryQualities.map(q => q.name);
        const compareToAllNames = [...compareToAllQualities.map(q => q.name), triadQualities[compareToId].name];

        // Shared Primary Qualities
        const sharedPrimaryQualities = currentPrimaryQualities.filter(q =>
          compareToPrimaryNames.includes(q.name)
        );

        // Shared Secondary Qualities
        const sharedSecondaryQualities = currentSecondaryQualities.filter(q =>
          compareToSecondaryNames.includes(q.name)
        );

        // Current unique qualities (considering both primary and secondary)
        const currentUniqueQualities = currentAllQualities.filter(q =>
          !compareToAllNames.includes(q.name)
        );

        // Compare to unique qualities (considering both primary and secondary)
        const compareToUniqueQualities = compareToAllQualities.filter(q =>
          !currentAllNames.includes(q.name)
        );

        // Get all possible qualities from all triads
        const allQualities = [];
        Object.values(triadQualities).forEach(triad => {
          [...triad.primaryQualities, ...triad.secondaryQualities].forEach(q => {
            if (!allQualities.find(existing => existing.name === q.name)) {
              allQualities.push(q);
            }
          });
        });

        // Missing qualities (qualities that BOTH triads lack - neither has them)
        const missingQualities = allQualities.filter(q =>
          !currentAllNames.includes(q.name) && !compareToAllNames.includes(q.name)
        );

        // Update Shared Primary Qualities
        const sharedPrimaryContainer = document.getElementById('same-primary-qualities');
        sharedPrimaryContainer.innerHTML = '';
        if (sharedPrimaryQualities.length > 0) {
          document.getElementById('shared-primary-qualities').style.display = 'block';
          sharedPrimaryQualities.forEach(quality => {
            sharedPrimaryContainer.appendChild(createQualityElement(quality, 'bg-blue-50 border-blue-200'));
          });
        } else {
          document.getElementById('shared-primary-qualities').style.display = 'none';
        }

        // Update Shared Secondary Qualities
        const sharedSecondaryContainer = document.getElementById('same-secondary-qualities');
        sharedSecondaryContainer.innerHTML = '';
        if (sharedSecondaryQualities.length > 0) {
          document.getElementById('shared-secondary-qualities').style.display = 'block';
          sharedSecondaryQualities.forEach(quality => {
            sharedSecondaryContainer.appendChild(createQualityElement(quality, 'bg-purple-50 border-purple-200'));
          });
        } else {
          document.getElementById('shared-secondary-qualities').style.display = 'none';
        }

        // Update sections visibility and content
        const isSameTriad = currentTriadId === compareToId;

        // Current unique section
        const currentUniqueSection = document.getElementById('current-unique-section');
        const currentUniqueTitle = document.getElementById('current-unique-title');
        const currentUniqueContainer = document.getElementById('current-unique-qualities');

        if (isSameTriad || currentUniqueQualities.length === 0) {
          currentUniqueSection.style.display = 'none';
        } else {
          currentUniqueSection.style.display = 'block';
          currentUniqueTitle.textContent = `Qualities ${currentTriadId} Has That ${compareToId} Lacks`;
          currentUniqueContainer.innerHTML = '';
          currentUniqueQualities.forEach(quality => {
            currentUniqueContainer.appendChild(createQualityElement(quality, 'bg-green-50 border-green-200'));
          });
        }

        // Compare to unique section
        const otherUniqueSection = document.getElementById('other-unique-section');
        const otherUniqueTitle = document.getElementById('other-unique-title');
        const otherUniqueContainer = document.getElementById('other-unique-qualities');

        if (isSameTriad || compareToUniqueQualities.length === 0) {
          otherUniqueSection.style.display = 'none';
        } else {
          otherUniqueSection.style.display = 'block';
          otherUniqueTitle.textContent = `Qualities ${compareToId} Has That ${currentTriadId} Lacks`;
          otherUniqueContainer.innerHTML = '';
          compareToUniqueQualities.forEach(quality => {
            otherUniqueContainer.appendChild(createQualityElement(quality, 'bg-orange-50 border-orange-200'));
          });
        }

        // Missing Qualities section
        const missingContainer = document.getElementById('neither-qualities');
        missingContainer.innerHTML = '';
        missingQualities.forEach(quality => {
          missingContainer.appendChild(createQualityElement(quality, 'bg-gray-50 border-gray-200'));
        });
      }

      // Initialize comparison on page load
      document.addEventListener('DOMContentLoaded', function() {
        updateComparison();

        // Add event listener for dropdown changes
        document.getElementById('compare-select').addEventListener('change', updateComparison);
      });
    </script>
  </div>
</div>
