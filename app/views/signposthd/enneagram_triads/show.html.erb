<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900"><%= @enneagram_triad.name %></h1>
      <p class="mt-2 text-sm text-gray-700">Triad <%= @enneagram_triad.id %> details and component enneatypes.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-3">
      <%= link_to enneagram_triads_path, class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        ← Back to Triads
      <% end %>
      <%= link_to enneagram_browser_path, class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Triad Details -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-16 w-16 rounded-full bg-green-500 flex items-center justify-center">
              <span class="text-sm font-bold text-white"><%= @enneagram_triad.id %></span>
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg font-medium text-gray-900"><%= @enneagram_triad.name %></h3>
            <p class="text-sm text-gray-500">Triad <%= @enneagram_triad.id %></p>
            <p class="text-sm text-gray-600 italic"><%= @enneagram_triad.nickname %></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Component Enneatypes -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Component Enneatypes</h2>
    <p class="text-sm text-gray-600 mb-4">The enneatypes that make up this triad:</p>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <% @enneagram_triad.enneatype_objects.each do |enneatype| %>
        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
          <%= link_to "", enneatype_path(enneatype), class: "absolute inset-0" %>
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center">
                <span class="text-sm font-medium text-white"><%= enneatype.id %></span>
              </div>
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900"><%= enneatype.name %></p>
              <p class="text-xs text-gray-500">Type <%= enneatype.id %></p>
            </div>
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Trigroup -->
  <% if @enneagram_triad.trigroup %>
    <div class="mt-8">
      <h2 class="text-lg font-medium text-gray-900 mb-6">Trigroup</h2>
      <p class="text-sm text-gray-600 mb-4">This triad belongs to:</p>
      <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400 max-w-md">
        <%= link_to "", enneagram_trigroup_path(@enneagram_triad.trigroup), class: "absolute inset-0" %>
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
              <span class="text-xs font-medium text-white"><%= @enneagram_triad.trigroup.id.split('-').first.first.upcase %></span>
            </div>
          </div>
          <div class="min-w-0 flex-1">
            <p class="text-sm font-medium text-gray-900"><%= @enneagram_triad.trigroup.name %></p>
            <p class="text-xs text-gray-500">Trigroup</p>
          </div>
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>
  <% end %>
  <!-- Enneatype Statements -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Enneatype Statements</h2>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <% @enneagram_triad.enneatypes.each do |enneatype_id| %>
        <div class="bg-white border border-gray-200 rounded-lg p-6">
          <div class="flex items-center mb-4">
            <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white"><%= enneatype_id %></span>
            </div>
            <h3 class="ml-3 text-lg font-medium text-gray-900">Type <%= enneatype_id %></h3>
          </div>
          <div class="space-y-2">
            <% @enneagram_triad.statements_for_enneatype(enneatype_id).each do |statement| %>
              <p class="text-sm text-gray-700 italic">"<%= statement %>"</p>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Qualities -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Qualities</h2>
    <p class="text-sm text-gray-600 mb-4">Qualities from other triads that share stems with this triad:</p>
    <% qualities = @enneagram_triad.qualities %>
    <% if qualities.any? %>
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
        <% qualities.each do |quality| %>
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <%= link_to quality[:name], enneagram_triad_path(quality[:triad_id]), class: "text-sm font-medium text-green-600 hover:text-green-800" %>
                <span class="text-sm text-gray-500"> via </span>
                <%= link_to quality[:stem_id], enneagram_stem_path(quality[:stem_id]), class: "text-sm font-medium text-orange-600 hover:text-orange-800" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="text-sm text-gray-500">No shared qualities found.</p>
    <% end %>
  </div>
  <!-- Related Stems -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Related Stems</h2>
    <p class="text-sm text-gray-600 mb-4">Stems that are formed by pairs within this triad:</p>
    <% stems = EnneagramStem.for_triad(@enneagram_triad) %>
    <% if stems.any? %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <% stems.each do |stem| %>
          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <%= link_to "", enneagram_stem_path(stem), class: "absolute inset-0" %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white"><%= stem.id %></span>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium text-gray-900"><%= stem.name %></p>
                <p class="text-xs text-gray-500">
                  <%= stem.centers %> | Types: <%= stem.enneatypes.join(', ') %>
                </p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No related stems</h3>
        <p class="mt-1 text-sm text-gray-500">This triad does not have any related stems.</p>
      </div>
    <% end %>
  </div>
</div>
