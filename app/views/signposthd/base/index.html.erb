<table class="min-w-full divide-y divide-gray-200">
  <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statement</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Macrocosm</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Microcosm</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Physics</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
    </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200">
    <% @base_data.each do |base_key, base| %>
      <tr onclick="window.location='<%= base_path(base_key) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base_key %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base[:name] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base[:statement] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base[:macrocosm] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base[:microcosm] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= base[:physics] %></td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
          <a href="<%= base_path(base_key) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>
