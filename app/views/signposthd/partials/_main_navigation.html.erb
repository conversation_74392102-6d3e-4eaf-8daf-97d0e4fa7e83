<nav id="main-navigation" class="top-12 top-20 bg-white fixed flex flex-1 flex-col z-50 overflow-y-auto overflow-x-hidden" style="height: 2000px; width: 260px;">
  <ul role="list" class="flex flex-1 flex-col gap-y-7">
    <li>
      <ul role="list" class="-mx-2 space-y-1">
        <li>
          <a id="dashboard-link-sidebar" href="/dashboard" class="<%= action_name == 'dashboard' || action_name == 'dashboard-astro' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
            </svg>
            Dashboard
          </a>
        </li>
        <li class="hidden">
          <a href="/calendar_events" class="<%= controller_name == 'calendar_events' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
            </svg>
            Calendar
          </a>
        </li>
        <li class="hidden">
          <a href="/day-planner" class="<%= action_name == 'day_planner' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"></path>
            </svg>
            Day Planner
          </a>
          <% if ['day_planner', 'line_calendar'].include?(action_name) || controller_name == 'reminders' %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/day-planner/reminders" class="<%= controller_name == 'reminders' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
                    <!-- Add the SVG icon for Reminders -->
                  </svg>
                  Reminders
                </a>
              </li>
              <li>
                <a href="/day-planner/line-calendar" class="<%= action_name == 'line_calendar' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
                    <!-- Add the SVG icon for Line Calendar -->
                  </svg>
                  Line Calendar
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/bodygraphs" class="<%= controller_name == 'bodygraphs' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <circle cx="12" cy="6" r="2"></circle>
              <path d="M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25z"></path>
            </svg>
            Bodygraphs
          </a>
        </li>
        <li>
          <a href="/reference" class="<%= action_name == 'reference' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path fill="none" d="M12 4v16M12 4l-8-2V22h16V2l-8 2z" stroke="#ccc"/>
            </svg>
            Reference
          </a>
          <% if ['reference', 'rave_mandala_explorer'].include?(action_name) || ['quarters_and_godheads', 'centers', 'circuits', 'channels', 'gates', 'lines', 'color', 'tone', 'base'].include?(controller_name) %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/rave-mandala-explorer" class="<%= action_name == 'rave_mandala_explorer' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" fill="none" />
                    <circle cx="12" cy="12" r="6" stroke="#ccc" fill="none" />
                    <path d="M 12 7.5 L 15 15 L 9 15 Z" stroke="currentColor" fill="none" />
                  </svg>
                  Rave Mandala Explorer
                </a>
              </li>
              <li>
                <a href="/quarters-and-godheads" class="<%= controller_name == 'quarters_and_godheads' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg version="1.0" xmlns="http://www.w3.org/2000/svg"
     viewBox="0 0 3600 3600"
     class="w-6 h-6 shrink-0 text-gray-400 group-hover:text-indigo-600" 
     fill="none" stroke-width="10" stroke="currentColor" aria-hidden="true">
                    <g transform="scale(1, -1) translate(0, -3600)">
                      <path d="M1365 4354 c-66 -21 -156 -114 -210 -216 -30 -58 -49 -82 -82 -103
-90 -57 -87 -120 15 -245 24 -30 48 -65 53 -78 11 -30 66 -52 127 -52 62 0 84
20 50 44 -12 9 -35 16 -50 16 -15 0 -30 5 -33 10 -12 20 63 9 151 -21 158 -55
245 -49 359 26 68 45 106 99 112 158 7 56 -13 78 -60 70 -19 -3 -49 2 -71 11
-51 21 -97 20 -138 -3 -25 -14 -54 -19 -117 -19 -51 0 -88 -5 -95 -12 -8 -10
10 -11 85 -8 76 4 105 10 138 29 39 22 44 22 89 9 26 -7 71 -15 100 -18 45 -3
53 -7 58 -27 16 -64 -51 -151 -158 -207 -87 -45 -157 -44 -293 1 -104 36 -159
41 -183 17 -15 -15 -20 -13 79 -32 38 -7 33 -28 -9 -32 -62 -6 -107 14 -140
65 -18 27 -52 76 -77 111 -55 77 -58 111 -14 152 16 16 39 32 49 35 10 3 39
43 64 89 88 162 171 226 288 226 77 0 130 -14 278 -72 63 -25 132 -52 154 -59
41 -15 73 -48 82 -86 9 -35 1 -37 -45 -14 -22 12 -54 21 -71 21 -25 0 -35 8
-60 49 -23 36 -41 53 -73 66 -104 42 -357 38 -357 -6 0 -8 18 -10 65 -5 79 8
181 -7 243 -37 26 -12 71 -45 100 -75 51 -50 55 -53 100 -50 45 3 47 2 50 -25
3 -24 -2 -30 -34 -43 -64 -27 -152 -12 -250 41 -59 32 -93 45 -123 45 -43 0
-119 -30 -190 -74 -44 -28 -76 -31 -154 -11 -25 7 -28 5 -25 -11 8 -45 131
-70 201 -40 18 7 54 30 80 50 59 45 92 53 140 32 46 -19 47 -32 3 -40 -31 -6
-82 -46 -59 -46 6 0 16 7 23 15 7 8 27 17 46 21 45 8 45 36 0 58 -63 30 -89
23 -193 -53 -63 -46 -166 -51 -215 -12 -23 19 -23 19 29 6 61 -16 76 -12 163
36 127 71 167 74 265 15 72 -44 112 -56 188 -56 96 0 152 46 111 91 -13 15
-29 19 -62 17 -41 -2 -48 2 -81 39 -69 78 -141 109 -281 123 -82 8 -80 8 40 6
158 -4 197 -18 239 -86 25 -41 36 -50 68 -55 63 -10 123 -36 123 -51 0 -22
-16 -48 -49 -81 -20 -21 -31 -44 -36 -78 -9 -59 9 -200 31 -232 32 -49 114
-79 141 -52 7 7 8 23 2 47 -9 40 -14 180 -7 188 3 2 10 -42 16 -99 10 -108 23
-143 56 -153 31 -10 35 15 15 85 -22 74 -27 203 -11 241 9 20 10 7 6 -60 -6
-93 4 -143 28 -148 21 -4 28 27 28 132 0 151 11 88 13 -80 3 -170 13 -210 54
-229 17 -7 22 -17 21 -41 -1 -17 3 -37 8 -45 5 -8 9 -64 9 -126 l0 -112 34
-35 c46 -47 119 -77 207 -83 124 -10 274 32 274 77 0 29 -25 37 -135 42 -108
5 -143 17 -205 71 -19 16 -30 28 -25 26 6 -3 30 -14 55 -26 54 -26 209 -63
216 -51 3 4 -29 44 -70 87 -70 74 -84 91 -71 91 3 0 44 -37 92 -81 95 -87 138
-119 165 -119 22 0 31 -16 21 -34 -18 -33 -113 -92 -183 -116 -59 -20 -76 -29
-78 -46 -4 -28 37 -58 114 -84 69 -24 98 -25 154 -5 23 8 50 15 58 15 29 0
-19 -29 -76 -46 -73 -22 -76 -30 -63 -204 6 -79 9 -146 7 -148 -3 -2 -12 11
-21 28 -11 21 -20 74 -25 150 -5 65 -13 127 -19 138 -22 41 -376 284 -443 304
-32 9 -41 8 -56 -5 -37 -33 -225 -100 -513 -181 -76 -21 -148 -72 -239 -166
-50 -53 -85 -81 -105 -85 -16 -3 -57 -15 -90 -25 -33 -10 -61 -14 -63 -9 -2 5
-15 9 -28 9 -20 0 -29 -9 -43 -43 -10 -24 -44 -69 -76 -100 -55 -54 -63 -77
-26 -77 27 0 69 36 102 89 l30 46 -6 -35 c-8 -43 -53 -143 -102 -224 -49 -82
-70 -105 -94 -99 -11 3 -33 -5 -53 -21 -28 -21 -37 -37 -46 -81 -11 -51 -14
-56 -64 -85 -80 -45 -137 -42 -259 14 -52 24 -99 50 -103 58 -5 7 -22 53 -39
103 -21 62 -35 91 -47 93 -30 6 -41 -42 -32 -133 14 -141 10 -237 -9 -270 -17
-27 -18 -28 -12 -5 3 14 9 59 12 101 5 63 2 87 -16 139 -17 50 -21 75 -15 114
9 68 16 86 59 142 20 26 49 73 65 104 31 61 77 104 150 139 l45 21 -29 -22
c-124 -95 -159 -154 -164 -273 -3 -63 1 -92 17 -136 49 -129 181 -216 294
-195 68 13 165 127 137 161 -8 10 -26 12 -64 8 -51 -5 -55 -4 -95 32 -22 20
-41 42 -41 47 0 6 17 -4 37 -22 35 -31 39 -32 103 -28 68 5 165 41 203 76 33
30 48 68 37 96 -12 33 -39 33 -108 1 -81 -38 -171 -29 -212 20 -13 15 -9 15
28 -4 69 -35 111 -32 177 13 89 62 205 195 205 237 0 17 -46 63 -73 72 -14 5
-38 -12 -95 -66 -83 -78 -126 -108 -184 -127 -66 -20 -76 -6 -13 18 137 51
222 149 272 312 2 8 15 -17 28 -55 31 -94 75 -135 142 -135 60 0 121 27 156
69 46 55 30 94 -54 130 -50 22 -52 24 -100 127 -53 113 -65 124 -137 124 -43
0 -157 -30 -226 -59 -80 -35 -151 -88 -201 -153 -53 -66 -75 -63 -29 4 35 50
138 144 184 168 40 20 109 35 232 50 48 6 95 13 103 16 30 12 14 47 -52 115
-70 72 -73 89 -7 40 76 -55 131 -137 203 -299 37 -84 54 -112 81 -130 45 -31
101 -26 165 14 26 16 79 47 118 70 147 84 100 152 -94 137 -48 -4 -89 -1 -112
6 -82 27 -124 110 -94 183 14 34 85 114 93 105 2 -2 -11 -30 -31 -62 -46 -77
-47 -123 -5 -159 43 -36 89 -46 212 -46 91 0 117 -4 175 -26 194 -74 201 -76
263 -44 56 28 51 44 -17 60 -57 12 -149 53 -188 84 -33 25 -9 18 68 -21 59
-30 85 -37 147 -40 49 -3 83 0 99 9 26 14 49 44 40 52 -3 3 -41 8 -85 9 -85 4
-129 20 -177 66 -24 22 -67 89 -67 105 0 2 30 -27 68 -64 56 -56 76 -69 115
-78 85 -18 152 10 187 78 38 74 18 159 -44 185 -18 8 -55 14 -83 13 -39 0 -76
11 -157 46 -125 55 -174 64 -235 42 -26 -10 -76 -16 -125 -16 -84 0 -106 -9
-106 -41 0 -17 25 -30 90 -48 33 -9 32 -9 -24 -10 -55 -1 -60 1 -95 38 -64 68
-89 73 -193 40 l-66 -22 -48 31 c-68 46 -123 44 -217 -5 -129 -68 -354 -310
-373 -401 -12 -60 -77 -174 -168 -295 -53 -70 -96 -132 -96 -136 0 -15 28 -20
61 -11 57 17 41 4 -30 -24 -47 -18 -81 -40 -104 -65 -72 -80 -126 -207 -98
-233 10 -11 17 -7 37 16 14 15 28 25 32 22 3 -4 -19 -30 -50 -59 -83 -77 -115
-161 -88 -232 6 -15 15 -59 21 -98 25 -173 62 -241 164 -309 36 -23 65 -49 65
-58 0 -86 102 -211 203 -248 63 -24 148 -28 211 -11 22 6 60 31 87 56 33 31
49 41 49 29 0 -29 -69 -95 -114 -107 -56 -15 -174 -5 -231 20 -26 12 -54 21
-61 21 -21 0 -47 -47 -40 -73 15 -63 136 -178 262 -252 36 -21 63 -39 61 -41
-2 -2 -34 8 -72 23 -66 25 -68 25 -81 8 -8 -11 -14 -27 -14 -37 0 -33 28 -86
49 -93 11 -3 18 -11 16 -18 -12 -32 27 -52 130 -67 96 -14 158 -41 174 -76 21
-46 -9 -87 -47 -64 -16 9 -16 9 0 10 23 0 31 16 24 44 -7 30 -69 56 -131 56
-43 0 -45 -2 -45 -28 0 -48 59 -133 112 -160 52 -27 86 -24 128 8 37 28 34
113 -6 177 -35 56 -82 82 -189 105 l-80 17 58 0 c41 1 75 -7 118 -24 71 -30
89 -23 89 34 -1 82 -28 147 -52 123 -26 -26 -27 -10 -3 35 28 54 30 64 13 81
-9 9 -23 1 -59 -33 -40 -37 -55 -45 -86 -45 -53 0 -138 54 -165 104 -7 12 10
4 41 -21 87 -70 136 -66 198 15 60 79 81 215 39 261 -12 14 -14 24 -6 52 6 18
10 78 10 131 0 72 3 98 13 98 16 0 62 -69 136 -205 85 -158 124 -208 229 -296
355 -296 764 -454 982 -379 54 18 55 18 32 -1 -30 -24 -114 -49 -164 -49 -224
0 -695 221 -925 434 -59 55 -79 82 -118 161 -50 103 -100 165 -134 165 -26 0
-28 -29 -7 -122 8 -35 17 -127 20 -206 3 -79 12 -169 20 -200 8 -31 19 -109
26 -172 13 -129 41 -272 71 -357 11 -33 15 -61 10 -67 -4 -6 -11 -52 -14 -102
-8 -106 -17 -134 -57 -181 -17 -19 -30 -43 -30 -54 0 -57 62 -116 198 -186 48
-24 78 -37 67 -28 -11 9 -50 32 -87 52 -81 43 -154 108 -163 146 -6 24 -2 34
24 61 41 42 61 109 61 202 0 82 13 102 30 49 16 -49 137 -300 167 -348 37 -59
47 -35 23 56 -27 107 -95 240 -152 299 -65 67 -104 197 -133 448 -9 74 -20
151 -26 170 -6 19 -14 114 -19 210 -5 96 -16 200 -25 230 -8 30 -15 63 -15 73
0 54 78 -29 131 -141 46 -96 85 -142 186 -223 253 -201 657 -379 864 -379 128
0 219 57 293 184 21 36 42 64 46 61 4 -3 16 -39 25 -81 9 -41 28 -108 43 -147
38 -108 35 -167 -16 -270 -30 -60 -64 -107 -128 -175 -47 -51 -84 -95 -81 -98
17 -18 152 95 212 176 21 28 50 81 65 118 16 37 35 71 43 76 18 12 104 -32
130 -66 19 -25 19 -32 8 -83 -46 -205 -392 -390 -805 -430 -78 -7 -169 -20
-202 -29 -75 -19 -194 -68 -254 -104 -81 -48 -13 -38 178 27 97 33 178 58 180
56 2 -2 -47 -31 -110 -65 -62 -34 -102 -58 -88 -54 14 3 54 22 90 42 192 104
173 115 -60 37 -104 -35 -190 -61 -190 -59 0 3 38 23 85 45 105 49 257 89 340
89 148 0 390 63 554 145 194 98 301 219 301 344 0 42 -52 95 -110 112 -47 14
-67 8 -81 -28 -55 -134 -116 -219 -212 -295 -33 -26 -25 -14 37 57 92 105 147
210 154 292 4 42 -2 72 -26 141 -17 48 -39 120 -48 161 -12 55 -21 75 -35 78
-25 7 -24 11 9 64 52 83 162 287 223 413 81 171 93 236 86 465 -5 151 -4 178
11 205 11 21 17 55 17 109 0 42 2 77 3 77 2 0 15 -9 29 -20 34 -27 59 -25 79
5 17 27 31 133 20 161 -6 15 -11 12 -33 -19 -24 -34 -53 -49 -53 -28 0 5 27
39 60 75 54 60 90 113 90 133 0 4 -12 -10 -27 -32 -14 -22 -50 -67 -80 -101
-29 -34 -53 -68 -53 -77 0 -31 22 -29 57 4 l36 34 -6 -58 c-6 -57 -28 -107
-47 -107 -5 0 -25 14 -45 31 -33 30 -35 34 -35 98 0 36 -5 105 -11 153 -12
104 -6 120 53 137 56 17 108 49 108 67 0 19 -14 18 -92 -7 l-66 -22 -69 22
c-38 12 -83 33 -99 47 -28 23 -28 25 -11 38 11 8 37 18 59 21 44 8 176 91 196
125 17 28 3 46 -48 60 -24 7 -66 37 -121 88 -84 78 -127 112 -141 112 -22 0 0
-35 70 -110 58 -63 71 -82 52 -76 -14 4 -54 14 -89 22 -79 18 -112 35 -166 85
-24 22 -46 36 -49 30 -11 -16 102 -128 156 -155 45 -23 65 -26 148 -26 90 0
120 -8 120 -32 0 -4 -19 -17 -42 -30 -34 -17 -65 -22 -153 -26 -157 -6 -230
19 -283 98 -21 31 -22 41 -16 111 4 51 2 94 -7 127 -15 61 -4 82 45 82 17 0
78 11 136 25 58 14 119 25 136 25 48 0 130 -47 179 -103 76 -86 145 -250 145
-345 0 -36 20 -81 44 -101 22 -19 20 -5 -5 28 -13 17 -24 54 -29 98 -29 228
-187 433 -333 433 -22 0 -90 -11 -151 -24 -152 -33 -217 -33 -231 1 -5 10 -11
95 -16 188 -4 94 -11 180 -14 193 -16 49 -25 17 -26 -85 0 -60 -4 -117 -9
-128 -8 -20 -8 -20 -16 0 -11 25 -11 123 -1 193 10 70 -10 67 -35 -4 -25 -73
-22 -135 17 -286 6 -22 4 -28 -9 -28 -22 0 -34 34 -46 137 -16 143 -20 157
-33 144 -12 -12 -16 -134 -7 -226 4 -44 3 -50 -15 -53 -11 -1 -34 7 -52 17
-55 35 -65 60 -71 185 l-5 113 36 40 c20 22 41 51 46 65 7 20 15 25 30 21 11
-3 39 4 61 16 59 31 112 28 153 -8 18 -17 59 -39 90 -51 65 -24 75 -42 52 -95
-19 -47 -19 -76 -1 -101 14 -18 15 -18 26 14 7 17 15 32 19 32 10 0 56 -51 69
-75 8 -16 5 -34 -10 -75 -11 -30 -22 -62 -25 -71 -2 -8 -29 -30 -59 -48 -31
-18 -53 -35 -50 -39 3 -3 28 9 56 26 27 17 52 27 55 21 9 -14 75 -11 135 7 95
28 174 0 230 -81 40 -58 76 -85 114 -85 33 0 76 24 76 41 0 6 -12 1 -26 -10
-15 -12 -37 -21 -50 -21 -28 0 -76 39 -114 95 -56 82 -125 98 -272 65 -31 -7
-65 -10 -74 -6 -15 6 -15 9 4 29 46 49 65 138 41 191 -7 14 -29 37 -50 52 -35
24 -39 31 -39 70 0 56 39 102 117 141 45 21 74 28 144 31 78 4 93 2 146 -22
41 -19 67 -26 89 -21 43 8 53 -18 18 -42 -20 -13 -62 -18 -192 -24 -164 -7
-237 -20 -229 -43 7 -19 340 -10 405 11 46 15 54 16 62 3 17 -26 5 -35 -56
-42 -32 -5 -77 -15 -99 -24 -22 -9 -82 -19 -134 -23 -100 -6 -125 -21 -70 -40
51 -18 97 -13 224 23 84 23 125 31 138 24 10 -5 17 -5 17 1 0 22 -42 20 -141
-8 -130 -37 -176 -44 -214 -33 -26 8 -22 10 40 16 39 4 97 15 130 26 33 10 86
21 117 25 64 7 73 14 64 51 -8 29 -38 32 -94 8 -23 -9 -63 -14 -107 -13 -38 1
-115 -2 -170 -6 -84 -6 -98 -5 -87 6 9 10 58 15 170 20 214 9 273 27 257 79
-5 16 -15 20 -49 20 -24 0 -63 9 -87 19 -93 41 -211 36 -310 -15 -51 -25 -109
-85 -109 -112 0 -9 -3 -27 -6 -40 -6 -19 3 -32 47 -73 53 -49 54 -51 54 -107
-1 -38 -7 -66 -19 -85 -27 -40 -29 -28 -4 30 19 45 20 55 8 82 -7 17 -30 45
-51 62 l-38 30 -14 -27 c-22 -43 -31 -10 -12 47 25 73 16 92 -52 119 -32 13
-69 34 -83 46 -39 36 -81 47 -129 34 -22 -6 -43 -15 -46 -20 -3 -6 -21 -10
-40 -10 -31 0 -35 3 -45 40 -21 77 -38 87 -357 201 -102 37 -190 45 -258 23z
m-224 -724 c53 -35 74 -37 150 -9 72 26 101 20 150 -31 20 -21 45 -43 57 -49
22 -12 129 -5 137 9 3 5 -27 18 -65 29 -45 14 -70 27 -70 36 0 18 23 23 130
29 47 3 111 8 142 11 50 6 66 3 145 -30 148 -62 156 -64 213 -65 33 0 64 -6
77 -15 30 -21 45 -80 33 -126 -22 -81 -85 -118 -176 -104 -43 6 -57 15 -122
81 -41 41 -79 74 -84 74 -25 0 37 -115 84 -155 46 -39 73 -47 168 -54 l85 -6
-30 -20 c-22 -15 -46 -20 -100 -20 -66 0 -77 4 -179 58 -60 31 -111 57 -113
57 -2 0 -3 -6 -3 -14 0 -32 156 -124 248 -146 58 -13 64 -21 27 -38 -47 -22
-103 -14 -208 29 -99 41 -105 42 -227 44 -138 2 -186 15 -215 59 -22 32 -14
67 31 141 48 80 44 96 -12 53 -62 -48 -93 -99 -94 -151 0 -59 26 -104 79 -135
43 -25 51 -26 158 -24 128 3 156 -5 151 -45 -2 -21 -18 -35 -75 -67 -40 -23
-93 -54 -119 -70 -58 -35 -112 -39 -150 -10 -18 13 -44 57 -74 125 -88 198
-173 305 -267 334 -41 12 -25 -18 47 -90 39 -38 70 -75 70 -82 0 -16 -18 -20
-135 -33 -179 -19 -224 -39 -329 -143 -79 -80 -113 -134 -102 -163 7 -17 13
-13 49 31 78 93 120 127 211 171 112 54 220 79 276 64 38 -10 42 -15 89 -115
49 -104 50 -105 104 -129 87 -38 94 -73 27 -130 -33 -27 -87 -46 -134 -46 -47
0 -100 62 -126 148 -23 77 -36 89 -46 45 -36 -165 -137 -275 -302 -328 -65
-21 -58 -39 12 -33 75 6 140 42 227 125 42 40 83 73 91 73 17 0 68 -44 68 -58
0 -19 -62 -100 -117 -154 -118 -114 -166 -130 -262 -82 -28 15 -55 23 -59 19
-14 -14 70 -74 119 -84 45 -10 56 -8 126 20 83 33 103 32 103 -5 0 -60 -118
-133 -231 -143 l-59 -6 -47 49 c-27 26 -52 47 -56 46 -5 -1 -6 2 -3 7 7 11
-24 81 -36 81 -14 0 -9 -24 21 -86 54 -114 125 -169 190 -146 33 12 41 7 41
-20 0 -28 -91 -114 -129 -123 -108 -24 -247 77 -286 207 -43 148 13 288 150
375 48 30 62 60 23 47 -89 -27 -171 -91 -203 -157 -14 -29 -43 -77 -67 -107
-69 -91 -85 -185 -48 -280 24 -60 25 -122 5 -209 -9 -35 -13 -66 -10 -69 12
-13 37 27 57 91 19 62 20 77 10 131 -25 131 -23 216 3 218 6 0 20 -31 32 -68
40 -123 46 -131 153 -181 111 -52 195 -62 246 -28 41 26 84 30 84 6 0 -10 11
-30 25 -45 28 -31 28 -31 15 -56 -13 -25 -67 -42 -91 -30 -24 13 -39 42 -39
74 0 18 9 30 31 41 17 9 29 19 26 22 -9 8 -46 -16 -62 -41 -12 -18 -13 -29 -5
-52 24 -63 62 -79 119 -49 44 22 56 73 23 97 -52 39 -47 165 10 213 16 14 34
25 39 25 17 0 9 -18 -15 -33 -13 -9 -32 -36 -41 -61 -22 -58 -9 -87 65 -138
41 -29 61 -51 78 -89 12 -27 26 -48 31 -44 11 6 31 94 31 138 0 25 -11 42 -57
85 -49 46 -57 59 -61 98 -2 28 0 44 7 44 6 0 11 -7 11 -16 0 -8 23 -39 51 -69
37 -40 55 -69 68 -111 9 -32 23 -60 30 -62 21 -7 10 182 -14 227 -10 20 -18
59 -19 90 -1 63 -14 73 -38 31 -9 -17 -22 -30 -29 -30 -6 0 -2 14 10 33 41 61
90 165 102 213 24 100 45 120 149 144 80 18 102 32 167 106 89 100 145 133
315 182 194 56 371 119 414 148 l35 24 42 -18 c44 -19 300 -189 368 -244 50
-40 60 -67 68 -196 6 -96 11 -116 34 -151 24 -36 27 -50 27 -129 0 -59 -5 -94
-14 -107 -12 -17 -14 -16 -25 16 -36 109 -185 165 -377 141 -54 -7 -137 -26
-187 -42 l-89 -30 -27 26 c-52 52 -97 67 -221 72 -131 5 -260 -13 -374 -53
-120 -42 -148 -78 -94 -118 49 -36 109 -28 223 29 92 46 104 49 169 48 39 -1
91 -9 116 -18 48 -18 110 -56 110 -68 0 -8 -43 -32 -85 -48 -23 -9 -24 -8 -19
19 8 38 -25 60 -99 68 -62 6 -177 -12 -204 -33 -17 -12 -17 -15 -1 -44 l17
-30 -67 0 c-49 0 -76 5 -100 20 -38 23 -72 26 -72 5 0 -9 14 -16 38 -20 20 -4
66 -19 102 -35 123 -54 190 -62 295 -35 91 23 161 62 198 107 40 51 77 88 88
88 15 0 50 -115 54 -175 4 -84 -10 -159 -42 -224 -24 -49 -34 -58 -73 -71 -56
-19 -82 -63 -78 -130 3 -43 5 -45 36 -48 17 -2 32 -7 32 -12 0 -5 11 -17 25
-26 27 -18 65 -11 65 12 0 16 16 10 58 -23 18 -15 21 -25 16 -49 -6 -33 -25
-41 -154 -68 -36 -8 -101 -30 -145 -49 l-80 -36 -21 27 c-21 26 -21 26 -28 4
-8 -25 -1 -42 18 -42 7 0 23 -11 35 -24 15 -16 51 -32 104 -46 45 -12 97 -31
115 -42 27 -17 42 -19 80 -13 26 4 82 8 125 9 75 1 80 2 108 34 57 64 30 90
-82 78 -76 -9 -254 3 -241 16 15 15 156 22 180 9 17 -9 38 -9 89 0 82 15 92
22 68 49 -15 16 -31 20 -86 20 -81 0 -103 18 -27 22 47 3 53 6 56 26 5 31 -12
49 -54 56 -57 11 -29 29 34 23 30 -3 63 -2 73 3 39 21 42 156 8 397 -24 170
-21 214 13 221 12 2 18 -5 20 -25 9 -81 214 -112 324 -49 l41 23 7 -141 c8
-174 -5 -285 -45 -386 -33 -83 -149 -309 -226 -440 -28 -47 -62 -117 -77 -156
-41 -110 -113 -164 -237 -179 -190 -23 -516 108 -806 326 -160 119 -217 185
-308 353 -83 154 -142 235 -163 222 -8 -4 -12 -37 -13 -89 0 -102 -13 -180
-29 -185 -6 -2 -34 -24 -60 -47 -112 -100 -292 -79 -404 46 -41 45 -86 142
-73 156 3 3 30 -7 59 -21 29 -15 44 -23 33 -20 -80 26 -87 6 -19 -56 78 -72
183 -103 242 -71 29 15 62 69 62 100 0 23 -2 23 -62 17 l-63 -6 53 19 c60 21
77 34 67 50 -4 8 -32 9 -86 5 -51 -5 -79 -3 -79 3 0 12 47 23 102 23 55 0 78
15 78 49 0 102 -194 166 -310 101 -51 -29 -90 -81 -90 -121 0 -15 -4 -30 -9
-33 -5 -3 -35 14 -67 37 -70 52 -107 114 -124 207 -6 36 -20 103 -31 150 -26
118 -10 166 85 260 46 44 58 70 33 70 -8 0 -22 -9 -32 -20 -42 -46 -42 -4 1
86 45 96 91 137 208 184 57 23 113 51 123 62 18 21 18 21 -2 13 -88 -34 -175
-54 -175 -38 0 3 38 56 84 117 110 146 142 201 170 284 29 91 92 176 209 289
68 64 117 101 168 127 90 45 131 46 190 6z m59 -930 c0 -27 -135 -190 -157
-190 -23 0 -14 17 33 62 25 23 57 64 70 91 22 42 54 64 54 37z m807 -226 c61
-31 89 -63 64 -72 -8 -3 -25 -16 -38 -30 l-23 -25 -23 21 c-45 42 -116 65
-207 66 -83 1 -89 0 -180 -47 -110 -55 -168 -62 -214 -26 -25 20 -26 22 -10
40 30 34 179 80 329 103 17 2 79 3 138 1 97 -2 115 -6 164 -31z m629 -21 c71
-38 131 -127 109 -163 -11 -17 -78 -47 -123 -55 -93 -15 -211 21 -220 68 -5
24 11 21 69 -14 43 -27 57 -31 94 -26 60 9 135 47 135 69 0 47 -140 79 -229
52 -29 -8 -55 -22 -58 -30 -3 -7 -17 -14 -32 -14 -15 0 -32 -5 -39 -12 -16
-16 -15 -112 3 -228 20 -130 29 -287 18 -335 l-9 -40 -66 -1 c-40 0 -73 -6
-83 -14 -13 -11 -19 -10 -38 7 -45 41 -86 55 -142 48 -49 -6 -53 -5 -58 17 -7
27 6 93 22 112 6 7 29 18 50 25 70 21 110 108 118 256 5 87 -3 130 -41 216 -9
20 21 34 139 65 76 20 109 24 215 21 105 -2 131 -6 166 -24z m28 -97 c14 -9
26 -20 26 -25 0 -12 -77 -47 -122 -55 -31 -6 -47 -1 -93 25 -30 17 -55 35 -55
40 0 39 191 51 244 15z m-1548 -33 c-5 -36 -1 -56 14 -88 14 -28 22 -70 25
-126 l4 -84 -16 53 c-11 36 -33 72 -70 113 -29 33 -53 66 -53 75 0 15 77 100
94 102 5 1 6 -19 2 -45z m752 21 c62 -40 -2 -75 -135 -75 -101 0 -133 13 -133
52 0 43 209 61 268 23z m84 -64 c-32 -27 -121 -58 -200 -70 -57 -9 -78 -8
-147 10 -43 11 -83 24 -87 28 -4 4 68 8 160 8 144 0 175 3 227 21 75 26 75 26
47 3z m-972 -71 c0 -23 10 -40 37 -65 20 -19 40 -34 43 -34 3 0 14 -14 24 -31
16 -27 17 -37 5 -97 l-12 -66 -22 50 c-15 33 -33 55 -56 68 -92 51 -113 108
-63 173 31 41 44 42 44 2z m-359 -270 c42 -21 79 -63 79 -90 0 -24 -25 -35
-85 -38 -54 -2 -105 -18 -105 -32 0 -23 49 -32 106 -20 32 6 60 9 63 6 10 -11
-16 -24 -86 -44 -113 -34 -91 -54 31 -30 31 7 59 9 63 6 10 -11 -18 -67 -45
-87 -32 -26 -94 -26 -149 -1 -39 18 -138 100 -130 108 2 2 23 -4 47 -12 29
-10 47 -12 54 -5 6 6 -16 21 -67 45 -85 38 -92 51 -63 118 39 93 181 130 287
76z m1474 -204 c0 -17 -41 -17 -55 0 -12 15 -10 16 21 13 19 -2 34 -7 34 -13z
m184 -80 c26 -25 5 -45 -45 -45 -43 0 -46 2 -40 23 3 12 6 28 6 35 0 17 58 7
79 -13z m-91 -76 c11 -18 19 -20 66 -14 53 7 96 -5 96 -25 0 -6 -18 -10 -41
-10 -22 0 -48 -4 -58 -9 -12 -7 -32 -5 -63 7 -42 15 -53 15 -110 2 -35 -7 -87
-16 -116 -19 -92 -10 -64 -26 58 -32 63 -3 123 -9 133 -14 9 -4 53 -1 98 6 70
11 81 11 95 -3 14 -13 13 -18 -10 -45 -25 -30 -26 -30 -156 -36 -105 -6 -135
-4 -153 7 -12 8 -62 26 -111 39 -49 14 -93 31 -97 39 -15 23 96 71 261 112 78
19 94 19 108 -5z m-1284 -87 c-2 -67 -28 -133 -74 -186 -47 -53 -91 -51 -164
9 -31 25 -58 45 -61 45 -3 0 -5 -8 -5 -17 1 -40 88 -116 151 -133 47 -13 82 0
132 48 46 44 49 27 10 -52 -28 -58 -26 -81 5 -53 35 32 48 15 53 -73 5 -68 -2
-72 -77 -39 -42 19 -60 21 -144 16 -53 -3 -100 -3 -106 1 -31 20 -48 112 -21
112 7 0 53 -13 102 -30 101 -34 95 -33 95 -22 0 5 -40 32 -89 61 -163 97 -281
211 -281 271 0 45 20 53 75 29 67 -30 158 -41 223 -28 41 8 64 20 98 53 24 23
44 51 44 61 0 18 1 18 17 2 13 -13 17 -33 17 -75z m-74 -483 c97 -23 160 -94
163 -184 2 -39 -2 -49 -23 -65 -48 -37 -121 -20 -168 40 -26 34 -52 86 -52
105 0 20 57 20 109 -1 62 -25 67 -64 8 -64 l-32 -1 24 -19 c28 -24 58 -25 83
-3 44 40 -1 114 -85 137 -29 8 -74 18 -102 21 -83 11 -123 33 -100 57 6 6 114
-8 175 -23z m404 -614 c41 -84 78 -191 73 -214 -3 -15 -157 289 -171 339 -12
44 59 -47 98 -125z"/>
                    </g>
                  </svg>
                  Quarters &amp; Godheads
                </a>
              </li>
              <li>
                <a href="/centers" class="<%= controller_name == 'centers' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <polygon points="12,4 9,8 12,12 15,8" stroke="currentColor" fill="none" />
                    <polygon points="17,10 20,14 14,14" stroke="currentColor" fill="none" />
                    <rect x="9" y="16" width="5" height="5" stroke="currentColor" fill="none" />
                  </svg>
                  Centers
                </a>
              </li>
              <li>
                <a href="/circuits" class="<%= controller_name == 'circuits' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 6 6" aria-hidden="true">
                    <polygon points="4,3 2,5 2,1" stroke="none" fill="#A0AEC0" />
                    <line x1="1.8" y1="2" x2="5" y2="-1" stroke="#A0AEC0" stroke-width="0.2" />
                    <line x1="1.85" y1="3" x2="5.05" y2="0" stroke="#A0AEC0" stroke-width="0.2" />
                    <line x1="1.9" y1="4" x2="5.1" y2="1" stroke="#A0AEC0" stroke-width="0.2" />
                    <line x1="2" y1="5" x2="6.2" y2="5.5" stroke="#A0AEC0" stroke-width="0.2" />
                    <line x1="2.05" y1="4.4" x2="6.25" y2="4.9" stroke="#A0AEC0" stroke-width="0.2" />
                    <line x1="2.1" y1="3.8" x2="6.3" y2="4.3" stroke="#A0AEC0" stroke-width="0.2" />
                  </svg>
                  Circuits
                </a>
              </li>
              <li>
                <a href="/channels" class="<%= controller_name == 'channels' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 6 6" aria-hidden="true">
                    <line x1="0.5" y1="0" x2="0.5" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                    <line x1="1.7" y1="0" x2="1.7" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                    <line x1="2.5" y1="0" x2="2.5" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                    <line x1="3.7" y1="0" x2="3.7" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                    <line x1="4.5" y1="0" x2="4.5" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                    <line x1="5.7" y1="0" x2="5.7" y2="6" stroke="currentColor" stroke-width=".2" fill="none" />
                  </svg>
                  Channels
                </a>
              </li>
              <li>
                <a href="/gates" class="<%= controller_name == 'gates' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <line x1="2" y1="1" x2="22" y2="1" stroke="currentColor" stroke-width="1" stroke-dasharray="8 4" />
                    <line x1="2" y1="5" x2="22" y2="5" stroke="currentColor" stroke-width="1" />
                    <line x1="2" y1="9" x2="22" y2="9" stroke="currentColor" stroke-width="1" stroke-dasharray="8 4" />
                    <line x1="2" y1="13" x2="22" y2="13" stroke="currentColor" stroke-width="1" />
                    <line x1="2" y1="17" x2="22" y2="17" stroke="currentColor" stroke-width="1" stroke-dasharray="8 4" />
                    <line x1="2" y1="21" x2="22" y2="21" stroke="currentColor" stroke-width="1" />
                  </svg>
                  Gates
                </a>
              </li>
              <li>
                <a href="/lines" class="<%= controller_name == 'lines' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <line x1="2" y1="10" x2="22" y2="10" stroke="currentColor" stroke-width="3" />
                    <line x1="2" y1="16" x2="22" y2="16" stroke="currentColor" stroke-width="3" stroke-dasharray="8 4" />
                  </svg>
                  Lines
                </a>
              </li>
              <li>
                <a href="/color" class="<%= controller_name == 'color' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <polygon points="12,2 5,7 5,17 12,22 19,17 19,7" stroke="currentColor" stroke-width="1" fill="none" />
                    <polygon points="12,3 5,17 19,17" stroke="#ccc" stroke-width="1" fill="none" />
                  </svg>
                  Color
                </a>
              </li>
              <li>
                <a href="/tone" class="<%= controller_name == 'tone' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <polygon points="12,2 5,7 5,17 12,22 19,17 19,7" stroke="#ccc" stroke-width="1" fill="none" />
                    <polygon points="12,3 5,17 19,17" stroke="currentColor" stroke-width="1" fill="none" />
                  </svg>
                  Tone
                </a>
              </li>
              <li>
                <a href="/base" class="<%= controller_name == 'base' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                    <polygon points="12,2 5,7 5,17 12,22 19,17 19,7" stroke="#ccc" stroke-width="1" fill="none" />
                    <polygon points="12,3 5,17 19,17" stroke="#ccc" stroke-width="1" fill="none" />
                    <polygon points="9,10 9,17 15,17 15,10" stroke="currentColor" stroke-width="1" fill="none" />
                  </svg>
                  Base
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/the-world" class="hidden <%= action_name == 'the_world' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"></path>
            </svg>
            The World
          </a>
          <% if ['the_world', '2027', 'transit_forecast', 'transit_theme_analysis'].include?(action_name) %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/the-world/2027" class="<%= action_name == '2027' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for 2027 submenu -->
                  2027
                </a>
              </li>
              <li>
                <a href="/the-world/transit-forecast" class="<%= action_name == 'transit_forecast' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Transit Forecast submenu -->
                  Transit Forecast
                </a>
              </li>
              <li>
                <a href="/the-world/transit-theme-analysis" class="<%= action_name == 'transit_theme_analysis' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Transit Theme Analysis submenu -->
                  Transit Theme Analysis
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/research-center" class="<%= action_name == 'research_center' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M7 19c-1.1 0-2 .9-2 2h14c0-1.1-.9-2-2-2h-4v-2h3c1.1 0 2-.9 2-2h-8c-1.66 0-3-1.34-3-3 0-1.09.59-2.04 1.46-2.56C8.17 9.03 8 8.54 8 8c0-.21.04-.42.09-.62C6.28 8.13 5 9.92 5 12c0 2.76 2.24 5 5 5v2H7z"></path>
              <path d="M10.56 5.51C11.91 5.54 13 6.64 13 8c0 .75-.33 1.41-.85 1.87l.59 1.62.94-.34.34.94 1.88-.68-.34-.94.94-.34-2.74-7.53-.94.34-.34-.94-1.88.68.34.94-.94.35.56 1.54z"></path>
              <circle cx="10.5" cy="8" r="1.5"></circle>
            </svg>
            Research Center
          </a>
          <% if ['research_center', 'famous_people', 'historical_figures', 'historical_events', 'global_cycles'].include?(action_name) || controller_name == 'amino_acid_explorer' %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/research-center/famous-people" class="<%= action_name == 'famous_people' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6">
                  <!-- Add the SVG icon for Famous People submenu -->
                  Famous People
                </a>
              </li>
              <li style="display: none;">
                <a href="/research-center/historical-figures" class="<%= action_name == 'historical_figures' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6">
                  <!-- Add the SVG icon for Historical Figures submenu -->
                  Historical Figures
                </a>
              </li>
              <li style="display: none;">
                <a href="/research-center/historical-events" class="<%= action_name == 'historical_events' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6">
                  <!-- Add the SVG icon for Historical Events submenu -->
                  Historical Events
                </a>
              </li>
              <li style="display: none;">
                <a href="/research-center/global-cycles" class="<%= action_name == 'global_cycles' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font">
                  <!-- Add the SVG icon for Global Cycles submenu -->
                  Global Cycles
                </a>
              </li>
              <li style="display: none;">
                <a href="/research-center/amino-acid-explorer" class="<%= controller_name == 'amino_acid_explorer' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font">
                  <!-- Add the SVG icon for Amino Acid Explorer submenu -->
                  Amino Acid Explorer
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/streaming-videos" class="<%= action_name == 'streaming_videos' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg style="fill: #999" class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" height="24"  viewBox="0 -960 960 960" width="24"><path d="m380-300 280-180-280-180v360ZM480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>
            Streaming Videos
          </a>
        </li>
        <li>
          <a href="#" onclick="openAudioPlayer()" class="text-gray-700 hover:text-indigo-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="#999" height="24" viewBox="0 -960 960 960" width="24" style="position: relative; top: -3px; left: 1px;">
              <path d="M160-80q-33 0-56.5-23.5T80-160v-480q0-25 13.5-45t36.5-29l506-206 26 66-330 134h468q33 0 56.5 23.5T880-640v480q0 33-23.5 56.5T800-80H160Zm0-80h640v-280H160v280Zm160-40q42 0 71-29t29-71q0-42-29-71t-71-29q-42 0-71 29t-29 71q0 42 29 71t71 29ZM160-520h480v-80h80v80h80v-120H160v120Zm0 360v-280 280Z"/>
            </svg>
            Audio Player
          </a>
        </li>
        <script>
          function openAudioPlayer() {
            // Adjust these values according to your desired dimensions
            var width = 485;
            var height = 300;
            var left = (screen.width - width) / 2;
            var top = (screen.height - height) / 2;

            // Open the new window with the specified dimensions
            window.open('/audio-player', 'Audio Player', 'width=' + width + ', height=' + height + ', top=' + top + ', left=' + left);
          }
        </script>
        <li>
          <a href="/learning-center" class="hidden <%= action_name == 'learning_center' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3 1 9l11 6 9-4.91V17h2V9L12 3z"></path>
            </svg>
            Learning Center
          </a>
          <% if ['learning_center', 'audio_library', 'video_library', 'books', 'rave_i_ching', 'classical_i_ching', 'printouts'].include?(action_name) %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/learning-center/audio-library" class="<%= action_name == 'audio_library' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Audio Library submenu -->
                  Audio Library
                </a>
              </li>
              <li>
                <a href="/learning-center/video-library" class="<%= action_name == 'video_library' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Video Library submenu -->
                  Video Library
                </a>
              </li>
              <li>
                <a href="/learning-center/books" class="<%= action_name == 'books' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Books submenu -->
                  Books
                </a>
              </li>
              <li>
                <a href="/learning-center/rave-i-ching" class="<%= action_name == 'rave_i_ching' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Rave I Ching submenu -->
                  Rave I'Ching
                </a>
              </li>
              <li>
                <a href="/learning-center/classical-i-ching" class="<%= action_name == 'classical_i_ching' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Classical I Ching submenu -->
                  Classical I'Ching
                </a>
              </li>
              <li>
                <a href="/learning-center/printouts" class="<%= action_name == 'printouts' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Classical I Ching submenu -->
                  Printouts
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/mystic-corner" class="hidden <%= action_name == 'mystic_corner' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M9 4v1.38c-.83-.33-1.72-.5-2.61-.5-1.79 0-3.58.68-4.95 2.05l3.33 3.33h1.11v1.11c.86.86 1.98 1.31 3.11 1.36V15H6v3c0 1.1.9 2 2 2h10c1.66 0 3-1.34 3-3V4H9zm-1.11 6.41V8.26H5.61L4.57 7.22a5.07 5.07 0 0 1 1.82-.34c1.34 0 2.59.52 3.54 1.46l1.41 1.41-.2.2c-.51.51-1.19.8-1.92.8-.47 0-.93-.12-1.33-.34zM19 17c0 .55-.45 1-1 1s-1-.45-1-1v-2h-6v-2.59c.57-.23 1.1-.57 1.56-1.03l.2-.2L15.59 14H17v-1.41l-6-5.97V6h8v11z"></path>
            </svg>
            Mystic Corner
          </a>
          <% if ['mystic_corner', 'mystic_way_analyzer', 'historical_mystics', 'incarnation_sequencing', 'base_theory'].include?(action_name) %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/mystic-corner/mystic-way-analyzer" class="<%= action_name == 'mystic_way_analyzer' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Mystic Way Analyzer submenu -->
                  Mystic Way Analyzer
                </a>
              </li>
              <li>
                <a href="/mystic-corner/historical-mystics" class="<%= action_name == 'historical_mystics' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Historical Mystics submenu -->
                  Historical Mystics
                </a>
              </li>
              <li>
                <a href="/mystic-corner/incarnation-sequencing" class="<%= action_name == 'incarnation_sequencing' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Incarnation Sequencing submenu -->
                  Incarnation Sequencing
                </a>
              </li>
              <li>
                <a href="/mystic-corner/base-theory" class="<%= action_name == 'base_theory' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Base Theory submenu -->
                  Base Theory
                </a>
              </li>
            </ul>
          <% end %>
        </li>
        <li>
          <a href="/special-interest" class="hidden <%= action_name == 'special_interest' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <path d="M7.02 13c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zM13 13v8h8v-8h-8zM7 2l-5 9h10L7 2zm12.25.5c-1.06 0-1.81.56-2.25 1.17-.44-.61-1.19-1.17-2.25-1.17C13.19 2.5 12 3.78 12 5.25c0 2 2.42 3.42 5 5.75 2.58-2.33 5-3.75 5-5.75 0-1.47-1.19-2.75-2.75-2.75z"></path>
            </svg>
            Special Interest
          </a>
          <% if %['special_interest', 'jungian_archetypes', 'jungian_typology', 'enneagram', 'tarot', 'cards_of_destiny'].include?(action_name) %>
            <ul class="pl-4 mt-1">
              <li>
                <a href="/special-interest/jungian-archetypes" class="<%= action_name == 'jungian_archetypes' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Jungian Archetypes submenu -->
                  Jungian Archetypes
                </a>
              </li>
              <li>
                <a href="/special-interest/jungian-typology" class="<%= action_name == 'jungian_typology' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Jungian Typology submenu -->
                  Jungian Typology
                </a>
              </li>
              <li>
                <a href="/special-interest/enneagram" class="<%= action_name == 'enneagram' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Enneagram submenu -->
                  Enneagram
                </a>
              </li>
              <li>
                <a href="/special-interest/tarot" class="<%= action_name == 'tarot' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Tarot submenu -->
                  Tarot
                </a>
              </li>
              <li>
                <a href="/special-interest/cards-of-destiny" class="<%= action_name == 'cards_of_destiny' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                  <!-- Add the SVG icon for Cards of Destiny submenu -->
                  Cards of Destiny
                </a>
              </li>
            </ul>
          <% end %>
        </li>
      </ul>
    </li>
    <li>
      <div class="text-xs font-semibold leading-6 text-gray-400">Settings</div>
      <ul role="list" class="-mx-2 mt-2 space-y-1">
        <li>
          <a href="/dashboard-settings" class="<%= action_name == 'dashboard-settings' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50' %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-[0.625rem] font-medium bg-white text-gray-400 border-gray-200 group-hover:border-indigo-600 group-hover:text-indigo-600">D</span>
            <span class="truncate">Dashboard Settings</span>
          </a>
        </li>
      </ul>
    </li>
    <li>
      <div class="text-xs font-semibold leading-6 text-gray-400">Tools</div>
      <ul role="list" class="-mx-2 mt-2 space-y-1">
        <li style="display: none;">
          <a href="/bodygraph-image-creator" class="<%= action_name == 'bodygraph_image_creator' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 shrink-0 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" aria-hidden="true">
              <circle cx="12" cy="6" r="2"></circle>
              <path d="M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25z"></path>
            </svg>
            Bodygraph Image Creator
          </a>
        </li>
        <li>
          <a href="/sequence-analyzer" class="<%= action_name == 'sequence_analyzer' ? 'bg-gray-50 text-indigo-600' : 'text-gray-700 hover:text-indigo-600 hover:bg-gray-50'  %> group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
            <svg class="h-6 w-6 text-gray-400 group-hover:text-indigo-600" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
              <!-- First square -->
              <rect x="5" y="12" width="4" height="4" stroke="currentColor" fill="none" />
              <!-- Second square -->
              <rect x="12" y="12" width="4" height="4" stroke="currentColor" fill="none" />
              <!-- Third square, offset -->
              <rect x="19" y="10" width="4" height="4" stroke="currentColor" fill="none" />
            </svg>
            Sequence Analyzer
          </a>
        </li>
      </ul>
    </li>
    <li class="">
      <a href="<%= edit_user_registration_path %>" class="items-center gap-x-4 py-3 text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-50">
        <span class="sr-only">Your profile</span>
        <span aria-hidden="true">Welcome, <%= current_user.email %>.</span>
      </a>
      <p class="text-xs text-gray-600 pt-1">Not you? <%= link_to 'Sign out', destroy_user_session_path, method: :delete, class: 'underline' %>.</p>
      <p class="text-xs text-gray-600 pt-8 font-bold">Additional Links</p>
      <p class="text-xs text-gray-600 underline"><a href="https://www.emergebydesign.net/">Emerge By Design</a></p>
      <p class="text-xs text-gray-600 underline"><a href="http://jonahdempcy.com/">Jonah Dempcy</a></p>
      <p class="text-xs text-gray-600 underline"><a href="http://centerforhumandesign.org/">The Center for Human Design</a></p>
      <p class="text-xs text-gray-600 underline"><a href="http://sfhdl.org/">The Santa Fe Human Design Library</a></p>
      <p class="text-xs text-gray-600 underline"><a href="http://highdeserthumandesign.com/">High Desert Human Design</a></p>
    </li>
  </ul>
</nav>
<!-- Navigation Resize Script -->
<script>
  function resizeNavigation() {
    const windowHeight = window.innerHeight;
    document.getElementById('main-navigation').style.height = `${windowHeight - 100}px`;
  }

  let resizeTimer;

  window.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(resizeNavigation, 500);
  });

  resizeNavigation();
</script>