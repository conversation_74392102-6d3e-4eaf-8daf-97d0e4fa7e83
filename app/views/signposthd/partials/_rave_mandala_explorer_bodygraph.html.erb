<svg id="rave-mandala-image" style="margin-top: 100px; margin-left: 100px;" transform="scale(1.4)" width="800" height="800" xmlns="http://www.w3.org/2000/svg">
  <circle id="circle-1" cx="400" cy="400" r="240" fill="transparent" stroke="#000" stroke-width=".8"></circle>
  <circle id="circle-2" cx="400" cy="400" r="270" fill="transparent" stroke="#000" stroke-width=".8"></circle>
  <circle id="circle-3" class="hidden" cx="400" cy="400" r="300" fill="transparent" stroke="#000" stroke-width=".8"></circle>
  <circle id="circle-4" class="hidden" cx="400" cy="400" r="330" fill="transparent" stroke="#000" stroke-width=".8"></circle>
  <circle id="godhead-circle" class="hidden" cx="400" cy="400" r="350" fill="transparent" stroke="#000" stroke-width=".8"></circle>
  <g transform="translate(400, 400) scale(3)"></g>
</svg>
<div id="godhead-info" class="hidden absolute" style="left: 835px; top: 37px;">
  <h1 id="godhead-name"></h1>
</div>
<div id="gate-info" class="hidden absolute" style="left: 835px; top: 37px;">
  <h1 id="gate-name" class="text-lg"></h1>
  <h2 id="gate-of" class="text-lg"></h2>
  <h3 class="relative" style="z-index: 50;"><span id="previous-gate" class="cursor-pointer">Previous</span> | <span id="next-gate" class="cursor-pointer">Next</span></h3>
  <h2 id="gate-glyph" style="font-size: 100px; line-height: 81px; left: -13px;" class="relative"></h2>
  <h3 id="gate-short-description"></h3>
  <h4 id="gate-godhead"></h4>
  <h3 id="gate-long-description"></h3>
</div>
<script>
  const showGodheads = localStorage.getItem('dashboardRaveMandalaShowGodheads') == 'true';
  const showZodiac = localStorage.getItem('dashboardRaveMandalaShowZodiac') == 'true';
  const useZodiacSymbols = localStorage.getItem('dashboardRaveMandalaUseZodiacSymbols') == 'true';
  let loaded = false;

  // Function to calculate coordinates of a point at a certain distance along a line
  document.addEventListener('DOMContentLoaded', function() {
    if (loaded) return;
    loaded = true;

    // Group to rotate astrological signs
    const group = document.createElementNS("http://www.w3.org/2000/svg", "g");
    group.setAttribute("transform", `rotate(-15 0 0)`);
    group.setAttribute('id', 'astrological-signs');
    document.getElementById('rave-mandala-image').querySelector('g').appendChild(group);

    // Group to rotate godhead labels
    const godheadGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
    godheadGroup.setAttribute("transform", `rotate(-258 0 0)`);
    godheadGroup.setAttribute('id', 'godhead-group');
    document.getElementById('rave-mandala-image').querySelector('g').appendChild(godheadGroup);

    // Create triangle highlights
    for (let index = 0; index < 64; index++) {
      let angle = (360 / 64) * index + (360 / 64);
      const nextAngle = (360 / 64) * (index + 1) + (360 / 64);

      // Calculate coordinates for the triangle vertices
      const x1 = 80 * Math.cos((angle * Math.PI) / 180);
      const y1 = 80 * Math.sin((angle * Math.PI) / 180);
      const x2 = 80 * Math.cos((nextAngle * Math.PI) / 180);
      const y2 = 80 * Math.sin((nextAngle * Math.PI) / 180);

      // Calculate the distance between (x1, y1) and (x2, y2)
      const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));

      // Create triangle
      const triangle = document.createElementNS("http://www.w3.org/2000/svg", "polygon");
      const [offsetX, offsetY] = calculatePointAlongLine(x1, y1, x2, y2, distance * 0.2);
      triangle.setAttribute("points", `${x1},${y1} ${0},${0} ${x2},${y2}`);
      triangle.setAttribute("fill", raveMandalaGateColors[svgRaveMandalaGateOrder[index]]);
      document.getElementById('rave-mandala-image').querySelector('g').appendChild(triangle);
    }

    // Godheads
    if (showGodheads) {
      if (showZodiac) {
        document.getElementById('godhead-circle').classList.remove('hidden');
      } else {
        document.getElementById('circle-4').classList.remove('hidden');
      }

      const godheadLineModifier = showZodiac ? 116.6 : 110;

      // Add lines for the godhead divisions
      for (let index = 0; index < 64; index++) {
        let angle = (360 / 64) * index + (360 / 64);
        if ((index + 1) % 4 == 0) {
          const x1 = godheadLineModifier * Math.cos((angle * Math.PI) / 180);
          const y1 = godheadLineModifier * Math.sin((angle * Math.PI) / 180);
          const x2 = 90 * Math.cos((angle * Math.PI) / 180); // Center x-coordinate
          const y2 = 90 * Math.sin((angle * Math.PI) / 180); // Center y-coordinate

          const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
          line.setAttribute("x1", x1);
          line.setAttribute("y1", y1);
          line.setAttribute("x2", x2);
          line.setAttribute("y2", y2);
          line.setAttribute("stroke", "#000");
          line.setAttribute("stroke-width", ".2");
          document.getElementById('rave-mandala-image').querySelector('g').appendChild(line);
        }
      }

      const godheadModifier = showZodiac ? 80 : 73;
      for (let godheadIndex = 0; godheadIndex < 16; godheadIndex++) {
        let angle = (360 / 16) * godheadIndex + (360 / 16);
        angle -= 1.9
        const x1 = godheadModifier * Math.cos((angle * Math.PI) / 180);
        const y1 = godheadModifier * Math.sin((angle * Math.PI) / 180);
        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        const fontSize = 3;
        text.setAttribute("x", x1); // Adjusted radius for the inner circle
        text.setAttribute("y", y1); // Adjusted radius for the inner circle
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("dominant-baseline", "bottom");
        text.setAttribute("dy", -30);
        text.setAttribute("font-size", fontSize);
        text.textContent = godheads[godheadIndex];
        text.style.cursor = "pointer";
        const rotation = angle + 93;
        text.setAttribute("transform", `rotate(${rotation} ${x1} ${y1})`);
        godheadGroup.appendChild(text); // e.g. <text>The Keepers of the Wheel</text>

        let zoomedInGodhead = '';
        text.onclick = function() {
            zoomedInGodhead = text.textContent; // e.g. 'The Keepers of the Wheel'
            let scaleIncrement = 0.02; // Increment for scaling up
            let currentScale = 1.4; // Initial scale
            const targetScale = 3.0; // Target scale
            let width = 800;
            let height = 800;
            marginLeft = 100;
            let marginTop = 100;

            let frame = 0;
            const scaleInterval = setInterval(function() {
              if (frame < 80) {
                frame++;
                currentScale += scaleIncrement;
                document.getElementById('rave-mandala-image').setAttribute("transform", `scale(${currentScale})`);
                width -= (800 - godheadCoordinates[zoomedInGodhead].width) / 80;
                height -= (800 - godheadCoordinates[zoomedInGodhead].height) / 80;
                marginLeft -= (100 - godheadCoordinates[zoomedInGodhead].marginLeft) / 80;
                marginTop -= (100 - godheadCoordinates[zoomedInGodhead].marginTop) / 80;
                document.getElementById('rave-mandala-image').style.marginLeft = marginLeft + 'px';
                document.getElementById('rave-mandala-image').style.marginTop = marginTop + 'px';
                document.getElementById('rave-mandala-image').setAttribute("width", width);
                document.getElementById('rave-mandala-image').setAttribute("height", height);
              } else {
                clearInterval(scaleInterval); // Stop the interval when target scale is reached
                window.addEventListener('click', godheadZoomOutHandler);
                document.getElementById('godhead-info').classList.remove('hidden');
                document.getElementById('godhead-name').innerText = zoomedInGodhead;
              }
            }, 1000 / 60); // 60 frames per second
        };

        function godheadZoomOutHandler(event) {
          let scaleIncrement = 0.02; // Increment for scaling up
          let currentScale = 3.0; // Initial scale
          const targetScale = 1.4; // Target scale
          let width = godheadCoordinates[zoomedInGodhead].width;
          let height = godheadCoordinates[zoomedInGodhead].height;
          let marginLeft = godheadCoordinates[zoomedInGodhead].marginLeft;
          let marginTop = godheadCoordinates[zoomedInGodhead].marginTop;

          document.getElementById('godhead-info').classList.add('hidden');

          let frame = 0;
          const scaleInterval = setInterval(function() {
              if (frame < 80) {
                frame++;
                currentScale -= scaleIncrement;
                document.getElementById('rave-mandala-image').setAttribute("transform", `scale(${currentScale})`);
                width += (800 - godheadCoordinates[zoomedInGodhead].width) / 80;
                height += (800 - godheadCoordinates[zoomedInGodhead].height) / 80;
                marginLeft += (100 - godheadCoordinates[zoomedInGodhead].marginLeft) / 80;
                marginTop += (100 - godheadCoordinates[zoomedInGodhead].marginTop) / 80;

                document.getElementById('rave-mandala-image').style.marginTop = marginTop + 'px';
                document.getElementById('rave-mandala-image').style.marginLeft = marginLeft + 'px';
                document.getElementById('rave-mandala-image').setAttribute("width", width);
                document.getElementById('rave-mandala-image').setAttribute("height", height);
              } else {
                  clearInterval(scaleInterval); // Stop the interval when target scale is reached
              }
          }, 1000 / 60); // 60 frames per second
          window.removeEventListener('click', godheadZoomOutHandler);
        }
      }
    } // End showGodheads

    // Lines for the 12 signs of the zodiac
    if (showZodiac) {
      document.getElementById('circle-3').classList.remove('hidden');

      for (let zodiacIndex = 0; zodiacIndex < 12; zodiacIndex++) {

        // Draw zodiac sign separators out from the middle
        let angle = (360 / 12) * zodiacIndex + (360 / 12);
        angle -= 1.9
        const x1 = 80 * Math.cos((angle * Math.PI) / 180);
        const y1 = 80 * Math.sin((angle * Math.PI) / 180);
        const x2 = 90 * Math.cos((angle * Math.PI) / 180);
        const y2 = 90 * Math.sin((angle * Math.PI) / 180);

        const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
        line.setAttribute("x1", x1);
        line.setAttribute("y1", y1);
        line.setAttribute("x2", x2);
        line.setAttribute("y2", y2);
        line.setAttribute("stroke", "#000");
        line.setAttribute("stroke-width", ".2");
        document.getElementById('rave-mandala-image').querySelector('g').appendChild(line);

        // Add text around the inner circle
        const text = document.createElementNS("http://www.w3.org/2000/svg", "text");
        const fontSize = useZodiacSymbols ? 6 : 3;
        text.setAttribute("x", x1);// Adjusted radius for the inner circle
        text.setAttribute("y", y1); // Adjusted radius for the inner circle
        text.setAttribute("text-anchor", "middle");
        text.setAttribute("dominant-baseline", "bottom");
        text.setAttribute("dy", useZodiacSymbols ? -2.8 : -4);
        text.setAttribute("font-size", fontSize);
        text.setAttribute("font-family", "'Noto Sans', sans-serif;");
        text.textContent = useZodiacSymbols ? astrologicalSignSymbols[zodiacIndex] : astrologicalSigns[zodiacIndex];
        const rotation = angle + 90;
        text.setAttribute("transform", `rotate(${rotation} ${x1} ${y1})`);
        group.appendChild(text); // e.g. <text>Pisces</text>
      }
    } // End lines for 12 signs of zodiac

    let currentZoomedInGate = -1; // Keeps track of currently zoomed-in gate

    // Add lines for the 64 gates
    for (let index = 0; index < 64; index++) {
      const currentGate = svgRaveMandalaGateOrder[index];
      let angle = (360 / 64) * index + (360 / 64);
      const x1 = 80 * Math.cos((angle * Math.PI) / 180);
      const y1 = 80 * Math.sin((angle * Math.PI) / 180);
      const x2 = 0; // Center x-coordinate
      const y2 = 0; // Center y-coordinate

      const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
      line.setAttribute("x1", x1);
      line.setAttribute("y1", y1);
      line.setAttribute("x2", x2);
      line.setAttribute("y2", y2);
      line.setAttribute("stroke", "#000");
      line.setAttribute("stroke-width", ".2");
      document.getElementById('rave-mandala-image').querySelector('g').appendChild(line);

      // Add numbers just outside the circle
      angle = angle + (360 / 128)
      const numberModifier = showZodiac ? .265 : .237;
      const number = document.createElementNS("http://www.w3.org/2000/svg", "text");

      number.setAttribute("x", numberModifier * (360 * Math.cos((angle * Math.PI) / 180)));
      number.setAttribute("y", numberModifier * (360 * Math.sin((angle * Math.PI) / 180)));
      number.setAttribute("text-anchor", "middle");
      number.setAttribute("dominant-baseline", "middle");
      number.setAttribute("font-size", 3);
      number.setAttribute('class', 'rave-mandala-gate-numbers');
      number.setAttribute("transform", `rotate(${angle + 90} ${numberModifier * (360 * Math.cos((angle * Math.PI) / 180))} ${numberModifier * (360 * Math.sin((angle * Math.PI) / 180))})`);
      number.style.fontWeight = "bold";
      number.textContent = currentGate;
      number.style.cursor = "pointer";
      document.getElementById('rave-mandala-image').querySelector('g').appendChild(number);

      number.onclick = function() {
        zoomedInGate = number.textContent; // e.g. '64'
        const rotationIncrement = ((svgRaveMandalaGateOrder.indexOf(parseInt(zoomedInGate)) * -5.625) - 39.375) / 80;

        let scaleIncrement = 0.04; // Increment for scaling up
        let currentScale = 1.4; // Initial scale
        const targetScale = 6.0; // Target scale
        let width = 800;
        let height = 800;
        marginLeft = 100;
        let marginTop = 100;
        let currentRotation = 0; // Initial rotation

        let frame = 0;
        const scaleInterval = setInterval(function() {
          if (frame < 80) {
            frame++;
            currentRotation += rotationIncrement;
            document.getElementById('rave-mandala-image').querySelector('g').setAttribute("transform", `translate(400, 400) scale(3) rotate(${currentRotation} 0 0)`);
            height -= (800 - 284) / 80;
            marginLeft -= (100 - -1408) / 80;
            marginTop -= (100 - -330) / 80;
            currentScale += scaleIncrement;
            document.getElementById('rave-mandala-image').setAttribute("transform", `scale(${currentScale})`);
            document.getElementById('rave-mandala-image').style.marginLeft = marginLeft + 'px';
            document.getElementById('rave-mandala-image').style.marginTop = marginTop + 'px';
            document.getElementById('rave-mandala-image').setAttribute("height", height);
          } else {
            clearInterval(scaleInterval); // Stop the interval when target scale is reached
            window.addEventListener('click', gateZoomOutHandler);
            currentZoomedInGate = zoomedInGate;
            updateGateInfo(zoomedInGate);
          }
        }, 1000 / 60); // 60 frames per second
      };

      const glyphModifier = showZodiac ? .288 : .265;

      const glyph = document.createElementNS("http://www.w3.org/2000/svg", "text");
      glyph.setAttribute("x", glyphModifier * (360 * Math.cos((angle * Math.PI) / 180)));
      glyph.setAttribute("y", glyphModifier * (360 * Math.sin((angle * Math.PI) / 180)));
      glyph.setAttribute("text-anchor", "middle");
      glyph.setAttribute("dominant-baseline", "middle");
      glyph.setAttribute("font-size", 6);
      glyph.setAttribute('class', 'rave-mandala-gate-glyphs')
      glyph.setAttribute("transform", `rotate(${angle + 90} ${glyphModifier * (360 * Math.cos((angle * Math.PI) / 180))} ${glyphModifier * (360 * Math.sin((angle * Math.PI) / 180))})`);
      glyph.textContent = iChingHexagramGlyphs[currentGate];
      document.getElementById('rave-mandala-image').querySelector('g').appendChild(glyph);
    } // End lines for 64 gates

    function gateZoomOutHandler() {
      if (event.target.id === 'previous-gate' || event.target.id === 'next-gate') {
          let index = svgRaveMandalaGateOrder.indexOf(parseInt(currentZoomedInGate));
          const rotationIncrement = ((index * 5.625) + 39.375) / 80;
          let currentRotation = -rotationIncrement * 80;
          if (event.target.id === 'next-gate') {
            index++;
            if (index == svgRaveMandalaGateOrder.length) index = 0;
            currentRotation -= 5.625;
          } else {
            index--;
            if (index == 0) index = svgRaveMandalaGateOrder.length - 1;
            currentRotation += 5.625;
          }
          currentZoomedInGate = svgRaveMandalaGateOrder[index];
          updateGateInfo(currentZoomedInGate);
          document.getElementById('rave-mandala-image').querySelector('g').setAttribute("transform", `translate(400, 400) scale(3) rotate(${currentRotation} 0 0)`);
        return;
      }
      let scaleIncrement = 0.04; // Increment for scaling up
      let currentScale = 4.6; // Initial scale
      const targetScale = 1.4; // Target scale
      let width = 800;
      let height = 284;
      let marginLeft = -1408;
      let marginTop = -330;
      const rotationIncrement = ((svgRaveMandalaGateOrder.indexOf(parseInt(zoomedInGate)) * 5.625)) / 80;
      let currentRotation = -rotationIncrement * 80;

      document.getElementById('gate-info').classList.add('hidden');

      let frame = 0;
      const scaleInterval = setInterval(function() {
          if (frame < 80) {
            frame++;
            currentRotation += rotationIncrement;
            document.getElementById('rave-mandala-image').querySelector('g').setAttribute("transform", `translate(400, 400) scale(3) rotate(${currentRotation} 0 0)`);
            currentScale -= scaleIncrement;
            document.getElementById('rave-mandala-image').setAttribute("transform", `scale(${currentScale})`);
            height += (800 - 284) / 80;
            marginLeft += (100 - -1408) / 80;
            marginTop += (100 - -330) / 80;

            document.getElementById('rave-mandala-image').style.marginTop = marginTop + 'px';
            document.getElementById('rave-mandala-image').style.marginLeft = marginLeft + 'px';
            document.getElementById('rave-mandala-image').setAttribute("width", width);
            document.getElementById('rave-mandala-image').setAttribute("height", height);
          } else {
              clearInterval(scaleInterval); // Stop the interval when target scale is reached
          }
      }, 1000 / 60); // 60 frames per second
      window.removeEventListener('click', gateZoomOutHandler);
    }

    // Utility methods
    function calculatePointAlongLine(x1, y1, x2, y2, distance) {
      const totalDistance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
      const ratio = distance / totalDistance;
      const newX = x1 + ratio * (x2 - x1);
      const newY = y1 + ratio * (y2 - y1);
      return [newX, newY];
    }

    function updateGateInfo(zoomedInGate) {
      document.getElementById('gate-info').classList.remove('hidden');
      document.getElementById('gate-name').innerText = 'Gate ' + zoomedInGate + ' — ' + gateNames[zoomedInGate];
      document.getElementById('gate-of').innerHTML = gateOf[zoomedInGate] + '<br />';
      document.getElementById('gate-glyph').innerText = iChingHexagramGlyphs[zoomedInGate];
      document.getElementById('gate-short-description').innerHTML = gateShortDescriptions[zoomedInGate];
      document.getElementById('gate-godhead').innerText = 'Godhead: ' + godheadsByGate[zoomedInGate];
      let iChing = localStorage.getItem('customIChing');
      if (iChing) {
        iChing = JSON.parse(iChing);
        document.getElementById('gate-long-description').innerText = iChing['gates'][`${zoomedInGate}.gateLongDescription`];
      }
    }

  });
</script>
