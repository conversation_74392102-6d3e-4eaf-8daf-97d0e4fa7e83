<script>
  // All JSON data for the latest entry (see: static_pages_controller.rb)
  const latestEntry = JSON.parse('<%= raw @latest_entry_json %>');
  const personalityActivations = latestEntry; // Used as a global variable by <PERSON>ve Mandala
  const activatedGates = {};
  const personalityGates = {};
  const personalityPlanetsByGate = {};
  Object.keys(latestEntry).forEach(planet => {
    const gate = latestEntry[planet].g;
    const line = latestEntry[planet].l;
    activatedGates[gate] = true;
    personalityGates[gate] = true;
    if (typeof personalityPlanetsByGate[gate] === 'undefined') {
      personalityPlanetsByGate[gate] = [];
    }
    personalityPlanetsByGate[gate].push(planet);
  });

  // Read settings from localStorage
  const isAminoAcidMode = localStorage.getItem('isAminoAcidMode') == 'true';
  const showGateAndLineInfo = localStorage.getItem('dashboardGateAndLineInfo') === 'true';
  const chartMode = localStorage.getItem('dashboardChartMode'); // 'standard-mode' | 'rave-mandala-mode'
  const showGodheads = localStorage.getItem('dashboardRaveMandalaShowGodheads') == 'true';
  const showZodiac = localStorage.getItem('dashboardRaveMandalaShowZodiac') == 'true';
  const useZodiacSymbols = localStorage.getItem('dashboardRaveMandalaUseZodiacSymbols') == 'true';

  // Signpost Data Console message
  const signpostDataConsoleMessages = [' ____  __  ___  __ _  ____   __   ____  ____ \r\n\/ ___)(  )\/ __)(  ( \\(  _ \\ \/  \\ \/ ___)(_  _)\r\n\\___ \\ )(( (_ \\\/    \/ ) __\/(  O )\\___ \\  )(  \r\n(____\/(__)\\___\/\\_)__)(__)   \\__\/ (____\/ (__) \r\n ____   __  ____  __                         \r\n(    \\ \/ _\\(_  _)\/ _\\                        \r\n ) D (\/    \\ )( \/    \\                       \r\n(____\/\\_\/\\_\/(__)\\_\/\\_\/                       \r\n  ___  __   __ _  ____   __   __    ____     \r\n \/ __)\/  \\ (  ( \\\/ ___) \/  \\ (  )  (  __)    \r\n( (__(  O )\/    \/\\___ \\(  O )\/ (_\/\\ ) _)     \r\n \\___)\\__\/ \\_)__)(____\/ \\__\/ \\____\/(____)',
                                        '  ________  __     _______   _____  ___     _______    ______    ________  ___________  \r\n \/\"       )|\" \\   \/\" _   \"| (\\\"   \\|\"  \\   |   __ \"\\  \/    \" \\  \/\"       )(\"     _   \") \r\n(:   \\___\/ ||  | (: ( \\___) |.\\\\   \\    |  (. |__) :)\/\/ ____  \\(:   \\___\/  )__\/  \\\\__\/  \r\n \\___  \\   |:  |  \\\/ \\      |: \\.   \\\\  |  |:  ____\/\/  \/    ) :)\\___  \\       \\\\_ \/     \r\n  __\/  \\\\  |.  |  \/\/  \\ ___ |.  \\    \\. |  (|  \/   (: (____\/ \/\/  __\/  \\\\      |.  |     \r\n \/\" \\   :) \/\\  |\\(:   _(  _||    \\    \\ | \/|__\/ \\   \\        \/  \/\" \\   :)     \\:  |     \r\n(_______\/ (__\\_|_)\\_______)  \\___|\\____\\)(_______)   \\\"_____\/  (_______\/       \\__|     \r\n                                                                                        \r\n ________       __  ___________   __                                                    \r\n|\"      \"\\     \/\"\"\\(\"     _   \") \/\"\"\\                                                   \r\n(.  ___  :)   \/    \\)__\/  \\\\__\/ \/    \\                                                  \r\n|: \\   ) ||  \/\' \/\\  \\  \\\\_ \/   \/\' \/\\  \\                                                 \r\n(| (___\\ || \/\/  __\'  \\ |.  |  \/\/  __\'  \\                                                \r\n|:       :)\/   \/  \\\\  \\\\:  | \/   \/  \\\\  \\                                               \r\n(________\/(___\/    \\___)\\__|(___\/    \\___)                                              \r\n                                                                                        \r\n  ______    ______    _____  ___    ________   ______    ___       _______              \r\n \/\" _  \"\\  \/    \" \\  (\\\"   \\|\"  \\  \/\"       ) \/    \" \\  |\"  |     \/\"     \"|             \r\n(: ( \\___)\/\/ ____  \\ |.\\\\   \\    |(:   \\___\/ \/\/ ____  \\ ||  |    (: ______)             \r\n \\\/ \\    \/  \/    ) :)|: \\.   \\\\  | \\___  \\  \/  \/    ) :)|:  |     \\\/    |               \r\n \/\/  \\ _(: (____\/ \/\/ |.  \\    \\. |  __\/  \\\\(: (____\/ \/\/  \\  |___  \/\/ ___)_              \r\n(:   _) \\\\        \/  |    \\    \\ | \/\" \\   :)\\        \/  ( \\_|:  \\(:      \"|             \r\n \\_______)\\\"_____\/    \\___|\\____\\)(_______\/  \\\"_____\/    \\_______)\\_______)             \r\n                                                                                        ',
                                        ' _______  ___   _______  __    _  _______  _______  _______  _______ \r\n|       ||   | |       ||  |  | ||       ||       ||       ||       |\r\n|  _____||   | |    ___||   |_| ||    _  ||   _   ||  _____||_     _|\r\n| |_____ |   | |   | __ |       ||   |_| ||  | |  || |_____   |   |  \r\n|_____  ||   | |   ||  ||  _    ||    ___||  |_|  ||_____  |  |   |  \r\n _____| ||   | |   |_| || | |   ||   |    |       | _____| |  |   |  \r\n|_______||___| |_______||_|  |__||___|    |_______||_______|  |___|  \r\n ______   _______  _______  _______                                  \r\n|      | |   _   ||       ||   _   |                                 \r\n|  _    ||  |_|  ||_     _||  |_|  |                                 \r\n| | |   ||       |  |   |  |       |                                 \r\n| |_|   ||       |  |   |  |       |                                 \r\n|       ||   _   |  |   |  |   _   |                                 \r\n|______| |__| |__|  |___|  |__| |__|                                 \r\n _______  _______  __    _  _______  _______  ___      _______       \r\n|       ||       ||  |  | ||       ||       ||   |    |       |      \r\n|       ||   _   ||   |_| ||  _____||   _   ||   |    |    ___|      \r\n|       ||  | |  ||       || |_____ |  | |  ||   |    |   |___       \r\n|      _||  |_|  ||  _    ||_____  ||  |_|  ||   |___ |    ___|      \r\n|     |_ |       || | |   | _____| ||       ||       ||   |___       \r\n|_______||_______||_|  |__||_______||_______||_______||_______|      ',
                                        '\u2591\u2591      \u2591\u2591\u2591        \u2591\u2591\u2591      \u2591\u2591\u2591   \u2591\u2591\u2591  \u2591\u2591       \u2591\u2591\u2591\u2591      \u2591\u2591\u2591\u2591      \u2591\u2591\u2591        \u2591\r\n\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592    \u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\r\n\u2593\u2593      \u2593\u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593   \u2593\u2593  \u2593  \u2593  \u2593\u2593       \u2593\u2593\u2593  \u2593\u2593\u2593\u2593  \u2593\u2593\u2593      \u2593\u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593\r\n\u2588\u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588  \u2588\u2588    \u2588\u2588  \u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\r\n\u2588\u2588      \u2588\u2588\u2588        \u2588\u2588\u2588      \u2588\u2588\u2588  \u2588\u2588\u2588   \u2588\u2588  \u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588      \u2588\u2588\u2588\u2588      \u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\r\n                                                                                \r\n\u2591       \u2591\u2591\u2591\u2591      \u2591\u2591\u2591        \u2591\u2591\u2591      \u2591\u2591                                        \r\n\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592  \u2592                                        \r\n\u2593  \u2593\u2593\u2593\u2593  \u2593\u2593  \u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593  \u2593                                        \r\n\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588        \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588        \u2588                                        \r\n\u2588       \u2588\u2588\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588  \u2588                                        \r\n                                                                                \r\n\u2591\u2591      \u2591\u2591\u2591\u2591      \u2591\u2591\u2591   \u2591\u2591\u2591  \u2591\u2591\u2591      \u2591\u2591\u2591\u2591      \u2591\u2591\u2591  \u2591\u2591\u2591\u2591\u2591\u2591\u2591\u2591        \u2591          \r\n\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592    \u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592  \u2592\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592\u2592  \u2592\u2592\u2592\u2592\u2592\u2592\u2592          \r\n\u2593  \u2593\u2593\u2593\u2593\u2593\u2593\u2593\u2593  \u2593\u2593\u2593\u2593  \u2593\u2593  \u2593  \u2593  \u2593\u2593\u2593      \u2593\u2593\u2593  \u2593\u2593\u2593\u2593  \u2593\u2593  \u2593\u2593\u2593\u2593\u2593\u2593\u2593\u2593      \u2593\u2593\u2593          \r\n\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588  \u2588\u2588    \u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588  \u2588\u2588\u2588\u2588  \u2588\u2588  \u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588  \u2588\u2588\u2588\u2588\u2588\u2588\u2588          \r\n\u2588\u2588      \u2588\u2588\u2588\u2588      \u2588\u2588\u2588  \u2588\u2588\u2588   \u2588\u2588\u2588      \u2588\u2588\u2588\u2588      \u2588\u2588\u2588        \u2588\u2588        \u2588          \r\n                                                                                ',
                                        '   _____ _                              __ \r\n  \/ ___\/(_)___ _____  ____  ____  _____\/ \/_\r\n  \\__ \\\/ \/ __ `\/ __ \\\/ __ \\\/ __ \\\/ ___\/ __\/\r\n ___\/ \/ \/ \/_\/ \/ \/ \/ \/ \/_\/ \/ \/_\/ (__  ) \/_  \r\n\/____\/_\/\\__, \/_\/ \/_\/ .___\/\\____\/____\/\\__\/  \r\n    ___\/____\/   __\/_\/                      \r\n   \/ __ \\____ _\/ \/_____ _                  \r\n  \/ \/ \/ \/ __ `\/ __\/ __ `\/                  \r\n \/ \/_\/ \/ \/_\/ \/ \/_\/ \/_\/ \/                   \r\n\/_____\/\\__,_\/\\__\/\\__,_\/         __         \r\n  \/ ____\/___  ____  _________  \/ \/__       \r\n \/ \/   \/ __ \\\/ __ \\\/ ___\/ __ \\\/ \/ _ \\      \r\n\/ \/___\/ \/_\/ \/ \/ \/ (__  ) \/_\/ \/ \/  __\/      \r\n\\____\/\\____\/_\/ \/_\/____\/\\____\/_\/\\___\/       \r\n                                           ',
                                        '                                                        \r\n ,---.  ,--.                                     ,--.   \r\n\'   .-\' `--\' ,---. ,--,--,  ,---.  ,---.  ,---.,-\'  \'-. \r\n`.  `-. ,--.| .-. ||      \\| .-. || .-. |(  .-\'\'-.  .-\' \r\n.-\'    ||  |\' \'-\' \'|  ||  || \'-\' \'\' \'-\' \'.-\'  `) |  |   \r\n`-----\' `--\'.`-  \/ `--\'\'--\'|  |-\'  `---\' `----\'  `--\'   \r\n,------.    `---\'   ,--.   `--\'                         \r\n|  .-.  \\  ,--,--.,-\'  \'-. ,--,--.                      \r\n|  |  \\  :\' ,-.  |\'-.  .-\'\' ,-.  |                      \r\n|  \'--\'  \/\\ \'-\'  |  |  |  \\ \'-\'  |                      \r\n`-------\'  `--`--\'  `--\'   `--`--\'                      \r\n ,-----.                             ,--.               \r\n\'  .--.\/ ,---. ,--,--,  ,---.  ,---. |  | ,---.         \r\n|  |    | .-. ||      \\(  .-\' | .-. ||  || .-. :        \r\n\'  \'--\'\\\' \'-\' \'|  ||  |.-\'  `)\' \'-\' \'|  |\\   --.        \r\n `-----\' `---\' `--\'\'--\'`----\'  `---\' `--\' `----\'        \r\n                                                        ',
                                        '\u250F\u2513\u2022          \r\n\u2517\u2513\u2513\u250F\u2513\u250F\u2513\u250F\u2513\u250F\u2513\u250F\u254B\r\n\u2517\u251B\u2517\u2517\u252B\u251B\u2517\u2523\u251B\u2517\u251B\u251B\u2517\r\n\u2533\u2513  \u251B  \u251B     \r\n\u2503\u2503\u250F\u2513\u254B\u250F\u2513      \r\n\u253B\u251B\u2517\u253B\u2517\u2517\u253B      \r\n\u250F\u2513       \u2513   \r\n\u2503 \u250F\u2513\u250F\u2513\u250F\u250F\u2513\u2503\u250F\u2513 \r\n\u2517\u251B\u2517\u251B\u251B\u2517\u251B\u2517\u251B\u2517\u2517  \r\n             ',
                                        '   ___      _      __ _            _ __                    _     \r\n  \/ __|    (_)    \/ _` |  _ _     | \'_ \\   ___     ___    | |_   \r\n  \\__ \\    | |    \\__, | | \' \\    | .__\/  \/ _ \\   (_-<    |  _|  \r\n  |___\/   _|_|_   |___\/  |_||_|   |_|__   \\___\/   \/__\/_   _\\__|  \r\n_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"| \r\n\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\' \r\n   ___             _                                             \r\n  |   \\   __ _    | |_    __ _                                   \r\n  | |) | \/ _` |   |  _|  \/ _` |                                  \r\n  |___\/  \\__,_|   _\\__|  \\__,_|                                  \r\n_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|                                 \r\n\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'                                 \r\n   ___                                      _                    \r\n  \/ __|    ___    _ _      ___     ___     | |     ___           \r\n | (__    \/ _ \\  | \' \\    (_-<    \/ _ \\    | |    \/ -_)          \r\n  \\___|   \\___\/  |_||_|   \/__\/_   \\___\/   _|_|_   \\___|          \r\n_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|_|\"\"\"\"\"|         \r\n\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'\"`-0-0-\'         '
                                      ];

  const regular = 'font-family: Courier New, Courier, monospace; color: darkgreen;';
  const bold = 'font-family: Courier New, Courier, monospace; color: darkgreen; font-weight: bold;';
  console.log('');
  console.log('');
  console.log(
    '%c' + signpostDataConsoleMessages[parseInt(Math.random() * signpostDataConsoleMessages.length)],
      [regular, bold][parseInt(Math.random())]
  );
  console.log('');
  console.log('%cDate', bold);
  console.log('%c----', bold);
  console.log('%c<%= @date + 7.hours %>', bold);
  console.log('');
  console.log('%cSun Gate & Line', bold);
  console.log('%c---------------', bold);
  console.log('%c' + latestEntry.Sun.g + '.' + latestEntry.Sun.l, bold);
  console.log('');
  console.log('%cFull Entry Details', bold);
  console.log('%c------------------', bold);
  console.log(latestEntry);
  console.log('');
  console.log('');

  // on DOMContentLoaded
  document.addEventListener('DOMContentLoaded', function() {
    ['standard-mode', 'rave-mandala-mode'].forEach(buttonId => {
      document.getElementById(buttonId).addEventListener('click', () => selectChartMode(buttonId));
    });

    if (chartMode) {
      selectChartMode(chartMode);
    }
  });

  function selectChartMode(buttonId) {
    localStorage.setItem('dashboardChartMode', buttonId);
    ['standard-mode', 'rave-mandala-mode'].forEach(id => {
      const currentButton = document.getElementById(id);
      const isCurrentButton = id === buttonId;

      if (isCurrentButton) {
        currentButton.setAttribute('aria-selected', 'true');
        currentButton.classList.add('bg-white');
        currentButton.classList.add('shadow');
      } else {
        currentButton.setAttribute('aria-selected', 'false');
        currentButton.classList.remove('bg-white');
        currentButton.classList.remove('shadow');
      }
    });

    switch (buttonId) {
      case 'standard-mode':
        document.getElementById('standard-mode-wrapper').style.marginLeft = '520px';
        document.getElementById('standard-mode-text').classList.remove('hidden');
        document.getElementById('amino-acid-info').classList.remove('hidden');
        document.getElementById('rave-mandala-mode-wrapper').classList.add('hidden');
        document.getElementById('bodygraph').width = '430';
        document.getElementById('bodygraph').style = 'left: 54px; top: 22px;';
        document.getElementById('bodygraph-bg').width = '1500px';
        document.getElementById('bodygraph-bg').style = 'left: 0; top: 30px; opacity: .8;'
        break;
      case 'rave-mandala-mode':
        const raveMandalaModeWrapper = document.getElementById('rave-mandala-mode-wrapper');
        const bodygraph = document.getElementById('bodygraph');
        const bodygraphBg = document.getElementById('bodygraph-bg');
        if (showZodiac && showGodheads) {
          bodygraph.style = `left: 291.5px; top: 177px;`;
          bodygraphBg.style = `left: 252px; top: 185px; opacity: 1;`;
          raveMandalaModeWrapper.style.left = 60;
          raveMandalaModeWrapper.style.top = 0;
        } else if (showZodiac || showGodheads) {
          bodygraph.style = `left: 291.5px; top: 182px;`;
          bodygraphBg.style = `left: 249px; top: 182px; opacity: 1;`;
          raveMandalaModeWrapper.style.left = 60;
          raveMandalaModeWrapper.style.top = 0;
        } else {
          bodygraph.style = `left: 231px; top: 101px;`;
          bodygraphBg.style = `left: 190px; top: 106px; opacity: 1;`;
          raveMandalaModeWrapper.style.left = 0;
          raveMandalaModeWrapper.style.top = '-70px';
        }

        document.getElementById('standard-mode-wrapper').style.marginLeft = '115px';
        document.getElementById('standard-mode-text').classList.add('hidden');
        document.getElementById('amino-acid-info').classList.add('hidden');
        document.getElementById('rave-mandala-mode-wrapper').classList.remove('hidden');
        document.getElementById('bodygraph').width = '341.55';
        document.getElementById('bodygraph-bg').width = '1191.375';
        break;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const dateParam = urlParams.get('date');

    if (dateParam !== null && dateParam !== '') {
      document.getElementById('standard-mode-text').classList.add('hidden');
    }
  } // end selectChartMode(chartMode)

  document.addEventListener('DOMContentLoaded', function() {
    // Set default selected options from local storage on page load
    document.querySelectorAll('#dashboard-form select').forEach(function(select) {
      const savedPreference = localStorage.getItem(select.name);
      if (savedPreference) {
        select.value = savedPreference;
      }
    });
    updateGateOrLineInput();
  });

  function updateGateOrLineInput() {
    if (document.getElementById('division').value.indexOf('#') != -1) {
      document.getElementById("gate_or_line_number_").classList.remove("hidden");
      const selectedPlanet = document.getElementById("planet").value;
      const gateOrLine = document.getElementById('division').value.indexOf('Gate') != -1 ? 'g' : 'l';
      document.getElementById("gate_or_line_number_").value = latestEntry[selectedPlanet][gateOrLine];
    } else {
      document.getElementById("gate_or_line_number_").classList.add("hidden");
    }
  }

  // Reminders
  document.addEventListener("DOMContentLoaded", function() {
    document.getElementById('new_reminder').addEventListener('submit', function(event) {
      event.preventDefault();
      document.getElementById('timezone').value = Intl.DateTimeFormat().resolvedOptions().timeZone;
      var formData = new FormData(this);
      fetch('/reminders', {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': Rails.csrfToken(),
          'Accept': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        // Create a div element to display the success message
        var successMessage = document.createElement('div');
        successMessage.textContent = 'Reminder added.';
        successMessage.classList.add('text-green-600', 'mt-2');

        // Insert the success message before the form
        this.parentNode.insertBefore(successMessage, this);

        document.getElementById('new_reminder').style.display = 'none';

        // After 5 seconds, fade out the success message
        setTimeout(function() {
          successMessage.style.opacity = '0';
          successMessage.style.transition = 'opacity 2s ease';
        }, 2000);

        setTimeout(function() {
          successMessage.style.display = 'none';
        }, 4000);

      })
      .catch(error => console.error('Error:', error));
    });
  });

  // Draw the bodygraph and Rave Mandala
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('bodygraph').addEventListener('load', function() {
      const svgDoc = this.contentDocument;

      // Set colors based on theme
      const theme = '<%= theme || "classic" %>';
      // Define color palettes for different themes
      const colorPalettes = {
        classic: ['#FCD34D', '#48BB78', '#B7791F', '#B7791F', '#F56565', '#FCD34D', '#B7791F', '#F56565', '#B7791F'],
        grayscale: ['#E5E7EB', '#D1D5DB', '#9CA3AF', '#6B7280', '#4B5563', '#E5E7EB', '#6B7280', '#4B5563', '#6B7280']
      };
      // Use the appropriate color palette based on theme
      const colors = colorPalettes[theme] || colorPalettes.classic;
      ['Head', 'Ajna', 'Throat', 'Spleen', 'Ego', 'G', 'SolarPlexus', 'Sacral', 'Root'].forEach((center, index) => {
        const centerElement = svgDoc.getElementById(center);

        // Create a linear gradient element
        const gradient = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
        gradient.setAttribute('id', `${center.toLowerCase()}Gradient`);
        gradient.setAttribute('x1', '0%');
        gradient.setAttribute('y1', '0%');
        gradient.setAttribute('x2', '0%');
        gradient.setAttribute('y2', '140%');

        // Add two gradient stops
        const stop1 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
        stop1.setAttribute('offset', '0%');
        stop1.setAttribute('stop-color', colors[index]);
        gradient.appendChild(stop1);

        const stop2 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
        stop2.setAttribute('offset', '100%');
        stop2.setAttribute('stop-color', 'dark' + colors[index].substring(1));
        gradient.appendChild(stop2);

        // Append the gradient to the defs element or create defs if it doesn't exist
        let defs = svgDoc.querySelector('defs');
        if (!defs) {
          defs = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'defs');
          centerElement.appendChild(defs);
        }
        defs.appendChild(gradient);
      });

      document.getElementById('current-sun-gate-and-line').innerHTML = latestEntry.Sun.g + '.' + latestEntry.Sun.l;
      document.getElementById('next-sun-gate-and-line').innerHTML = nextGateAndLine(latestEntry.Sun.g, latestEntry.Sun.l);

      Object.keys(latestEntry).forEach(planet => {
        const gate = latestEntry[planet].g;
        const line = latestEntry[planet].l;
        const hangingGate = svgDoc.getElementById(`Gate${gate}`);
        const gateText = svgDoc.getElementById(`GateText${gate}`);
        const gateBackground = svgDoc.getElementById(`GateTextBg${gate}`);
        const gateBackgroundPath = gateBackground.querySelector('path');
        const gateBackgroundCircle = gateBackground.querySelector('circle');
        if (hangingGate && gateText && gateBackground) {
          // Set gate color based on theme
          hangingGate.style.fill = theme === 'grayscale' ? '#4B5563' : 'black';
          gateText.style.fill = '#F9F9F9';
          const bgColor = theme === 'grayscale' ? '#4B5563' : 'black';
          if (gateBackgroundPath) gateBackgroundPath.style.fill = bgColor;
          if (gateBackgroundCircle) gateBackgroundCircle.style.fill = bgColor;
        }

        const exaltingPlanet = fixings[`${gate}.${line}.exaltingPlanet`];
        const detrimentingPlanet = fixings[`${gate}.${line}.detrimentingPlanet`];
        let isExalted, isDetrimented = false;
        if (exaltingPlanet) {
          isExalted = isFixed(gate, exaltingPlanet, latestEntry);
        }
        if (detrimentingPlanet) {
          isDetrimented = isFixed(gate, detrimentingPlanet, latestEntry);
        }

        let fixing = '';
        if (isExalted && isDetrimented) {
          fixing = '<span style="font-size: 16px; left: 3px;" class="relative">⬘</span>';
        } else if (isExalted) {
          fixing = '<span style="font-size: 8px; left: 3px; top: -4px;" class="relative">▲</span>';
        } else if (isDetrimented) {
          fixing = '<span style="font-size: 8px; left: 3px;" class="relative">▼</span>';
        }
        document.getElementById(`bodygraph-label-${planet}`).innerHTML = `${gate}.${line}${fixing}`; // Fill in label, eg: 46.5
      });

      // Color in the Centers if channels are made
      const headCenter = svgDoc.getElementById('Head');
      const ajnaCenter = svgDoc.getElementById('Ajna');
      const throatCenter = svgDoc.getElementById('Throat');
      const egoCenter = svgDoc.getElementById('Ego');
      const gCenter = svgDoc.getElementById('G');
      const solarPlexusCenter = svgDoc.getElementById('SolarPlexus');
      const spleenCenter = svgDoc.getElementById('Spleen');
      const sacralCenter = svgDoc.getElementById('Sacral');
      const rootCenter = svgDoc.getElementById('Root');

      const centersMapping = [
        // [Activated Gate 1, Activated Gate 2, Center 1, Center 2]
        [3, 60, sacralCenter, rootCenter],
        [42, 53, sacralCenter, rootCenter],
        [9, 52, sacralCenter, rootCenter],
        [19, 49, rootCenter, solarPlexusCenter],
        [39, 55, rootCenter, solarPlexusCenter],
        [41, 30, rootCenter, solarPlexusCenter],
        [54, 32, spleenCenter, rootCenter],
        [28, 38, spleenCenter, rootCenter],
        [18, 58, spleenCenter, rootCenter],
        [48, 16, spleenCenter, throatCenter],
        [57, 20, spleenCenter, throatCenter],
        [57, 10, spleenCenter, gCenter],
        [34, 20, sacralCenter, throatCenter],
        [34, 10, sacralCenter, gCenter],
        [2, 14, sacralCenter, gCenter],
        [5, 15, sacralCenter, gCenter],
        [29, 46, sacralCenter, gCenter],
        [57, 34, sacralCenter, spleenCenter],
        [50, 27, sacralCenter, spleenCenter],
        [37, 40, solarPlexusCenter, egoCenter],
        [22, 12, solarPlexusCenter, throatCenter],
        [35, 36, solarPlexusCenter, throatCenter],
        [51, 25, egoCenter, gCenter],
        [21, 45, egoCenter, throatCenter],
        [7, 31, gCenter, throatCenter],
        [1, 8, gCenter, throatCenter],
        [13, 33, gCenter, throatCenter],
        [17, 62, throatCenter, ajnaCenter],
        [43, 23, throatCenter, ajnaCenter],
        [56, 11, throatCenter, ajnaCenter],
        [47, 6, ajnaCenter, headCenter],
        [24, 61, ajnaCenter, headCenter],
        [4, 63, ajnaCenter, headCenter],
      ];

      // Iterate over the mapping and set the fill colors if channels are made
      centersMapping.forEach(mapping => {
        const [gate1, gate2, center1, center2] = mapping;
        if (activatedGates[gate1] && activatedGates[gate2]) {
          center1.querySelector('path').style.fill = `url(#${center1.id.toLowerCase()}Gradient)`;
          center2.querySelector('path').style.fill = `url(#${center2.id.toLowerCase()}Gradient)`;
        }
      });

      // Fill in the GateSpan if 34-20, 34-10, 34-57, or 57-20
      if ((activatedGates[34] && activatedGates[20]) || (activatedGates[34] && activatedGates[10]) || (activatedGates[34] && activatedGates[57]) || (activatedGates[57] && activatedGates[20]) || (activatedGates[57] && activatedGates[10])) {
        const connectColor = theme === 'grayscale' ? '#4B5563' : 'black';
        svgDoc.getElementById('GateSpan').style.fill = connectColor;
        svgDoc.getElementById('GateConnect10').style.fill = connectColor;
        svgDoc.getElementById('GateConnect34').style.fill = connectColor;
      }

      if (isAminoAcidMode) {
        const getAminoAcidInfo = (planet, gate) => {
          const aminoAcidInfo = aminoAcidByGate[gate];
          const aminoAcid = aminoAcidInfo.name;
          const ringName = aminoAcidInfo.ring;
          let gates = '';
          aminoAcidInfo.gates.forEach((g, i) => {
            gates += g === gate ?
              `<span class="underline">${g}</span>` :
              g;
            if (i < aminoAcidInfo.gates.length - 1) gates += ', ';
          });
          return `<span>${planet} in ${gate}: ${aminoAcid}</span><br />
                <span class="text-xs">${ringName} (${gates})</span><br /><br />`;
        };

        const sunInfo = getAminoAcidInfo('Sun', latestEntry.Sun.g);
        const earthInfo = getAminoAcidInfo('Earth', latestEntry.Earth.g);
        const northNodeInfo = getAminoAcidInfo('North Node', latestEntry.NorthNode.g);
        const southNodeInfo = getAminoAcidInfo('South Node', latestEntry.SouthNode.g);
        const moonInfo = getAminoAcidInfo('Moon', latestEntry.Moon.g);
        const mercuryInfo = getAminoAcidInfo('Mercury', latestEntry.Mercury.g);
        const venusInfo = getAminoAcidInfo('Venus', latestEntry.Venus.g);
        const marsInfo = getAminoAcidInfo('Mars', latestEntry.Mars.g);
        const jupiterInfo = getAminoAcidInfo('Jupiter', latestEntry.Jupiter.g);
        const saturnInfo = getAminoAcidInfo('Saturn', latestEntry.Saturn.g);
        const uranusInfo = getAminoAcidInfo('Uranus', latestEntry.Uranus.g);
        const neptuneInfo = getAminoAcidInfo('Neptune', latestEntry.Neptune.g);
        const plutoInfo = getAminoAcidInfo('Pluto', latestEntry.Pluto.g);

        document.getElementById('amino-acid-info').innerHTML = `
        ${sunInfo}
        ${earthInfo}
        ${northNodeInfo}
        ${southNodeInfo}
          <div id="full-amino-acid-info" class="hidden">
            ${moonInfo}
            ${mercuryInfo}
            ${venusInfo}
            ${marsInfo}
            ${jupiterInfo}
            ${saturnInfo}
            ${uranusInfo}
            ${neptuneInfo}
            ${plutoInfo}
          </div>
            <span class="underline text-xs cursor-pointer" onclick="this.classList.add('hidden'); document.getElementById('full-amino-acid-info').classList.remove('hidden');">Show full amino acid information</span>
        `;
      } // End isAminoAcidMode

      if (showGateAndLineInfo) {
        if (!iChing) {
          document.getElementById('upload-custom-i-ching-message').classList.remove('hidden');
          return;
        }
        // Iterate over all celestial body labels
        const celestialBodies = ['Sun', 'Earth', 'NorthNode', 'SouthNode', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'Uranus', 'Neptune', 'Pluto'];
        celestialBodies.forEach(body => {
          const label = document.getElementById(`bodygraph-label-${body}`);
          const gate = latestEntry[body].g;
          const line = latestEntry[body].l;
          const gateName = iChing['gates'][`${gate}.gateName`];
          const gateOf = iChing['gates'][`${gate}.gateOf`];
          const gateShortDescription = iChing['gates'][`${gate}.gateShortDescription`];
          const gateLongDescription = iChing['gates'][`${gate}.gateLongDescription`];
          const lineName = iChing['lines'][`${gate}.${line}.lineName`];

          // Construct the HTML string for the gate and line info
          let gateAndLineInfoHtmlString = `
              Gate <span id="current-${body.toLowerCase()}-gate">${gate}</span> — <span id="current-${body.toLowerCase()}-gate-i-ching-name">${gateName}</span><br />
              <span id="current-${body.toLowerCase()}-gate-human-design-gate-of">${gateOf}</span><br />
              <span id="current-${body.toLowerCase()}-gate-short-description">${gateShortDescription}</span><br /><br />
              <span id="current-${body.toLowerCase()}-gate-long-description">${gateLongDescription}</span><br /><br />
              Line <span id="current-${body.toLowerCase()}-line">${line}</span> - <span id="current-${body.toLowerCase()}-line-name">${lineName}</span>
            `;

          // Check for additional line properties and add to the HTML string
          const blueLine = iChing['lines'][`${gate}.${line}.blueLine`];
          const exaltation = iChing['lines'][`${gate}.${line}.exaltation`];
          const detriment = iChing['lines'][`${gate}.${line}.detriment`];
          const exaltingPlanet = fixings[`${gate}.${line}.exaltingPlanet`];
          const detrimentingPlanet = fixings[`${gate}.${line}.detrimentingPlanet`];
          let isExalted, isDetrimented = false;

          if (exaltingPlanet) {
              isExalted = isFixed(gate, exaltingPlanet, latestEntry);
          }
          if (detrimentingPlanet) {
              isDetrimented = isFixed(gate, detrimentingPlanet, latestEntry);
          }

          if (blueLine) {
              gateAndLineInfoHtmlString += `<br /><br /><span id="current-${body.toLowerCase()}-line-blue-line" style="color: blue;">${blueLine}</span>`;
          }
          if (isExalted || !isDetrimented) {
              gateAndLineInfoHtmlString += `<br /><br /><span id="current-${body.toLowerCase()}-line-exaltation">Exaltation: ${exaltation}</span>`
          }
          if (isDetrimented || !isExalted) {
              gateAndLineInfoHtmlString += `<br /><br /><span id="current-${body.toLowerCase()}-line-detriment">Detriment: ${detriment}</span>`
          }

          label.addEventListener('click', function() {
            // Set the HTML content of the gate and line info
            document.getElementById('gate-and-line-info').innerHTML = gateAndLineInfoHtmlString + '<br /><br />';
          });

          if (body === 'Sun') {
            document.getElementById('gate-and-line-info').innerHTML = gateAndLineInfoHtmlString + '<br /><br />';
          }

          // Set the title attribute for the celestial body label
          label.setAttribute('title', `Gate ${gate} — ${gateName}. The ${gateOf}.\n\n ${gateShortDescription} ${gateLongDescription}\n\nLine ${line} - ${lineName}`);
        });


      }

      function getTimeOfDay(hour) {
        if (hour >= 4 && hour < 6) {
          return 'early morning';
        } else if (hour >= 6 && hour < 11) {
          return 'morning';
        } else if (hour >= 11 && hour < 14) {
          return 'midday';
        } else if (hour >= 14 && hour < 18) {
          return 'afternoon';
        } else if (hour >= 18 && hour < 22) {
          return 'evening';
        } else {
          return 'nighttime';
        }
      }

      function updateDateTime() {
        const currentDate = new Date();
        const formattedDateTime = currentDate.toLocaleString(undefined, {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          second: 'numeric',
          timeZoneName: 'short'
        });

        document.getElementById('current-date-and-time').innerHTML = `${formattedDateTime}.`;
        const iso8601DateString = '<%= @next_sun_line_entry.ts %>'.replace(' ', 'T').replace(' UTC', 'Z');
        const nextLineTimestamp = new Date(iso8601DateString).getTime() + 21600000; // Add 6 hours in ms due to timezone difference
        document.getElementById('time-until-next-sun-line').innerText = timeRemainingUntilTimestamp(nextLineTimestamp) + ' at ' + localTime(nextLineTimestamp);
      }

      function showSelectedDate() {
        document.getElementById('showing-date-wrapper').innerHTML = '<%= (@date + 7.hours).strftime('%B %d, %Y %I:%M %p') %>' + ' UTC<br />Local time: ' + new Date('<%= @date + 6.hours %>').toLocaleString();
        document.getElementById('just-now-wrapper').classList.add('hidden');
      }

      // Update the date and time immediately
      updateDateTime();

      // Update the date and time every second
      let dateTimeInterval = setInterval(updateDateTime, 1000);

      <% if params[:date] %>
        clearInterval(dateTimeInterval);
        showSelectedDate();
      <% end %>


      const temporaryMessage = document.getElementById('temporary-message');
      temporaryMessage.innerText = `It is ${getTimeOfDay(new Date().getHours())}.`;
      setTimeout(function() {
        temporaryMessage.style.transition = 'opacity 1s ease';
        temporaryMessage.style.opacity = '0';
      }, 3500);

    }); // End SVG onload()
  }); // End DOMContentLoaded
</script>
<div class="py-5 sm:px-6 relative" style="min-height: 1020px;">
  <div class="absolute <%= 'hidden' unless full_dashboard %>" style="right: 0px;" id="chart-mode-wrapper">
    <h3 class="text-xs pb-2">Chart Mode</h3>
    <div class="flex items-start gap-4 relative" style="left: -5px;">
      <div class="flex space-x-1 rounded-lg bg-slate-100 p-0.5" role="tablist" aria-orientation="horizontal">
        <button id="standard-mode" style="z-index: 100; width: 40px; overflow-y: hidden;" class="flex items-center rounded-md py-[0.4375rem] pl-2 pr-2 text-sm font-semibold lg:pr-3 bg-white shadow" role="tab" type="button" aria-selected="true">
          <%= image_tag('chart-mode-icon-standard.png', style: 'position: relative; left: 2px; width: 40px;') %>
          <span class="sr-only">Standard</span>
        </button>
        <button id="rave-mandala-mode" style="z-index: 100;" class="flex items-center rounded-md py-[0.4375rem] pl-2 pr-2" role="tab" type="button" aria-selected="false">
          <svg style="width: 24px; height: 24px;" class="flex-none stroke-slate-600" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93s3.05-7.44 7-7.93v15.86zm2-15.86c1.03.13 2 .45 2.87.93H13v-.93zM13 7h5.24c.25.31.48.65.68 1H13V7zm0 3h6.74c.08.33.15.66.19 1H13v-1zm0 9.93V19h2.87c-.87.48-1.84.8-2.87.93zM18.24 17H13v-1h5.92c-.2.35-.43.69-.68 1zm1.5-3H13v-1h6.93c-.04.34-.11.67-.19 1z"></path></svg>
          <span class="sr-only">Rave Mandala</span>
        </button>
      </div>
    </div>
  </div>
  <div id="just-now-wrapper" class="text-xs">
    <h2 id="current-date-and-time"></h2>
    <h3 id="temporary-message"></h3>
  </div>
  <div id="showing-date-wrapper" class="text-xs">
  </div>
  <div class="relative px-4 flex items-start gap-4">
    <div id="rave-mandala-mode-wrapper" class="hidden" style="position: relative; z-index: 10; left: 60px; top: -20px;">
      <%= render partial: 'signposthd/partials/rave_mandala_mode_bodygraph' %>
      <div id="goto-wrapper" class="absolute text-xs" style="right: -185px;">
        <%= form_tag dashboard_path, method: :get, class: 'flex space-x-1 items-center', id: 'dashboard-form' do %>
          <%= hidden_field_tag 'date', params[:date] %>
          <%= select_tag 'direction', options_for_select(['Next', 'Prev']), class: 'text-xs' %>
          <%= select_tag 'planet', options_for_select(['Sun', 'Earth', ['North Node', 'NorthNode'], ['South Node', 'SouthNode'], 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'Uranus', 'Neptune', 'Pluto']), class: 'text-xs', onchange: 'localStorage.setItem(this.name, this.value); updateGateOrLineInput();' %>
          <%= select_tag 'division', options_for_select(['Gate', 'Gate #', 'Line', 'Line #']), class: 'text-xs', onchange: 'localStorage.setItem(this.name, this.value); updateGateOrLineInput();' %>
          <%= text_field 'gate_or_line_number', '', class: "hidden text-xs", style: "width: 40px; height: 35px;" %>
          <%= submit_tag 'Go', class: 'bg-blue-500 text-white px-4 py-2 rounded' %>
        <% end %>
        <%= form_tag dashboard_path, method: :get, class: 'mt-1 float-right' do %>
          <%= label_tag '', 'Go To Date:' %>
          <%= datetime_local_field_tag 'date', (@date + 7.hours).strftime('%Y-%m-%dT%H:%M'),onchange: 'var form = this.form; clearTimeout(form.throttleTimeout); form.throttleTimeout = setTimeout(function() { form.submit(); }, 800);', class: 'text-xs' %>
        <% end %>
        <%= form_with(model: Reminder.new, id: 'new_reminder', class: 'contents float-right') do |form| %>
          <%= form.datetime_field :utc_time, class: "hidden", value: params[:date] ? DateTime.parse(params[:date]).strftime('%Y-%m-%dT%H:%M') : Time.now.strftime('%Y-%m-%dT%H:%M') %>
          <%= form.text_field :timezone, id: 'timezone', class: "hidden", value: '' %> <%# Populated by JavaScript %>
          <%= form.text_field :title, class: "hidden", value: params[:date] ? DateTime.parse(params[:date]).strftime('%A, %B %d, %Y %I:%M %p %Z') : Time.now.strftime('%A, %B %d, %Y %I:%M %p %Z') %>
          <%= form.text_field :description, class: "hidden" %>
          <% if user_signed_in? %>
            <%= form.text_field :user_id, class: "hidden", value: current_user.id %>
            <%= form.submit "Add Bookmark", style: "position: relative; top: 13px;", class: "italic font-medium cursor-pointer" %>
          <% end %>
        <% end %>
      </div>
      <div id="rave-mandala-info-wrapper" class="absolute text-xs" style="left: -17px;">
        <!-- TODO: Put message here -->
      </div>
    </div>
    <div id="bodygraph-wrapper" style="position: absolute; z-index: 20;">
      <object class="absolute" style="left: -20px; top: 30px; opacity: .8;" id="bodygraph-bg" type="image/svg+xml" data="<%= image_url('bodygraph-bg.svg') %>" width="1500px"></object>
      <object class="absolute" style="left: 33px; top: 25px;" id="bodygraph" type="image/svg+xml" data="<%= image_url('bodygraph-blank.svg') %>" width="430px"></object>
    </div>
    <div id="standard-mode-wrapper" style="margin-left: 520px; z-index: 25;" class="relative flex <%= 'hidden' unless full_dashboard %>">
      <div class="relative" style="cursor: pointer;">
        <object id="bodygraph-planets" style="float: left; position: relative; top: 70px; margin-top: -70px;" type="image/svg+xml" data="<%= image_url('bodygraph-personality.svg') %>" width="140px"></object>
        <span id="bodygraph-label-Sun" style="position: absolute; top: 63px; left: 60px;"></span>
        <span id="bodygraph-label-Earth" style="position: absolute; top: 100px; left: 60px;"></span>
        <span id="bodygraph-label-NorthNode" style="position: absolute; top: 137px; left: 60px;"></span>
        <span id="bodygraph-label-SouthNode" style="position: absolute; top: 174px; left: 60px;"></span>
        <span id="bodygraph-label-Moon" style="position: absolute; top: 211px; left: 60px;"></span>
        <span id="bodygraph-label-Mercury" style="position: absolute; top: 248px; left: 60px;"></span>
        <span id="bodygraph-label-Venus" style="position: absolute; top: 285px; left: 60px;"></span>
        <span id="bodygraph-label-Mars" style="position: absolute; top: 322px; left: 60px;"></span>
        <span id="bodygraph-label-Jupiter" style="position: absolute; top: 359px; left: 60px;"></span>
        <span id="bodygraph-label-Saturn" style="position: absolute; top: 396px; left: 60px;"></span>
        <span id="bodygraph-label-Uranus" style="position: absolute; top: 433px; left: 60px;"></span>
        <span id="bodygraph-label-Neptune" style="position: absolute; top: 470px; left: 60px;"></span>
        <span id="bodygraph-label-Pluto" style="position: absolute; top: 507px; left: 60px;"></span>
        <!-- Gate and Line info -->
        <div id="standard-mode-text" style="width: 400px;" class="mr-3">
          The Sun is in <span id="current-sun-gate-and-line"></span>. The Sun enters <span id="next-sun-gate-and-line"></span>
          in <span id="time-until-next-sun-line"></span>.<br />
          <br />
          <div id="gate-and-line-info">
            <h3 class="hidden" id="upload-custom-i-ching-message">Please <a class="underline" href="/dashboard-settings">upload a custom I Ching file</a> to view Gate and Line information.</h3>
          </div>
        </div>
        <!-- End standard-mode-text -->
      </div>
      <div id="amino-acid-info"></div>
    </div>
    <!-- End standard-mode-wrapper -->
  </div>
</div>
