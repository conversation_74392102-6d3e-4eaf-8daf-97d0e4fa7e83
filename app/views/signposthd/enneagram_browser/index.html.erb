<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Enneagram Browser</h1>
      <p class="mt-2 text-sm text-gray-700">Explore enneatypes, triads, and trigroups in the enneagram system.</p>
    </div>
  </div>
  <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
    <!-- Enneatypes Card -->
    <div class="overflow-hidden rounded-lg bg-white shadow">
      <div class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white">E</span>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">
              <%= link_to "Enneatypes", enneatypes_path, class: "hover:text-indigo-600" %>
            </h3>
            <p class="text-sm text-gray-500"><%= @enneatypes.count %> types</p>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-600">The nine fundamental personality types of the enneagram system.</p>
          <div class="mt-3">
            <%= link_to enneatypes_path, class: "inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" do %>
              Browse Enneatypes
              <svg class="ml-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    <!-- Enneagram Triads Card -->
    <div class="overflow-hidden rounded-lg bg-white shadow">
      <div class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white">T</span>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">
              <%= link_to "Enneagram Triads", enneagram_triads_path, class: "hover:text-green-600" %>
            </h3>
            <p class="text-sm text-gray-500"><%= @enneagram_triads.count %> triads</p>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-600">Three-type combinations that form meaningful patterns in the enneagram.</p>
          <div class="mt-3">
            <%= link_to enneagram_triads_path, class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600" do %>
              Browse Triads
              <svg class="ml-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    <!-- Enneagram Trigroups Card -->
    <div class="overflow-hidden rounded-lg bg-white shadow">
      <div class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white">G</span>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">
              <%= link_to "Enneagram Trigroups", enneagram_trigroups_path, class: "hover:text-purple-600" %>
            </h3>
            <p class="text-sm text-gray-500"><%= @enneagram_trigroups.count %> trigroups</p>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-600">Groupings of triads that share common characteristics and themes.</p>
          <div class="mt-3">
            <%= link_to enneagram_trigroups_path, class: "inline-flex items-center rounded-md bg-purple-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600" do %>
              Browse Trigroups
              <svg class="ml-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    <!-- Enneagram Stems Card -->
    <div class="overflow-hidden rounded-lg bg-white shadow">
      <div class="p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-8 w-8 rounded-full bg-orange-500 flex items-center justify-center">
              <span class="text-sm font-medium text-white">S</span>
            </div>
          </div>
          <div class="ml-4">
            <h3 class="text-lg font-medium text-gray-900">
              <%= link_to "Enneagram Stems", enneagram_stems_path, class: "hover:text-orange-600" %>
            </h3>
            <p class="text-sm text-gray-500"><%= @enneagram_stems.count %> stems</p>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-600">Two-type combinations that connect different centers of intelligence.</p>
          <div class="mt-3">
            <%= link_to enneagram_stems_path, class: "inline-flex items-center rounded-md bg-orange-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-orange-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600" do %>
              Browse Stems
              <svg class="ml-1.5 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Quick Stats Section -->
  <div class="mt-12">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Quick Overview</h2>
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-4">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 text-indigo-600">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Enneatypes</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @enneatypes.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 text-green-600">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Triads</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @enneagram_triads.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 text-purple-600">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Trigroups</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @enneagram_trigroups.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 text-orange-600">
                <svg fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Stems</dt>
                <dd class="text-lg font-medium text-gray-900"><%= @enneagram_stems.count %></dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  (function() {
    var cookieName = "access_granted";
    var correctPassword = "infodom";

    function getCookie(name) {
      var value = "; " + document.cookie;
      var parts = value.split("; " + name + "=");
      if (parts.length === 2) return parts.pop().split(";").shift();
    }

    function setCookie(name, value) {
      var expires = new Date();
      expires.setFullYear(expires.getFullYear() + 100); // Effectively "never expires"
      document.cookie = name + "=" + value + ";expires=" + expires.toUTCString() + ";path=/";
    }

    // If cookie not set, prompt for password
    if (!getCookie(cookieName)) {
      var pwd = prompt("Enter password:");
      if (pwd === correctPassword) {
        setCookie(cookieName, "true");
        alert("Access granted.");
      } else {
        window.location.href = "https://google.com";
      }
    }
  })();
</script>
