<table class="min-w-full divide-y divide-gray-200">
  <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motivation</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Determination</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Outlook</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resilience</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Environment</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
    </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200">
    <% @color_data.each do |color_key, color| %>
      <tr onclick="window.location='<%= color_path(color_key) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color_key %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color[:motivation] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color[:determination] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color[:outlook] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color[:resilience] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= color[:environment] %></td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
          <a href="<%= color_path(color_key) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>
