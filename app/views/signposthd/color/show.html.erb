<div class="max-w-4xl mx-auto py-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Color Information</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about Color <%= params[:color] %>.</p>
    </div>
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Color</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= params[:color] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Motivation</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:motivation] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Determination</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:determination] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Outlook</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:outlook] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Resilience</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:resilience] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Environment</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:environment] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">View</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @color[:view] %></dd>
        </div>
      </dl>
    </div>
  </div>
  <div class="mt-6">
    <%= link_to 'Back to All Colors', colors_path, class: 'text-indigo-600 hover:text-indigo-900' %>
  </div>
</div>
