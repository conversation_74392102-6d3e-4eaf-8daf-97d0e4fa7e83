<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900"><%= @enneagram_stem.name %></h1>
      <p class="mt-2 text-sm text-gray-700">Stem <%= @enneagram_stem.id %> details and component enneatypes.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-3">
      <%= link_to enneagram_stems_path, class: "inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
        ← Back to Stems
      <% end %>
      <%= link_to enneagram_browser_path, class: "inline-flex items-center rounded-md bg-orange-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-orange-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Stem Details -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-16 w-16 rounded-full bg-orange-500 flex items-center justify-center">
              <span class="text-sm font-bold text-white"><%= @enneagram_stem.id %></span>
            </div>
          </div>
          <div class="ml-6">
            <h3 class="text-lg font-medium text-gray-900"><%= @enneagram_stem.name %></h3>
            <p class="text-sm text-gray-500">Centers: <%= @enneagram_stem.centers %></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Component Enneatypes -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Component Enneatypes</h2>
    <p class="text-sm text-gray-600 mb-4">The enneatypes that make up this stem:</p>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
      <% @enneagram_stem.enneatype_objects.each do |enneatype| %>
        <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
          <%= link_to "", enneatype_path(enneatype), class: "absolute inset-0" %>
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center">
                <span class="text-sm font-medium text-white"><%= enneatype.id %></span>
              </div>
            </div>
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900"><%= enneatype.name %></p>
              <p class="text-xs text-gray-500">Type <%= enneatype.id %></p>
            </div>
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  <!-- Related Triads -->
  <div class="mt-8">
    <h2 class="text-lg font-medium text-gray-900 mb-6">Related Triads</h2>
    <p class="text-sm text-gray-600 mb-4">Triads that contain this stem:</p>
    <% triads = @enneagram_stem.triads %>
    <% if triads.any? %>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <% triads.each do |triad| %>
          <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
            <%= link_to "", enneagram_triad_path(triad), class: "absolute inset-0" %>
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white"><%= triad.id %></span>
                </div>
              </div>
              <div class="min-w-0 flex-1">
                <p class="text-sm font-medium text-gray-900">The <%= triad.id %> Triad</p>
                <p class="text-xs text-gray-500"><%= triad.name %></p>
                <p class="text-xs text-gray-500">
                  Types: <%= triad.enneatypes.join(', ') %>
                </p>
              </div>
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
          <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No related triads</h3>
        <p class="mt-1 text-sm text-gray-500">This stem is not part of any triads.</p>
      </div>
    <% end %>
  </div>
  <!-- Stem Composition -->
  <div class="mt-8">
    <div class="bg-gray-50 px-4 py-5 sm:p-6 rounded-lg">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Stem Composition</h3>
      <div class="space-y-3">
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">This stem consists of types:</span>
          <div class="flex space-x-2">
            <% @enneagram_stem.enneatypes.each do |type_id| %>
              <span class="inline-flex items-center rounded-full bg-indigo-100 px-3 py-0.5 text-sm font-medium text-indigo-800">
                <%= type_id %>
              </span>
            <% end %>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-600">Centers connected:</span>
          <span class="inline-flex items-center rounded-full bg-orange-100 px-3 py-0.5 text-sm font-medium text-orange-800">
            <%= @enneagram_stem.centers %>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
