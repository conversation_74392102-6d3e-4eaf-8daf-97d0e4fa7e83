<div class="px-4 sm:px-6 lg:px-8 py-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Enneagram Stems</h1>
      <p class="mt-2 text-sm text-gray-700">Two-type combinations that connect different centers of intelligence.</p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to enneagram_browser_path, class: "block rounded-md bg-orange-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-orange-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600" do %>
        ← Back to Browser
      <% end %>
    </div>
  </div>
  <!-- Group by Centers -->
  <% centers_groups = @enneagram_stems.group_by(&:centers) %>
  <% centers_groups.each do |centers, stems| %>
    <div class="mt-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4"><%= centers %></h2>
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Stem ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">Enneatypes</th>
              <th scope="col" class="relative px-6 py-3"><span class="sr-only">View</span></th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% stems.each do |stem| %>
              <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location='<%= enneagram_stem_path(stem) %>'">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= stem.id %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <%= stem.name %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <% stem.enneatypes.each_with_index do |type_id, index| %>
                    <%= link_to type_id, enneatype_path(type_id), class: "text-indigo-600 hover:text-indigo-900", onclick: "event.stopPropagation()" %><%= ", " unless index == stem.enneatypes.length - 1 %>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <%= link_to "View", enneagram_stem_path(stem), class: "text-orange-600 hover:text-orange-900", onclick: "event.stopPropagation()" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  <% end %>
  <!-- Grid view for mobile -->
  <div class="mt-8 grid grid-cols-1 gap-4 sm:hidden">
    <% @enneagram_stems.each do |stem| %>
      <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm hover:border-gray-400">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center">
              <span class="text-xs font-medium text-white"><%= stem.id %></span>
            </div>
          </div>
          <div class="min-w-0 flex-1">
            <span class="absolute inset-0" aria-hidden="true"></span>
            <p class="text-sm font-medium text-gray-900"><%= stem.name %></p>
            <p class="truncate text-sm text-gray-500">
              <%= stem.centers %> | Types: <%= stem.enneatypes.join(', ') %>
            </p>
          </div>
          <div class="flex-shrink-0">
            <%= link_to enneagram_stem_path(stem), class: "inline-flex items-center rounded-md bg-white px-2.5 py-1.5 text-xs font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" do %>
              View
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
