<!-- app/views/reminders/index.html.erb -->
<div class="container m-5" style="width: 400px;">
  <% if @reminders.any? %>
    <h1 class="text-2xl font-bold mb-4">Reminders</h1>
    <ul class="mb-4">
      <% @reminders.each do |reminder| %>
        <li id="reminder_<%= reminder.id %>" class="border-b border-gray-200 py-4 px-2 transition-colors duration-300 hover:bg-gray-100 relative cursor-pointer"
            onclick="window.location.href='/dashboard?date=<%= reminder.utc_time.iso8601 %>'">
          <div><%= reminder.utc_time.in_time_zone('MST').strftime('%Y-%m-%d %H:%M:%S %Z') %></div>
          <div class="text-xs text-gray-500"><%= reminder.utc_time.utc.strftime('%Y-%m-%d %H:%M:%S UTC') %></div>
          <button class="absolute top-0 right-0 m-2 text-red-600 hover:text-red-800" style="display: none;" onclick="deleteReminder(event, <%= reminder.id %>)">X</button>
        </li>
      <% end %>
    </ul>
    <a href="#" id="toggleDelete" class="ml-2 text-blue-500">Unlock</a>
    <script>
  function deleteReminder(event, reminderId) {
    event.stopPropagation(); // Prevent the event from propagating
    event.preventDefault();
    fetch('/reminders/' + reminderId, {
      method: 'DELETE',
      headers: {
        'X-CSRF-Token': Rails.csrfToken(),
        'Accept': 'application/json'
      }
    })
    .then(response => {
      if (response.ok) {
        // Remove the reminder from the UI
        document.getElementById('reminder_' + reminderId).remove();
      } else {
        console.error('Failed to delete reminder');
      }
    })
    .catch(error => console.error('Error:', error));
  }

  document.addEventListener("DOMContentLoaded", function() {
    var deleteEnabled = false;
    var toggleDeleteLink = document.getElementById('toggleDelete');

    function toggleDelete() {
      deleteEnabled = !deleteEnabled;
      var reminders = document.querySelectorAll('.border-b');
      toggleDeleteLink.textContent = deleteEnabled ? 'Lock' : 'Unlock';
    }

    toggleDeleteLink.addEventListener('click', function(event) {
      event.preventDefault();
      toggleDelete();
    });

    var remindersList = document.querySelectorAll('.border-b');
    remindersList.forEach(function(reminder) {
      reminder.addEventListener('mouseenter', function() {
        if (deleteEnabled) {
          var deleteButton = reminder.querySelector('button');
          deleteButton.style.display = 'block';
        }
      });
      reminder.addEventListener('mouseleave', function() {
        var deleteButton = reminder.querySelector('button');
        deleteButton.style.display = 'none';
      });
    });
  });
</script>
  <% else %>
    You currently have no reminders.
  <% end %>
</div>
