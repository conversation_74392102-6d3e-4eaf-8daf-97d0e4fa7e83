<%= form_tag({ action: :index }, method: :get, id: 'sequence-analyzer-form') do %>
  <div class="m-4 flex items-center space-x-6">
    <div class="flex items-center">
      <label for="bodygraph" class="mr-2 whitespace-nowrap">Bodygraph:</label>
      <%= select_tag 'bodygraph', options_from_collection_for_select(@bodygraphs, 'id', 'name', @bodygraph.id), { prompt: "", id: 'bodygraph-select', class: "border border-gray-300 rounded-md px-3 py-1 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
    </div>
    <div class="flex items-center">
      <label for="activator" class="mr-2 whitespace-nowrap">Activator:</label>
      <%= select_tag 'activator', options_from_collection_for_select(@activators, 'id', 'name', @activator.id), { prompt: "", id: 'activator-select', class: "border border-gray-300 rounded-md px-3 py-1 mr-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
      <% unless @activator.name.nil? %>
        <span class="ml-2 text-sm text-gray-600"><%= "Current #{@activator.name} Gate: #{@current_activator_gate}" %></span>
      <% end %>
    </div>
    <div class="flex items-center">
      <label for="mode" class="mr-2 whitespace-nowrap">Mode:</label>
      <%= select_tag 'mode', options_for_select([['Standard', 'standard'], ['Polarity Hotspots', 'polarities']], params[:mode] || 'standard'), { id: 'mode-select', class: "border border-gray-300 rounded-md px-3 py-1 w-48 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
    </div>
  </div>
  <% if params[:mode] == 'polarities' %>
    <div class="ml-4 text-sm">
      <div class="text-purple-600">Dark purple: polarity hotspots—opposite gates that both connect to hanging or dormant gates in the bodygraph.</div>
      <div class="text-purple-300">Light purple: gates that connect to hanging or dormant gates in the bodygraph.</div>
    </div>
  <% elsif params[:mode].nil? || params[:mode] == 'standard' %>
    <div class="ml-4 text-sm">
      <span class="text-black">Personality activations are in black.</span>
      <span class="text-red-600 ml-2">Design activations are in red.</span>
    </div>
  <% end %>
<% end %>
<script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function() {
    var form = document.getElementById('sequence-analyzer-form');
    var bodygraphSelect = document.getElementById('bodygraph-select');
    var activatorSelect = document.getElementById('activator-select');
    var modeSelect = document.getElementById('mode-select');

    bodygraphSelect.addEventListener('change', updateURL);
    activatorSelect.addEventListener('change', updateURL);
    modeSelect.addEventListener('change', updateURL);

    function updateURL() {
      var bodygraphValue = bodygraphSelect.value;
      var activatorValue = activatorSelect.value;
      var modeValue = modeSelect.value;

      var url = '<%= url_for(action: :index) %>';
      var params = [];

      if (bodygraphValue !== '') {
        params.push('bodygraph=' + bodygraphValue);
      }

      if (activatorValue !== '') {
        params.push('activator=' + activatorValue);
      }

      if (modeValue !== 'standard') {
        params.push('mode=' + modeValue);
      }

      if (params.length > 0) {
        url += '?' + params.join('&');
      }

      window.location.href = url;
    }
  });
</script>
<div class="grid grid-cols-8 gap-4 m-4 pb-5">
  <%
    all_activated_gates = JSON.parse(@bodygraph.all_activated_gates)

    # Extract gates from personality and design activations
    personality_gates = []
    design_gates = []

    # Parse personality activations - try multiple approaches
    begin
      personality_data = JSON.parse(@bodygraph.personality_activations)
      Rails.logger.debug "Personality data: #{personality_data.inspect}"

      # Approach 1: Check if it's a hash with keys like 'sun_gate', 'earth_gate', etc.
      if personality_data.is_a?(Hash) && personality_data.key?('sun_gate')
        # Extract gates from keys that end with '_gate'
        personality_data.each do |key, value|
          if key.to_s.end_with?('_gate') && value.present?
            personality_gates << value.to_s
            Rails.logger.debug "Added personality gate (approach 1): #{value}"
          end
        end
      # Approach 2: Check if it's a hash with planet keys and gate values in nested hashes
      elsif personality_data.is_a?(Hash)
        personality_data.each do |planet, data|
          if data.is_a?(Hash) && data['g'].present?
            personality_gates << data['g'].to_s
            Rails.logger.debug "Added personality gate (approach 2): #{data['g']}"
          end
        end
      end

      # If we still don't have any gates, try a more direct approach
      if personality_gates.empty?
        # Try to extract gates directly from the JSON string
        gate_matches = @bodygraph.personality_activations.scan(/"g":(\d+)/).flatten
        gate_matches.each do |gate|
          personality_gates << gate
          Rails.logger.debug "Added personality gate (direct approach): #{gate}"
        end
      end
    rescue JSON::ParserError => e
      # Handle parsing error
      Rails.logger.error("Error parsing personality activations: #{e.message}")

      # Try to extract gates directly from the string if JSON parsing failed
      gate_matches = @bodygraph.personality_activations.scan(/"g":(\d+)/).flatten
      gate_matches.each do |gate|
        personality_gates << gate
        Rails.logger.debug "Added personality gate (fallback): #{gate}"
      end
    end

    # Parse design activations - try multiple approaches
    begin
      design_data = JSON.parse(@bodygraph.design_activations)
      Rails.logger.debug "Design data: #{design_data.inspect}"

      # Approach 1: Check if it's a hash with keys like 'sun_gate', 'earth_gate', etc.
      if design_data.is_a?(Hash) && design_data.key?('sun_gate')
        # Extract gates from keys that end with '_gate'
        design_data.each do |key, value|
          if key.to_s.end_with?('_gate') && value.present?
            design_gates << value.to_s
            Rails.logger.debug "Added design gate (approach 1): #{value}"
          end
        end
      # Approach 2: Check if it's a hash with planet keys and gate values in nested hashes
      elsif design_data.is_a?(Hash)
        design_data.each do |planet, data|
          if data.is_a?(Hash) && data['g'].present?
            design_gates << data['g'].to_s
            Rails.logger.debug "Added design gate (approach 2): #{data['g']}"
          end
        end
      end

      # If we still don't have any gates, try a more direct approach
      if design_gates.empty?
        # Try to extract gates directly from the JSON string
        gate_matches = @bodygraph.design_activations.scan(/"g":(\d+)/).flatten
        gate_matches.each do |gate|
          design_gates << gate
          Rails.logger.debug "Added design gate (direct approach): #{gate}"
        end
      end
    rescue JSON::ParserError => e
      # Handle parsing error
      Rails.logger.error("Error parsing design activations: #{e.message}")

      # Try to extract gates directly from the string if JSON parsing failed
      gate_matches = @bodygraph.design_activations.scan(/"g":(\d+)/).flatten
      gate_matches.each do |gate|
        design_gates << gate
        Rails.logger.debug "Added design gate (fallback): #{gate}"
      end
    end

    # Debug output
    Rails.logger.debug "Final personality gates: #{personality_gates.inspect}"
    Rails.logger.debug "Final design gates: #{design_gates.inspect}"

    mode = params[:mode] || 'standard'

    @gates.each_with_index do |gate, index|
      harmonic_gate = @harmonic_gates[index]
      activated = all_activated_gates.include?(gate)
      harmonic_activated = !activated && all_activated_gates.include?(harmonic_gate)
      polarity_hotspot = harmonic_activated && all_activated_gates.include?(harmonic_gate(opposite_gate(gate)))

      # Check if gate is activated in personality or design
      # Convert gate to string for comparison since personality_gates and design_gates contain strings
      gate_str = gate.to_s
      personality_activated = personality_gates.include?(gate_str)
      design_activated = design_gates.include?(gate_str)
      both_activated = personality_activated && design_activated

      # Debug output for each gate
      Rails.logger.debug "Gate #{gate} (#{gate_str}): personality_activated=#{personality_activated}, design_activated=#{design_activated}, both_activated=#{both_activated}"

      gate_border, gate_text, gate_style = if gate == @current_activator_gate
        ['border-green-600', 'text-green-600', '']
      elsif mode == 'standard' || mode.nil?
        if personality_activated && design_activated
          ['border-black border-red-600', 'text-black', 'border-dashed']
        elsif personality_activated
          ['border-black', 'text-black', '']
        elsif design_activated
          ['border-red-600', 'text-red-600', '']
        else
          ['border-gray-300', 'text-gray-300', '']
        end
      elsif mode == 'polarities'
        if polarity_hotspot
          ['border-purple-600', 'text-purple-600', '']
        elsif harmonic_activated
          ['border-purple-300', 'text-purple-300', '']
        else
          ['border-gray-300', 'text-gray-300', '']
        end
      else
        ['border-gray-300', 'text-gray-300', '']
      end
  %>
  <div class="gate relative p-4 border <%= gate_border %> <%= gate_style %> rounded-md shadow-md text-center">
    <% if @planets_by_gate[gate] %>
      <div class="gate-bg absolute" style="font-size: 100px; z-index: 9; color: #efefef; text-align: center; left: 50%; top: 50%; transform: translate(-50%, -50%);"><%= @planet_glyphs[@planets_by_gate[gate][0].to_sym] %></div>
    <% end %>
    <div class="gate-info relative" style="z-index: 10;">
      <div class="text-2xl font-bold <%= gate_text %> mb-2">
        Gate <%= gate %>
      </div>
      <% if activated %>
        <% @activations_by_gate[gate].each do |activation| %>
          <span class="text-gray-500 text-xs"><%= activation.gsub("_gate", "").split("_").map(&:capitalize).join(" ") %></span><br>
        <% end %>
      <% else %>
        <div class="text-xs <%= gate_text %>">
          <%= harmonic_activated ? ('Activations: <br />' + [gate, harmonic_gate].join('-')).html_safe : '' %><br />
          <br />
          <% activated_centers = [] %>
          <% unless @defined[@centers_by_gate[gate.to_s]] %>
            <% activated_centers << @centers_by_gate[gate.to_s] %>
          <% end %>
          <% unless @defined[@centers_by_gate[harmonic_gate.to_s]] %>
            <% activated_centers << @centers_by_gate[harmonic_gate.to_s] %>
          <% end %>
          <%= activated_centers.join(', ') if harmonic_activated %>
        </div>
      <% end %>
      <% if @planets_by_gate[gate] %>
        <div class="activators text-2xl <%= gate_text %>">
          <% @planets_by_gate[gate].each_with_index do |planet, index| %>
            <% next if index.zero? %>
            <%= @planet_glyphs[planet.to_sym] %>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>
</div>
