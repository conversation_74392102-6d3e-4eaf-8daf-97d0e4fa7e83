<div class="bg-white">
  <div class="mx-auto max-w-7xl py-24 px-6 sm:py-32 lg:grid lg:grid-cols-3 lg:gap-x-12 lg:px-8 lg:py-40">
    <div>
      <h2 class="text-lg font-semibold leading-8 tracking-tight text-indigo-600">Everything you need</h2>
      <p class="mt-2 text-4xl font-bold tracking-tight text-gray-900">All-in-one platform</p>
      <p class="mt-6 text-base leading-7 text-gray-600">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Maiores impedit perferendis suscipit eaque, iste dolor cupiditate blanditiis ratione.</p>
    </div>
    <div class="mt-20 lg:col-span-2 lg:mt-0">
      <dl class="grid grid-cols-1 gap-12 sm:grid-flow-col sm:grid-cols-2 sm:grid-rows-4">
        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Invite team members</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">You can manage phone, email and chat conversations all from a single mailbox.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">List view</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">You can manage phone, email and chat conversations all from a single mailbox.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Keyboard shortcuts</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">You can manage phone, email and chat conversations all from a single mailbox.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Calendars</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">You can manage phone, email and chat conversations all from a single mailbox.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Notifications</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Find what you need with advanced filters, bulk actions, and quick views.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Boards</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Find what you need with advanced filters, bulk actions, and quick views.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Reporting</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Find what you need with advanced filters, bulk actions, and quick views.</dd>
        </div>

        <div class="relative">
          <dt>
            <!-- Heroicon name: outline/check -->
            <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
            </svg>
            <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Mobile app</p>
          </dt>
          <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Find what you need with advanced filters, bulk actions, and quick views.</dd>
        </div>
      </dl>
    </div>
  </div>
</div>

<div class="bg-white py-20 sm:py-24 lg:py-32">
  <div class="mx-auto max-w-xl px-6 lg:max-w-7xl lg:px-8">
    <h2 class="sr-only">A better way to send money.</h2>
    <dl class="grid grid-cols-1 gap-16 lg:grid lg:grid-cols-3">
      <div>
        <dt>
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-500 text-white">
            <!-- Heroicon name: outline/globe-alt -->
            <svg class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
            </svg>
          </div>
          <p class="mt-6 text-lg font-semibold leading-8 tracking-tight text-gray-900">Competitive exchange rates</p>
        </dt>
        <dd class="mt-2 text-base leading-7 text-gray-600">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Maiores impedit perferendis suscipit eaque, iste dolor cupiditate blanditiis ratione.</dd>
      </div>

      <div>
        <dt>
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-500 text-white">
            <!-- Heroicon name: outline/scale -->
            <svg class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0012 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52l2.62 10.726c.122.499-.106 1.028-.589 1.202a5.988 5.988 0 01-2.031.352 5.988 5.988 0 01-2.031-.352c-.483-.174-.711-.703-.59-1.202L18.75 4.971zm-16.5.52c.99-.203 1.99-.377 3-.52m0 0l2.62 10.726c.122.499-.106 1.028-.589 1.202a5.989 5.989 0 01-2.031.352 5.989 5.989 0 01-2.031-.352c-.483-.174-.711-.703-.59-1.202L5.25 4.971z" />
            </svg>
          </div>
          <p class="mt-6 text-lg font-semibold leading-8 tracking-tight text-gray-900">No hidden fees</p>
        </dt>
        <dd class="mt-2 text-base leading-7 text-gray-600">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Maiores impedit perferendis suscipit eaque, iste dolor cupiditate blanditiis ratione.</dd>
      </div>

      <div>
        <dt>
          <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-500 text-white">
            <!-- Heroicon name: outline/bolt -->
            <svg class="h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
            </svg>
          </div>
          <p class="mt-6 text-lg font-semibold leading-8 tracking-tight text-gray-900">Transfers are instant</p>
        </dt>
        <dd class="mt-2 text-base leading-7 text-gray-600">Lorem ipsum, dolor sit amet consectetur adipisicing elit. Maiores impedit perferendis suscipit eaque, iste dolor cupiditate blanditiis ratione.</dd>
      </div>
    </dl>
  </div>
</div>

<div class="bg-white">
  <div class="mx-auto max-w-7xl py-24 px-6 sm:py-32 lg:px-8 lg:py-40">
    <div class="mx-auto max-w-3xl text-center">
      <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">All-in-one platform</h2>
      <p class="mx-auto mt-4 max-w-xl text-lg leading-8 text-gray-600">Quia est qui aut velit exercitationem repudiandae voluptatem facilis. Neque est debitis dolor facilis ab amet.</p>
    </div>
    <dl class="mt-20 grid grid-cols-1 gap-12 sm:grid-cols-2 sm:gap-x-6 lg:grid-cols-4 lg:gap-x-8">
      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Invite team members</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Quam a velit animi fuga ad. Accusamus consectetur nulla perferendis quam. Aperiam error iusto id eos.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Notifications</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Ut excepturi sequi et corrupti. Quidem est non ipsam sunt voluptatem. Velit dicta iusto. Molestiae.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">List view</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Quis ratione necessitatibus ullam id animi iure accusamus debitis voluptas. Cumque debitis exercitationem.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Boards</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Quae et accusantium quo molestiae sed sit ut quo. Quidem omnis iure et maiores porro. Eligendi deserunt.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Keyboard shortcuts</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Optio assumenda eos neque. Quaerat temporibus dicta provident. Quia unde quo aut aut molestiae sit..</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Reporting</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Excepturi sed quo mollitia voluptatibus. Qui quo ut nihil quo. Dolor at dignissimos ea voluptatem.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Calendars</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Illum nesciunt odio. Dolorem nobis labore eveniet consequatur quas aut delectus molestias. Qui recusandae.</dd>
      </div>

      <div class="relative">
        <dt>
          <!-- Heroicon name: outline/check -->
          <svg class="absolute mt-1 h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
          </svg>
          <p class="ml-10 text-lg font-semibold leading-8 text-gray-900">Mobile app</p>
        </dt>
        <dd class="mt-2 ml-10 text-base leading-7 text-gray-600">Aut velit est eius dolore repudiandae. Vitae temporibus amet possimus mollitia. Quia molestiae rerum.</dd>
      </div>
    </dl>
  </div>
</div>
