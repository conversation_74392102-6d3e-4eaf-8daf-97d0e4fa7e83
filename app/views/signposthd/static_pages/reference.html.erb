<ul role="list" class="p-5 grid grid-cols-2 gap-x-4 gap-y-8 sm:grid-cols-3 sm:gap-x-6 lg:grid-cols-5 xl:gap-x-8">
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-rave-mandala.png", alt: "Rave Mandala Explorer", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/rave-mandala-explorer" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Rave Mandala Explorer</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Rave <PERSON>dala Explorer</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">Use an interactive Rave Mandala to learn the chops.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-quarters-and-godheads.png", alt: "Quarters and Godheads", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/quarters-and-godheads" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Quarters and Godheads</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Quarters and Godheads</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">The quarters of the wheel and the 16 Faces of the Godhead.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-centers.png", alt: "Centers", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/centers" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Centers</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Centers</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">The 9 centers of the bodygraph.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-circuits.png", alt: "Circuits", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/circuits" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Circuits</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Circuits</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">Explore the circuits and circuit groups.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-channels.png", alt: "Channels", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/channels" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Channels</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Channels</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">Explore the channels within the bodygraph.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-gates.png", alt: "Gates", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/gates" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Gates</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Gates</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">Discover the gates of the mandala.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-lines.png", alt: "Lines", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/lines" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Lines</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Lines</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">The 6 lines of the hexagram.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-color.png", alt: "Color", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/color" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Color</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Color</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">The exit frequency from the crystal.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-tone.png", alt: "Tone", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/tone" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Tone</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Tone</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">The frequency within the crystal.</p>
  </li>
  <li class="relative">
    <div class="group aspect-h-12 aspect-w-9 block w-full overflow-hidden rounded-lg bg-gray-100 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
      <%= image_tag("reference-dashboard-base.png", alt: "Base", class: "pointer-events-none object-cover group-hover:opacity-75") %>
      <%= link_to "/base" do %>
        <button type="button" class="absolute inset-0 focus:outline-none">
          <span class="sr-only">Base</span>
        </button>
      <% end %>
    </div>
    <p class="pointer-events-none mt-2 block truncate text-sm font-medium text-gray-900">Base</p>
    <p class="pointer-events-none block text-sm font-medium text-gray-500">Base Theory and the 5 dimensions of the maia.</p>
  </li>
</ul>
