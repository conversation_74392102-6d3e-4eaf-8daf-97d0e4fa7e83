<div class="max-w-lg mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
        <h2 class="text-2xl font-bold mb-4">Countdown until February 15, 2027 12:00:00 UTC</h2>
        <div id="countdown" class="text-xl font-bold"></div>
    </div>

    <script>
        // Set the target date for the countdown
        const targetDate = new Date("February 15, 2027 12:00:00 UTC").getTime();

        // Update the countdown every second
        const countdown = setInterval(function() {
            // Get the current date and time
            const now = new Date().getTime();

            // Calculate the difference between the target date and the current date
            const difference = targetDate - now;

            // Calculate the time units remaining
            const years = Math.floor(difference / (1000 * 60 * 60 * 24 * 365));
            const remainingDays = Math.floor(difference % (1000 * 60 * 60 * 24 * 365));
            const months = Math.floor(remainingDays / (1000 * 60 * 60 * 24 * (365 / 12)));
            const remainingDaysInMonths = Math.floor(remainingDays % (1000 * 60 * 60 * 24 * (365 / 12)));
            const days = Math.floor(remainingDaysInMonths / (1000 * 60 * 60 * 24));
            const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            //  Construct the human-readable countdown string
            let countdownString = "";

            if (years > 0) {
                countdownString += `${years} year${years > 1 ? 's' : ''}, `;
            }
            if (months > 0) {
                countdownString += `${months} month${months > 1 ? 's' : ''}, `;
            }
            if (days > 0) {
                countdownString += `${days} day${days > 1 ? 's' : ''}, `;
            }
            if (hours > 0) {
                countdownString += `${hours} hour${hours > 1 ? 's' : ''}, `;
            }
            if (minutes > 0) {
                countdownString += `${minutes} minute${minutes > 1 ? 's' : ''}, `;
            }
            if (seconds > 0 || countdownString === "") {
                countdownString += `${seconds} second${seconds > 1 ? 's' : ''}`;
            }

            // Update the countdown timer display
            document.getElementById("countdown").textContent = countdownString;


            // Update the countdown timer display
            document.getElementById("countdown").textContent = countdownString;

            // If the countdown is finished, clear the interval
            if (difference < 0) {
                clearInterval(countdown);
                document.getElementById("countdown").textContent = "Countdown expired!";
            }
        }, 1000); // Update every second
    </script>