<div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
  <% case [Date.current.month, Date.current.day]
      when [2, 18]
        message = "Today is the anniversary of the discovery of Pluto!"
      when [3, 13]
        message = "Today is the anniversary of Sir <PERSON>'s discovery of Uranus!"
      when [4, 9]
        message = "Today is <PERSON>'s birthday!"
      when [9, 23]
        message = "Today is the anniversary of the discovery of <PERSON>!"
      when [9, 25]
        message = "Today is <PERSON>'s birthday!"
      when [9, 26]
        message = "Today is <PERSON>'s birthday!"
      when [12, 4]
        message = "Today is <PERSON><PERSON><PERSON><PERSON>'s birthday!"
      else
        message = nil
    end %>
  <% if message %>
    <div class="px-4 py-5 sm:px-6" id="messageContainer">
      <p id="message" style="opacity: 1; transition: opacity 1s ease-in-out;"><%= message %></p>
    </div>
    <script>
      // JavaScript code to fade out the message after 3 seconds
      document.addEventListener('DOMContentLoaded', function() {
        var messageElement = document.getElementById('message');
        var messageContainer = document.getElementById('messageContainer');

        // Start fading out after 3 seconds
        setTimeout(function() {
          // Fade out the message
          messageElement.style.opacity = 0;

          // Remove the div after the fade-out animation is finished
          setTimeout(function() {
            messageContainer.parentNode.removeChild(messageContainer);
          }, 1000); // 1 second (matching the transition duration)
        }, 2000);
      });
    </script>
  <% end %>
  <div>
    <%= render partial: 'signposthd/partials/dashboard_bodygraph', locals: { full_dashboard: true, theme: 'classic' } %>
  </div>
</div>
