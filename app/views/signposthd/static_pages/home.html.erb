<div class="bg-gray-100 pt-10 sm:pt-16 lg:overflow-hidden lg:pt-8 lg:pb-14">
  <div class="mx-auto max-w-7xl lg:px-8">
    <div class="lg:grid lg:grid-cols-2 lg:gap-8">
      <div class="mx-auto max-w-md px-4 sm:max-w-2xl sm:px-6 sm:text-center lg:flex lg:px-0 lg:text-left">
        <div class="">
          <h1 class="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:mt-3 sm:text-6xl lg:mt-4 xl:text-6xl">
            <span class="block">Your Human Design</span>
            <span class="block text-gray-700">Toolbox</span>
          </h1>
          <p class="mt-3 text-base text-gray-600 sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">The all-in-one resource for Human Design professionals. Completely free. Use state-of-the-art tools for Human Design analysis. Sponsored by Emerge by Design, High Desert Human Design, The Center for Human Design, and The Open Design Consortium.</p>
          <div class="mt-10 sm:mt-12">
            <a href="<%= new_user_registration_path %>" class="button rounded-md bg-gray-800 py-3 px-4 text-white shadow hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-100">Sign up</a>
          </div>
        </div>
      </div>
      <div class="mt-12 -mb-16 sm:-mb-48 lg:relative lg:m-0">
        <div id="dashboard-bodygraph-wrapper" class="mx-auto max-w-md px-4 sm:max-w-2xl sm:px-6 lg:max-w-none lg:px-0" style="position: absolute; left: -9999px;">
          <%= render partial: 'signposthd/partials/dashboard_bodygraph', locals: { full_dashboard: false, theme: 'grayscale' } %>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function() {

    // Select the bodygraph-bg object and bodygraph object
    var bodygraphBgObject = document.getElementById("bodygraph-bg");
    var bodygraphObject = document.getElementById("bodygraph");

    // Function to handle SVG fading
    function fadeInSvgElements(svgObject) {
      svgObject.onload = function() {
        var bodygraphSVG = svgObject.contentDocument;

        // Get all the elements inside the bodygraph SVG
        var svgElements = bodygraphSVG.querySelectorAll('*');

        // Initially hide all elements by setting their opacity to 0
        svgElements.forEach(function(element) {
          element.style.opacity = 0;
        });

        document.getElementById("dashboard-bodygraph-wrapper").style.position = '';

        // Function to show each element one by one over 20 seconds
        var delay = 0;
        svgElements.forEach(function(element) {
          setTimeout(function() {
            element.style.transition = 'opacity 1s';
            element.style.opacity = 1; // Fade in each element
          }, (delay += 20)); // 20ms delay between each element
        });
      };
    }

    // Call the fadeInSvgElements function for both SVG objects
    fadeInSvgElements(bodygraphBgObject);
    fadeInSvgElements(bodygraphObject);

  });
</script>