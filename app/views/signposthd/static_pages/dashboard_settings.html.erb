<script>
  const modes = {
   astrologer: 'isAstrologerMode',
   aminoAcid: 'isAminoAcidMode',
   dashboardGateAndLineInfo: 'dashboardGateAndLineInfo',
   dashboardRaveMandalaShowGodheads: 'dashboardRaveMandalaShowGodheads',
   dashboardRaveMandalaShowZodiac: 'dashboardRaveMandalaShowZodiac',
   dashboardRaveMandalaUseZodiacSymbols: 'dashboardRaveMandalaUseZodiacSymbols'
   // Add more modes here as needed
   };

   const linkTextEnabled = {
   'isAstrologerMode': 'Astrologer Mode (On)',
   'isAminoAcidMode': 'Show amino acids (On)',
   'dashboardGateAndLineInfo': 'Show Gate and Line information (On)',
   dashboardRaveMandalaShowGodheads: 'Rave <PERSON>a - Show Godheads (On)',
   dashboardRaveMandalaShowZodiac: 'Rave Mandala - Show Zodiac (On)',
   dashboardRaveMandalaUseZodiacSymbols: 'Rave <PERSON> - Use Symbols (Off)'
   };

   const linkTextDisabled = {
   'isAstrologerMode': 'Astrologer Mode (Off)',
   'isAminoAcidMode': 'Show amino acids (Off)',
   'dashboardGateAndLineInfo': 'Show Gate and Line information (Off)',
   dashboardRaveMandalaShowGodheads: 'Rave Mandala - Show Godheads (Off)',
   dashboardRaveMandalaShowZodiac: 'Rave Mandala - Show Zodiac (Off)',
   dashboardRaveMandalaUseZodiacSymbols: 'Rave Mandala - Use Symbols (Off)'
   };

   function toggleMode(mode) {
   const isMode = localStorage.getItem(mode) === 'true';
   const newMode = !isMode;
   localStorage.setItem(mode, newMode.toString());
   updateLinkText(mode);
   updateUploadCustomIChing();
   }

   function updateLinkText(mode) {
   const isMode = localStorage.getItem(mode) === 'true';
   const link = document.getElementById(`${mode}-toggle-link`);
   const toggleButton = document.getElementById(`${mode}-mode-toggle-button`);
   const toggleSpan = document.getElementById(`${mode}-mode-toggle-span`);
   if (isMode) {
     link.textContent = linkTextEnabled[mode];
     toggleButton.classList.remove('bg-gray-200');
     toggleButton.classList.add('bg-indigo-600');
     toggleButton.setAttribute('aria-checked', 'true');
     toggleSpan.classList.remove('translate-x-0');
     toggleSpan.classList.add('translate-x-5');
   } else {
     link.textContent = linkTextDisabled[mode];
     toggleButton.classList.remove('bg-indigo-600');
     toggleButton.classList.add('bg-gray-200');
     toggleButton.setAttribute('aria-checked', 'false');
     toggleSpan.classList.remove('translate-x-5');
     toggleSpan.classList.add('translate-x-0');
   }
   }


   function saveCustomIChing(content) {
     localStorage.setItem('customIChing', content);
   }

   function updateUploadCustomIChing() {
   if (localStorage.getItem('dashboardGateAndLineInfo') === 'true') {
     const iChing = localStorage.getItem('customIChing');
     document.getElementById('upload-custom-i-ching').classList.remove('hidden');
     if (iChing) {
       document.getElementById('custom-i-ching-already-uploaded').classList.remove('hidden');
       document.getElementById('upload-custom-i-ching').classList.add('hidden');
     }
   } else {
     document.getElementById('upload-custom-i-ching').classList.add('hidden');
   }
   }

   window.addEventListener('load', () => {
   Object.keys(modes).forEach(mode => updateLinkText(modes[mode]));
   updateUploadCustomIChing();
   document.getElementById('replace-i-ching-link').addEventListener('click', () => {
     document.getElementById('custom-i-ching-already-uploaded').classList.add('hidden');
     document.getElementById('upload-custom-i-ching').classList.remove('hidden');
   });
   document.getElementById('enter-a-url-link').addEventListener('click', () => {
     document.getElementById('enter-a-url-link').classList.add('hidden');
     document.getElementById('upload-file').classList.add('hidden');
     document.getElementById('enter-url').classList.remove('hidden');
   });
   document.getElementById('custom-i-ching-form').addEventListener('submit', function(event) {
     event.preventDefault();
     const urlInput = document.getElementById('urlInput').value;
     const fileInput = document.getElementById('fileInput').files[0];
     let contentToSave = '';
     if (urlInput.trim() !== '') {
       fetch(urlInput)
         .then(response => {
           if (!response.ok) {
             throw new Error('Network response was not ok');
           }
           return response.text();
         })
         .then(data => {
           contentToSave = data;
           saveCustomIChing(contentToSave);
         })
         .catch(error => {
           console.error('There was a problem fetching the URL:', error);
         });
     } else if (fileInput) {
       const reader = new FileReader();
       reader.onload = function(event) {
         contentToSave = event.target.result;
         saveCustomIChing(contentToSave);
       };
       reader.readAsText(fileInput);
     } else {
       console.error('Please enter a URL or upload a file.');
     }
   });
   });
</script>
<div class="p-5">
  <!-- Astrologer Mode -->
  <button id="isAstrologerMode-mode-toggle-button" type="button" class="bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false" onclick="toggleMode('isAstrologerMode')">
    <span id="isAstrologerMode-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="isAstrologerMode-toggle-link" class="pl-1" href="#" onclick="toggleMode('isAstrologerMode')"></a>
  <br />
  <!-- Amino Acid Mode -->
  <button id="isAminoAcidMode-mode-toggle-button" type="button" class="mt-6 bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false" onclick="toggleMode('isAminoAcidMode')">
    <span id="isAminoAcidMode-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="isAminoAcidMode-toggle-link" class="pl-1" href="#" onclick="toggleMode('isAminoAcidMode')"></a>
  <br />
  <!-- Dashboard Gate and Line Info -->
  <button id="dashboardGateAndLineInfo-mode-toggle-button" type="button" class="mt-6 bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false" onclick="toggleMode('dashboardGateAndLineInfo')">
    <span id="dashboardGateAndLineInfo-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="dashboardGateAndLineInfo-toggle-link" class="pl-1" href="#" onclick="toggleMode('dashboardGateAndLineInfo')"></a>
  <div class="hidden pt-5" id="custom-i-ching-already-uploaded">You have successfully added a custom I'Ching file. <span id="replace-i-ching-link" class="underline cursor-pointer">Replace</span></div>
  <div class="hidden pt-5" id="upload-custom-i-ching">
    <h2 class="text-xl font-semibold mb-4">Upload I'Ching File</h2>
    <form id="custom-i-ching-form" class="space-y-4">
      <div id="upload-file">
        <label for="fileInput" class="block mb-1 font-medium">Upload File:</label>
        <input type="file" id="fileInput" name="file" accept=".txt" class="w-full">
      </div>
      <div id="enter-url" class="hidden">
        <label for="urlInput" class="block mb-1 font-medium">Enter URL:</label>
        <input type="text" id="urlInput" name="url" class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
      </div>
      <button type="submit" class="bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600">Save</button><br />
      <span id="enter-a-url-link" class="text-xs underline cursor-pointer">Or enter a URL</span>
    </form>
  </div>
  <br />
  <!-- Dashboard Rave Mandala - Show Godheads -->
  <button id="dashboardRaveMandalaShowGodheads-mode-toggle-button" onclick="toggleMode('dashboardRaveMandalaShowGodheads')" type="button" class="mt-6 bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false">
    <span id="dashboardRaveMandalaShowGodheads-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="dashboardRaveMandalaShowGodheads-toggle-link" class="pl-1" href="#" onclick="toggleMode('dashboardRaveMandalaShowGodheads')"></a>
  <br />
  <!-- Dashboard Rave Mandala - Show Zodiac -->
  <button id="dashboardRaveMandalaShowZodiac-mode-toggle-button" onclick="toggleMode('dashboardRaveMandalaShowZodiac')" type="button" class="mt-6 bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false">
    <span id="dashboardRaveMandalaShowZodiac-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="dashboardRaveMandalaShowZodiac-toggle-link" class="pl-1" href="#" onclick="toggleMode('dashboardRaveMandalaShowZodiac')"></a>
  <br />
  <!-- Dashboard Rave Mandala - Use Zodiac Symbols -->
  <button id="dashboardRaveMandalaUseZodiacSymbols-mode-toggle-button" onclick="toggleMode('dashboardRaveMandalaUseZodiacSymbols')" type="button" class="mt-6 bg-gray-200 relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2" role="switch" aria-checked="false">
    <span id="dashboardRaveMandalaUseZodiacSymbols-mode-toggle-span" aria-hidden="true" class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
  </button>
  <a id="dashboardRaveMandalaUseZodiacSymbols-toggle-link" class="pl-1" href="#" onclick="toggleMode('dashboardRaveMandalaUseZodiacSymbols')"></a>
  <br />
</div>
