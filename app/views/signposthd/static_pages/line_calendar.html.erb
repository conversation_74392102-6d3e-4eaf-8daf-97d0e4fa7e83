<div class="p-5 grid grid-cols-7 gap-4">
  <% @entries_by_day.each do |day, entries| %>
    <div class="border border-gray-300 p-4">
      <p class="text-gray-600"><%= entries.first.ts.strftime("%a") %></p>
      <!-- Display day of the week for the group -->
      <div>
        <% entries.each do |entry| %>
          <p class="font-bold text-lg">L<%= entry.sun_line.floor %></p>
          <p id="entry-<%= entry.id %>" class="text-xs text-gray-400"><%= (entry.ts + 6.hours).strftime("%Y-%m-%d %H:%M:%S") %> UTC</p>
          <!-- JavaScript to convert UTC time to local time -->
          <script type="text/javascript">
            // Get the UTC time string
            var utcTimeString = '<%= (entry.ts + 6.hours).strftime("%Y-%m-%d %H:%M:%S") %>';

            // Create a Date object with the UTC time string
            var utcDate = new Date(utcTimeString + ' UTC');

            // Extract date components
            var month = utcDate.toLocaleString('default', { month: 'long' });
            var day = utcDate.getDate();
            var hour = utcDate.getHours() % 12 || 12; // Convert hour to 12-hour format
            var minute = utcDate.getMinutes();
            var ampm = utcDate.getHours() >= 12 ? 'PM' : 'AM';

            // Construct local time string
            var localTimeString = `${month} ${day} at ${hour}:${minute < 10 ? '0' : ''}${minute} ${ampm}.`;

            // Create <span> element for local time
            var localTimeSpan = document.createElement('span');

            localTimeSpan.innerHTML = '<br />Local Time: ' + localTimeString;

            // Create <br> element
            var lineBreak = document.createElement('br');

            // Append the <span> and <br> elements to the parent element
            document.getElementById('entry-<%= entry.id %>').appendChild(localTimeSpan);
            document.getElementById('entry-<%= entry.id %>').appendChild(lineBreak);
          </script>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
