<div class="mt-3 ml-6" style="max-width: 800px;">
  <h3 class="font-bold text-2xl pb-1">Famous People</h3>
  <p class="pb-6">Here you will find the bodygraphs of notable celebrities in the arts and sciences.</p>
  <% categories = {
    painters: "painter", 
    filmmakers: "filmmaker", 
    actors: "actor", 
    musicians: "musician", 
    singers: "singer", 
    writers: "writer", 
    poets: "poet", 
    photographers: "photographer", 
    comedians: "comedian", 
    philosophers: "philosopher", 
    psychologists: "psychologist", 
    fashion_designers: "fashion designer",
    scientists: "scientist"
  } %>
  <% categories.each do |variable_name, profession| %>
    <h3 class="font-bold text-lg pb-2"><%= profession.pluralize %></h3>
    <div class="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 pb-6">
      <% bodygraphs = @public_bodygraphs.where(profession: profession) %>
      <% bodygraphs.each do |bodygraph| %>
        <% personality_sun_line = JSON.parse(bodygraph.personality_activations)['sun_line'].to_i %>
        <% design_sun_line = JSON.parse(bodygraph.design_activations)['sun_line'].to_i %>
        <a href="<%= public_bodygraph_path(bodygraph) %>" class="block p-4 border rounded-lg shadow-md font-semibold text-s">
          <%= "#{bodygraph.name} (#{personality_sun_line}/#{design_sun_line} #{bodygraph.aura_type}), #{bodygraph.notable_for}" %>
        </a>
      <% end %>
    </div>
  <% end %>
</div>
