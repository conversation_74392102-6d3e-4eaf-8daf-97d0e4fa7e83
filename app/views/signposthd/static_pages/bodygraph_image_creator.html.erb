<div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow">
  <object id="bodygraph" type="image/svg+xml" data="<%= image_url('bodygraph-blank.svg') %>" width="430px"></object>
</div>
<div>
  <button id="addGateActivation">Add Gate Activation</button>
  <div id="gateActivation"></div>
  <button id="addCenterActivation">Add Center Activation</button>
  <div id="centerActivation"></div>
  <input type="checkbox" id="autoActivateCenters">
  <label for="autoActivateCenters">Automatically Activate Centers</label>
</div>
<script>
  document.getElementById('bodygraph').addEventListener('load', function() {
    let gateActivations = [];
    let centerActivations = [];
   	const svgDoc = this.contentDocument;
  	if (!svgDoc) return;

  	// The Centers
  	const headCenter = svgDoc.getElementById('Head');
  	const ajnaCenter = svgDoc.getElementById('Ajna');
  	const throatCenter = svgDoc.getElementById('Throat');
  	const egoCenter = svgDoc.getElementById('Ego');
  	const gCenter = svgDoc.getElementById('G');
  	const solarPlexusCenter = svgDoc.getElementById('SolarPlexus');
  	const spleenCenter = svgDoc.getElementById('Spleen');
  	const sacralCenter = svgDoc.getElementById('Sacral');
  	const rootCenter = svgDoc.getElementById('Root');

  	const centers = ['Head', 'Ajna', 'Throat', 'Spleen', 'Ego', 'G', 'SolarPlexus', 'Sacral', 'Root'];
  	const colors = ['#FCD34D', '#48BB78', '#B7791F', '#B7791F', '#F56565', '#FCD34D', '#B7791F', '#F56565', '#B7791F'];

  	centers.forEach((center, index) => {
  		const centerElement = svgDoc.getElementById(center);

  		// Create a linear gradient element
  		const gradient = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
  		gradient.setAttribute('id', `${center.toLowerCase()}Gradient`);
  		gradient.setAttribute('x1', '0%');
  		gradient.setAttribute('y1', '0%');
  		gradient.setAttribute('x2', '0%');
  		gradient.setAttribute('y2', '140%');

  		// Add two gradient stops
  		const stop1 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
  		stop1.setAttribute('offset', '0%');
  		stop1.setAttribute('stop-color', colors[index]);
  		gradient.appendChild(stop1);

  		const stop2 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
  		stop2.setAttribute('offset', '100%');
  		stop2.setAttribute('stop-color', 'dark' + colors[index].substring(1));
  		gradient.appendChild(stop2);

  		// Append the gradient to the defs element or create defs if it doesn't exist
  		let defs = svgDoc.querySelector('defs');
  		if (!defs) {
  			defs = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'defs');
  			centerElement.appendChild(defs);
  		}
  		defs.appendChild(gradient);
  	});

  	// Populate the bodygraph
  	const activatedGates = {};
  	// Object.keys(latestEntry).forEach(planet => {
  	// 	const gate = latestEntry[planet].g;
  	// 	activatedGates[gate] = true;
  	// 	const hangingGate = svgDoc.getElementById(`Gate${gate}`);
  	// 	const gateText = svgDoc.getElementById(`GateText${gate}`);
  	// 	const gateBackground = svgDoc.getElementById(`GateTextBg${gate}`);
  	// 	const gateBackgroundPath = gateBackground.querySelector('path');
  	// 	const gateBackgroundCircle = gateBackground.querySelector('circle');
  	// 	if (hangingGate && gateText && gateBackground) {
  	// 		hangingGate.style.fill = 'black';
  	// 		gateText.style.fill = 'white';
  	// 		if (gateBackgroundPath) gateBackgroundPath.style.fill = 'black';
  	// 		if (gateBackgroundCircle) gateBackgroundCircle.style.fill = 'black';
  	// 	}

  	// 	// Fill in the labels
  	// 	const line = latestEntry[planet].l;
  	// 	document.getElementById(`bodygraph-label-${planet}`).innerHTML = `${gate}.${line}`;
  	// });

  	// Color in the Centers if channels are made
  	if (
  		(activatedGates[3] && activatedGates[60]) ||
  		(activatedGates[42] && activatedGates[53]) ||
  		(activatedGates[9] && activatedGates[52])
  	) {
  		// Set the gradient as the fill for the path element within sacralCenter
  		sacralCenter.querySelector('path').style.fill = 'url(#sacralGradient)';
  		rootCenter.querySelector('path').style.fill = 'url(#rootGradient)';
  	}

  	// Color in the Spleen and Solar Plexus Center if channels are made
  	if (
  		(activatedGates[19] && activatedGates[49]) ||
  		(activatedGates[39] && activatedGates[55]) ||
  		(activatedGates[41] && activatedGates[30])
  	) {
  		// Set the gradient as the fill for the path element within spleenCenter and solarPlexusCenter
  		rootCenter.querySelector('path').style.fill = 'url(#rootGradient)';
  		solarPlexusCenter.querySelector('path').style.fill = 'url(#solarplexusGradient)';
  	}

  	// Color in the Spleen and Root Center if channels are made
  	if ((activatedGates[54] && activatedGates[32]) || (activatedGates[28] && activatedGates[38]) || (activatedGates[18] && activatedGates[58])) {
  		// Set the gradient as the fill for the path element within spleenCenter and rootCenter
  		spleenCenter.querySelector('path').style.fill = 'url(#spleenGradient)';
  		rootCenter.querySelector('path').style.fill = 'url(#rootGradient)';
  	}

  	// Color in the Spleen and Throat Center if channels are made
  	if ((activatedGates[48] && activatedGates[16]) || (activatedGates[57] && activatedGates[20])) {
  		// Set the gradient as the fill for the path element within spleenCenter and throatCenter
  		spleenCenter.querySelector('path').style.fill = 'url(#spleenGradient)';
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  	}

  	// Color in the Spleen and G Center if channels are made
  	if (activatedGates[57] && activatedGates[10]) {
  		// Set the gradient as the fill for the path element within spleenCenter and gCenter
  		spleenCenter.querySelector('path').style.fill = 'url(#spleenGradient)';
  		gCenter.querySelector('path').style.fill = 'url(#gGradient)';
  	}

  	// Color in the Sacral and Throat Center if channels are made
  	if (activatedGates[34] && activatedGates[20]) {
  		// Set the gradient as the fill for the path element within sacralCenter and throatCenter
  		sacralCenter.querySelector('path').style.fill = 'url(#sacralGradient)';
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  	}

  	// Color in the Sacral and G Center if channels are made
  	if ((activatedGates[34] && activatedGates[10]) || (activatedGates[50] && activatedGates[27])) {
  		// Set the gradient as the fill for the path element within sacralCenter and gCenter
  		sacralCenter.querySelector('path').style.fill = 'url(#sacralGradient)';
  		gCenter.querySelector('path').style.fill = 'url(#gGradient)';
  	}

  	// Color in the Sacral and Spleen Center if channels are made
  	if ((activatedGates[57] && activatedGates[34]) || (activatedGates[59] && activatedGates[6])) {
  		// Set the gradient as the fill for the path element within sacralCenter and spleenCenter
  		sacralCenter.querySelector('path').style.fill = 'url(#sacralGradient)';
  		spleenCenter.querySelector('path').style.fill = 'url(#spleenGradient)';
  	}

  	// Color in the Solar Plexus and Ego Center if channels are made
  	if (activatedGates[37] && activatedGates[40]) {
  		// Set the gradient as the fill for the path element within solarPlexusCenter and egoCenter
  		solarPlexusCenter.querySelector('path').style.fill = 'url(#solarplexusGradient)';
  		egoCenter.querySelector('path').style.fill = 'url(#egoGradient)';
  	}

  	// Color in the Solar Plexus and Throat Center if channels are made
  	if ((activatedGates[22] && activatedGates[12]) || (activatedGates[35] && activatedGates[36])) {
  		// Set the gradient as the fill for the path element within solarPlexusCenter and throatCenter
  		solarPlexusCenter.querySelector('path').style.fill = 'url(#solarplexusGradient)';
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  	}

  	// Color in the Ego and G Center if channels are made
  	if (activatedGates[51] && activatedGates[25]) {
  		// Set the gradient as the fill for the path element within egoCenter and gCenter
  		egoCenter.querySelector('path').style.fill = 'url(#egoGradient)';
  		gCenter.querySelector('path').style.fill = 'url(#gGradient)';
  	}

  	// Color in the Ego and Throat Center if channels are made
  	if (activatedGates[21] && activatedGates[45]) {
  		// Set the gradient as the fill for the path element within egoCenter and throatCenter
  		egoCenter.querySelector('path').style.fill = 'url(#egoGradient)';
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  	}

  	// Color in the Sacral and G Center if channels are made
  	if (
  		(activatedGates[29] && activatedGates[46]) ||
  		(activatedGates[14] && activatedGates[2]) ||
  		(activatedGates[5] && activatedGates[15])
  	) {
  		// Set the gradient as the fill for the path element within sacralCenter and gCenter
  		sacralCenter.querySelector('path').style.fill = 'url(#sacralGradient)';
  		gCenter.querySelector('path').style.fill = 'url(#gGradient)';
  	}

  	// Color in the G and Throat Center if channels are made
  	if (
  		(activatedGates[7] && activatedGates[31]) ||
  		(activatedGates[1] && activatedGates[8]) ||
  		(activatedGates[13] && activatedGates[33])
  	) {
  		// Set the gradient as the fill for the path element within gCenter and throatCenter
  		gCenter.querySelector('path').style.fill = 'url(#gGradient)';
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  	}

  	// Color in the Throat and Ajna Center if channels are made
  	if (
  		(activatedGates[17] && activatedGates[62]) ||
  		(activatedGates[43] && activatedGates[23]) ||
  		(activatedGates[56] && activatedGates[11])
  	) {
  		// Set the gradient as the fill for the path element within throatCenter and ajnaCenter
  		throatCenter.querySelector('path').style.fill = 'url(#throatGradient)';
  		ajnaCenter.querySelector('path').style.fill = 'url(#ajnaGradient)';
  	}

  	// Color in the Ajna and Head Center if channels are made
  	if ((activatedGates[47] && activatedGates[6]) || (activatedGates[4] && activatedGates[61]) || (activatedGates[24] && activatedGates[63])) {
  		// Set the gradient as the fill for the path element within ajnaCenter and headCenter
  		ajnaCenter.querySelector('path').style.fill = 'url(#ajnaGradient)';
  		headCenter.querySelector('path').style.fill = 'url(#headGradient)';
  	}


  // Function to update the SVG based on added activations
  function updateSVG() {

  }

  // Add Gate Activation button click event
  document.getElementById('addGateActivation').addEventListener('click', function() {
      let gateActivationDropdown = document.createElement('select');
      gateActivationDropdown.classList.add('gateDropdown');
      for (let i = 1; i <= 64; i++) {
          let option = document.createElement('option');
          option.text = 'Gate ' + i;
          option.value = i;
          gateActivationDropdown.appendChild(option);
      }

      let colorDropdown = document.createElement('select');
      colorDropdown.classList.add('colorDropdown');
      let blackOption = document.createElement('option');
      blackOption.text = 'Black';
      blackOption.value = 'black';
      colorDropdown.appendChild(blackOption);
      let redOption = document.createElement('option');
      redOption.text = 'Red';
      redOption.value = 'red';
      colorDropdown.appendChild(redOption);

      let removeButton = document.createElement('button');
      removeButton.textContent = 'Remove';
      removeButton.classList.add('removeGateActivation');

      let gateActivationLine = document.createElement('div');
      gateActivationLine.appendChild(gateActivationDropdown);
      gateActivationLine.appendChild(colorDropdown);
      gateActivationLine.appendChild(removeButton);
      document.getElementById('gateActivation').appendChild(gateActivationLine);

      gateActivationDropdown.addEventListener('change', function() {
          // Update the SVG according to the selected gate and color
      });

      colorDropdown.addEventListener('change', function() {
          // Update the SVG according to the selected color
      });

      removeButton.addEventListener('click', function() {
          gateActivationLine.remove(); // Remove the gate activation line
          updateSVG(); // Call the updateSVG function if necessary
      });
  });

  // Add Center Activation button click event
  document.getElementById('addCenterActivation').addEventListener('click', function() {
        let centerActivationDropdown = document.createElement('select');
        centerActivationDropdown.classList.add('centerDropdown');

        const centerIds = ['Ajna', 'Head', 'SolarPlexus', 'Spleen', 'Throat', 'Sacral', 'Root', 'G', 'Ego'];

        centerIds.forEach(centerId => {
            let option = document.createElement('option');
            option.value = centerId;
            option.text = centerId;
            centerActivationDropdown.appendChild(option);
        });

        // Add the created dropdown to the DOM
        document.getElementById('centerActivation').appendChild(centerActivationDropdown);

      let removeButton = document.createElement('button');
      removeButton.textContent = 'Remove';
      removeButton.classList.add('removeCenterActivation');

      let centerActivationLine = document.createElement('div');
      centerActivationLine.appendChild(centerActivationDropdown);
      centerActivationLine.appendChild(removeButton);
      document.getElementById('centerActivation').appendChild(centerActivationLine);

      centerActivationDropdown.addEventListener('change', function() {
          // Update the SVG according to the selected center
      });

      removeButton.addEventListener('click', function() {
          centerActivationLine.remove(); // Remove the gate activation line
          updateSVG(); // Call the updateSVG function if necessary
      });
  });

  // Automatically Activate Centers checkbox change event
  document.getElementById('autoActivateCenters').addEventListener('change', function() {
      if (this.checked) {
          // Execute additional code for automatic activation of centers
          // You can call a function or trigger an event here to handle the automatic activation
      }
  });
  });
</script>
