<table class="min-w-full divide-y divide-gray-200">
  <thead class="bg-gray-50">
    <tr>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gate</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gate of</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Godhead</th>
      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
      <th scope="col" class="relative px-6 py-3"><span class="sr-only">Show</span></th>
    </tr>
  </thead>
  <tbody class="bg-white divide-y divide-gray-200">
    <% @gates_data.each do |gate_key, gate| %>
      <tr onclick="window.location='<%= gate_path(gate_key) %>'" style="cursor: pointer; transition: background-color 0.3s, box-shadow 0.3s;" onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.boxShadow='0 0 5px rgba(0, 0, 0, 0.1)';" onmouseout="this.style.backgroundColor='inherit'; this.style.boxShadow='none';">
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= gate_key %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= gate[:name] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= gate[:gate_of] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= gate[:godhead] %></td>
        <td class="px-6 py-4 whitespace-nowrap" style="transition: inherit;"><%= gate[:short_description] %></td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" style="transition: inherit;">
          <a href="<%= gate_path(gate_key) %>" class="text-indigo-600 hover:text-indigo-900">View</a>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>
