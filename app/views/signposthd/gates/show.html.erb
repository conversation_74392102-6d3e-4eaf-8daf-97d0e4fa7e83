<div class="max-w-4xl mx-auto py-8">
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h1 class="text-lg font-2xl leading-6 text-gray-900">Gate <%= params[:gate] %></h1>
    </div>
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Navigate</h3>
      <div class="mt-6">
        <table class="min-w-full divide-y divide-gray-200">
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">Previous #</td>
              <td class="px-6 py-4 whitespace-nowrap">Previous</td>
              <td class="px-6 py-4 whitespace-nowrap">Current</td>
              <td class="px-6 py-4 whitespace-nowrap">Next</td>
              <td class="px-6 py-4 whitespace-nowrap">Next #</td>
              <td class="px-6 py-4 whitespace-nowrap">Harmonic</td>
              <td class="px-6 py-4 whitespace-nowrap">Polarity</td>
              <td class="px-6 py-4 whitespace-nowrap">Mirror</td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @previous_gate_number, gate_path(@previous_gate_number) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @previous_gate, gate_path(@previous_gate) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= params[:gate] %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @next_gate, gate_path(@next_gate) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @next_gate_number, gate_path(@next_gate_number) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @harmonic_gate, gate_path(@harmonic_gate) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @opposite_gate, gate_path(@opposite_gate) %></td>
              <td class="px-6 py-4 whitespace-nowrap"><%= link_to @mirror_gate, gate_path(@mirror_gate) %></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Gate Information</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about Gate <%= params[:gate] %>.</p>
    </div>
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Gate</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= params[:gate] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Name</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @gate[:name] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Gate of</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @gate[:gate_of] %></dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Godhead</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @gate[:godhead] %></dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Description</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:col-span-2"><%= @gate[:short_description] %></dd>
        </div>
      </dl>
    </div>
  </div>
  <div class="mt-6">
    <%= link_to 'Back to All Gates', gates_path, class: 'text-indigo-600 hover:text-indigo-900' %>
  </div>
</div>
