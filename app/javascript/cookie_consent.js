document.addEventListener('DOMContentLoaded', function() {
  // Check if user has already consented
  if (!localStorage.getItem('cookieConsent')) {
    // Determine the cookie policy URL based on the current domain
    let cookiePolicyUrl = '/cookie-policy';
    const hostname = window.location.hostname;

    if (hostname.includes('signposthd')) {
      cookiePolicyUrl = '/cookie-policy/';
    } else if (hostname.includes('highdeserthumandesign')) {
      cookiePolicyUrl = '/cookie-policy';
    } else if (hostname.includes('sfhdl')) {
      cookiePolicyUrl = '/cookie-policy/';
    } else if (hostname.includes('centerforhumandesign')) {
      cookiePolicyUrl = '/cookie-policy/';
    }

    // Create cookie consent banner
    const banner = document.createElement('div');
    banner.id = 'cookie-consent-banner';
    banner.className = 'fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 z-50';
    banner.innerHTML = `
      <div class="container mx-auto flex flex-col md:flex-row items-center justify-between">
        <div class="mb-4 md:mb-0 md:mr-4">
          <p class="text-sm">
            This website uses cookies to ensure you get the best experience on our website.
            <a href="${cookiePolicyUrl}" class="underline hover:text-gray-300">Learn more</a>
          </p>
        </div>
        <div class="flex space-x-2">
          <button id="cookie-accept" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">Accept All</button>
          <button id="cookie-reject" class="bg-gray-700 hover:bg-gray-800 text-white px-4 py-2 rounded text-sm">Reject Non-Essential</button>
        </div>
      </div>
    `;

    // Append banner to body
    document.body.appendChild(banner);

    // Add event listeners to buttons
    document.getElementById('cookie-accept').addEventListener('click', function() {
      localStorage.setItem('cookieConsent', 'accepted');
      banner.remove();
    });

    document.getElementById('cookie-reject').addEventListener('click', function() {
      localStorage.setItem('cookieConsent', 'rejected');
      banner.remove();

      // Here you would add code to disable non-essential cookies
      // For example, disabling Google Analytics:
      window['ga-disable-UA-XXXXX-Y'] = true;
    });
  }
});
