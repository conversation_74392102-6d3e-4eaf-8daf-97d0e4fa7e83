import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Add click event listeners to all notification links
    document.querySelectorAll('[data-notification-id]').forEach(link => {
      link.addEventListener('click', this.markAsRead.bind(this));
    });
  }
  
  markAsRead(event) {
    // Don't prevent default navigation - we still want to follow the link
    const notificationId = event.currentTarget.dataset.notificationId;
    
    // Send AJAX request to mark notification as read
    fetch(`/notifications/${notificationId}/mark_as_read`, {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update UI to reflect read status
        this.updateNotificationStatus();
      }
    })
    .catch(error => console.error('Error marking notification as read:', error));
  }
  
  markAllAsRead(event) {
    event.preventDefault();
    
    // Send AJAX request to mark all notifications as read
    fetch('/notifications/mark_all_as_read', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update UI to reflect all notifications read
        this.updateNotificationStatus();
        
        // Update the notification dropdown content
        const unreadContainer = document.getElementById('unread-notifications');
        if (unreadContainer) {
          unreadContainer.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">You have no new notifications.</div>';
        }
      }
    })
    .catch(error => console.error('Error marking all notifications as read:', error));
  }
  
  updateNotificationStatus() {
    // Check if there are any unread notifications left
    fetch('/notifications/unread_count', {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      },
      credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
      // Update the notification indicator
      const indicator = document.getElementById('notification-indicator');
      if (data.count === 0) {
        // Hide the red dot if no unread notifications
        if (indicator) indicator.style.display = 'none';
        
        // Update Alpine.js state
        const notificationComponent = Alpine.getComponent(document.querySelector('[x-data*="hasUnread"]'));
        if (notificationComponent) {
          notificationComponent.hasUnread = false;
        }
      }
    })
    .catch(error => console.error('Error checking unread notifications:', error));
  }
}
