// This file is loaded by importmaps, not by esbuild
import { Application } from "@hotwired/stimulus"

// Initialize Stimulus application
const application = Application.start()

// Configure Stimulus development experience
application.debug = false
window.Stimulus = application

// Import and register all controllers
import NotificationsController from "controllers/notifications_controller"
application.register("notifications", NotificationsController)

// Export the application for use in other files
export { application }
