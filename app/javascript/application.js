// Entry point for the build script in your package.json

// This file is processed by esbuild, which doesn't handle the imports below
// They are marked as external in the esbuild configuration and handled by importmaps

// The following imports are for documentation purposes only
// They will be handled by importmaps, not by esbuild
// import "@hotwired/stimulus"
// import "./controllers"

// Any actual code that should be processed by esbuild goes here
console.log('Application JavaScript loaded')