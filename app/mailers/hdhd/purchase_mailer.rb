module Hdhd
  class PurchaseMailer < ApplicationMailer
    def confirmation_email
      @user = params[:user]
      @product = params[:product]
      @purchase_date = Time.current

      mail(
        to: @user.email,
        subject: "Your purchase of #{@product.title} was successful!"
      )
    end

    def account_creation_email
      @user = params[:user]
      @product = params[:product]
      @password = params[:password]

      mail(
        to: @user.email,
        subject: "Account Created: High Desert Human Design"
      )
    end
  end
end
