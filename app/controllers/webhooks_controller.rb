class WebhooksController < ApplicationController
  # Skip CSRF protection for webhooks
  protect_from_forgery except: :stripe
  # Skip authentication for webhooks
  skip_before_action :authenticate_user!, only: [:stripe, :test], raise: false

  # Test page for webhook configuration
  def test
    # This action renders the test.html.erb view
  end

  def create_user_from_purchase(email, product)
    # Generate password from word list
    word_list = ['split-definition', 'profile', 'bodygraph', 'strategy', 'authority', 'defined', 'undefined', 'center', 'gate', 'line', 'color', 'tone', 'base', 'channel', 'circuitry', 'incarnation-cross', 'triple-split', 'single-definition', 'head', 'ajna', 'throat', 'g-center', 'will-center', 'heart-center', 'spleen', 'sacral', 'solar-plexus', 'root', 'emotional-center', 'self-projected', 'mental-projector']
    password = word_list.sample(4).join('-')

    # Create user
    user = User.new(
      email: email,
      password: password,
      password_confirmation: password
    )

    # Auto-confirm users created from purchases
    user.skip_confirmation!
    user.save!

    # Send welcome email with password
    Hdhd::PurchaseMailer.with(
      user: user,
      product: product,
      password: password
    ).account_creation_email.deliver_later

    user
  end

  def handle_successful_purchase(session)
    product_id = session.metadata['product_id']
    product = Product.find_by(id: product_id)
    user = User.find_by(email: session.customer_email)

    # Create user if they don't exist
    user ||= create_user_from_purchase(session.customer_email, product)

    # Create purchased product
    user.purchased_products.create!(product: product)

    # Create notification
    Notification.create!(
      user: user,
      title: "Purchase Successful!",
      message: "Thank you for purchasing #{product.title}!",
      action_url: "/users/library?product_id=#{product.id}"
    )

    # Send confirmation email
    Hdhd::PurchaseMailer.with(user: user, product: product).confirmation_email.deliver_later
  end

  def handle_conference_ticket_purchase(session)
    conference_id = session.metadata['conference_id']
    user_id = session.metadata['user_id']
    referrer_speaker_id = session.metadata['referrer_speaker_id']
    discount_type = session.metadata['discount_type']
    platform_fee_percentage = session.metadata['platform_fee_percentage']

    conference = Hdhd::Conference.find_by(id: conference_id)
    user = User.find_by(id: user_id) || User.find_by(email: session.customer_email)
    referrer_speaker = referrer_speaker_id.present? ? Hdhd::Speaker.find_by(id: referrer_speaker_id) : nil

    # Create user if they don't exist (for purchases from friends and family page)
    unless user
      # Only create a user if we have an email
      if session.customer_email.present?
        user = create_user_from_purchase(session.customer_email, nil)
      else
        Rails.logger.error "Cannot create conference ticket: No user found and no email provided"
        return
      end
    end

    return unless conference && user

    # Determine the price based on discount type
    price = if discount_type == 'friends_and_family'
              # 10% discount for friends and family
              (conference.ticket_price * 0.9).round(2)
            else
              conference.ticket_price
            end

    # Create conference ticket
    ticket = ConferenceTicket.create!(
      user: user,
      conference: conference,
      referrer_speaker: referrer_speaker,
      stripe_session_id: session.id,
      stripe_payment_intent_id: session.payment_intent,
      price: price,
      status: 'purchased',
      discount_type: discount_type,
      platform_fee_percentage: platform_fee_percentage.present? ? platform_fee_percentage.to_f : nil
    )

    # Create notification
    Notification.create!(
      user: user,
      title: "Conference Ticket Purchased!",
      message: "Thank you for purchasing a ticket to HDHD #{conference.year}!",
      action_url: "/dashboard"
    )

    # Send confirmation email
    Hdhd::ConferenceTicketMailer.with(user: user, conference: conference, ticket: ticket).confirmation_email.deliver_later

    # If this was a referral, notify the speaker
    return unless referrer_speaker && referrer_speaker.user

    Notification.create!(
      user: referrer_speaker.user,
      title: "New Ticket Sale!",
      message: "Someone purchased a ticket through your Friends & Family link!",
      action_url: "/speakers/#{referrer_speaker.id}/edit"
    )
  end

  def stripe
    # Log the request domain for debugging
    Rails.logger.info "Webhook received from domain: #{request.host}"

    # Get the webhook payload and signature header
    payload = request.body.read
    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    webhook_secret = Rails.env.production? ? ENV.fetch('STRIPE_WEBHOOK_PRODUCTION_SECRET', nil) : ENV.fetch('STRIPE_WEBHOOK_SECRET', nil)

    begin
      # Verify webhook signature and extract the event
      event = Stripe::Webhook.construct_event(
        payload, sig_header, webhook_secret
      )

      # Handle the event
      case event.type
      when 'checkout.session.completed'
        session = event.data.object
        Rails.logger.info "Processing checkout.session.completed for session ID: #{session.id}"

        if session.metadata['ticket_type'] == 'conference'
          handle_conference_ticket_purchase(session)
        else
          handle_successful_purchase(session)
        end
      when 'payment_intent.succeeded'
        payment_intent = event.data.object
        Rails.logger.info "Payment succeeded for PaymentIntent: #{payment_intent.id}"
      when 'payment_intent.payment_failed'
        payment_intent = event.data.object
        Rails.logger.error "Payment failed for PaymentIntent: #{payment_intent.id}"
      else
        Rails.logger.info "Unhandled event type: #{event.type}"
      end

      # Return a 200 success response to acknowledge receipt of the event
      head :ok
    rescue JSON::ParserError => e
      # Invalid payload
      Rails.logger.error "Webhook error: #{e.message}"
      head :bad_request
    rescue Stripe::SignatureVerificationError => e
      # Invalid signature
      Rails.logger.error "Webhook signature verification failed: #{e.message}"
      head :bad_request
    rescue StandardError => e
      # Other errors
      Rails.logger.error "Webhook error: #{e.class.name} - #{e.message}"
      head :ok # Still return 200 to prevent Stripe from retrying
    end
  end
end
