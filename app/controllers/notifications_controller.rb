class NotificationsController < ApplicationController
  before_action :authenticate_user!

  def mark_as_read
    notification = current_user.notifications.find(params[:id])
    notification.update(read: true)

    respond_to do |format|
      format.json { render json: { success: true } }
      format.html { redirect_back(fallback_location: dashboard_path) }
    end
  end

  def mark_all_as_read
    current_user.notifications.unread.update_all(read: true)

    respond_to do |format|
      format.json { render json: { success: true } }
      format.html { redirect_back(fallback_location: dashboard_path) }
    end
  end

  def unread_count
    count = current_user.notifications.unread.count

    respond_to do |format|
      format.json { render json: { count: count } }
    end
  end
end
