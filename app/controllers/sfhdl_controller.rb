class SfhdlController < ApplicationController
  def home; end

  def about; end

  def browse
    @books = Book.all
    @ebooks = Ebook.all
    @cds = Cd.all
    @tapes = Tape.all
    @audios = Audio.all
    @videos = Video.all
  end

  def authors
    @authors = Author.all
  end

  def author
    <AUTHOR> Author.find(params[:id])
    @books = @author.books
    @ebooks = @author.ebooks
    @cds = @author.cds
    @tapes = @author.tapes
    @audios = @author.audios
    @videos = @author.videos
  end

  def terms_and_conditions; end

  def privacy_policy; end

  def cookie_policy; end

  layout 'sfhdl/application'
end
