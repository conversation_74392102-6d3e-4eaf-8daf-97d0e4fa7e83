class Signposthd::StreamingVideosController < ApplicationController
  before_action :set_streaming_video, only: %i[show watch edit update destroy]
  before_action :set_layout
  before_action :check_admin_access, only: %i[new edit]

  # GET /streaming_videos or /streaming_videos.json
  def admin_index
    @streaming_videos = StreamingVideo.all
  end

  # Landing page fullscreen viewer at /streaming-videos
  def index
    @streaming_videos = StreamingVideo.all.order(created_at: :desc)
  end

  # GET /streaming_videos/1 or /streaming_videos/1.json
  def show; end

  # GET /streaming_videos/1/watch
  def watch; end

  # GET /streaming_videos/new
  def new
    @streaming_video = StreamingVideo.new
  end

  # GET /streaming_videos/1/edit
  def edit; end

  # POST /streaming_videos or /streaming_videos.json
  def create
    @streaming_video = StreamingVideo.new(streaming_video_params)

    respond_to do |format|
      if @streaming_video.save
        format.html do
          redirect_to streaming_video_url(@streaming_video), notice: 'Streaming video was successfully created.'
        end
        format.json { render :show, status: :created, location: @streaming_video }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @streaming_video.errors, status: :unprocessable_entity }
      end
    end
  end

# PATCH/PUT /streaming_videos/1 or /streaming_videos/1.json
def update
  respond_to do |format|
    if @streaming_video.update(streaming_video_params)
      format.html do
        redirect_to streaming_video_url(@streaming_video), notice: 'Streaming video was successfully updated.'
      end
      format.json { render :show, status: :ok, location: @streaming_video }
    else
      format.html { render :edit, status: :unprocessable_entity }
      format.json { render json: @streaming_video.errors, status: :unprocessable_entity }
    end
  end
end


  # DELETE /streaming_videos/1 or /streaming_videos/1.json
  def destroy
    @streaming_video.destroy

    respond_to do |format|
      format.html { redirect_to streaming_videos_url, notice: 'Streaming video was successfully destroyed.' }
      format.json { head :no_content }
    end
  end



  private


# Use a private method to filter and sanitize the incoming parameters
def streaming_video_params
  # Permit cover and cover_small attributes
  permitted_params = params.require(:streaming_video).permit(:cover, :cover_small)

  # If cover or cover_small is blank, remove it from the permitted parameters
  permitted_params.delete(:cover) if permitted_params[:cover].blank?
  permitted_params.delete(:cover_small) if permitted_params[:cover_small].blank?

  permitted_params
end



  # Use callbacks to share common setup or constraints between actions.
  def set_streaming_video
    @streaming_video = StreamingVideo.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def streaming_video_params
    params.require(:streaming_video).permit(:title, :is_4k, :runtime_minutes, :url, :short_description, :cover, :cover_small,:year)
  end

  def set_layout
    if action_name == 'index'
      self.class.layout 'signposthd/streaming_videos'
    elsif action_name == 'watch'
      self.class.layout 'signposthd/fullscreen'
    else
      self.class.layout 'signposthd/signed_in'
    end
  end
end
