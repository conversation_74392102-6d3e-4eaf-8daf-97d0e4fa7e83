# app/controllers/color_controller.rb
class Signposthd::ColorController < ApplicationController
  before_action :set_color_data, only: [:index, :show]

  def index; end

  def show
    @color = @color_data[params[:color]]
  end

  private

  def set_color_data
    @color_data = {
      '1' => {
        motivation: 'Fear',
        determination: 'Appetite',
        outlook: 'pessimistic',
        resilience: 'low resilience',
        environment: 'Caves',
        view: 'Survival',
        trajectory: 'Communalist-Separatist'
      },
      '2' => {
        motivation: 'Hope',
        determination: 'Taste',
        outlook: 'pessimistic',
        resilience: 'low resilience',
        environment: 'Markets',
        view: 'Possibility',
        trajectory: 'Theist-Antitheist'
      },
      '3' => {
        motivation: 'Desire',
        determination: 'Thirst',
        outlook: 'realistic',
        resilience: 'medium resilience',
        environment: 'Kitchens',
        view: 'Power',
        trajectory: 'Leader-Follower'
      },
      '4' => {
        motivation: 'Need',
        determination: 'Touch',
        outlook: 'realistic',
        resilience: 'medium resilience',
        environment: 'Mountains',
        view: 'Wanting',
        trajectory: 'Master-Novi<PERSON>'
      },
      '5' => {
        motivation: 'Guilt',
        determination: 'Sound',
        outlook: 'optimistic',
        resilience: 'high resilience',
        environment: 'Valleys',
        view: 'Probability',
        trajectory: 'Conditioner-Conditioned'
      },
      '6' => {
        motivation: 'Innocence',
        determination: 'Light',
        outlook: 'optimistic',
        resilience: 'high resilience',
        environment: 'Shores',
        view: 'Personal',
        trajectory: 'Observer-Observed'
      }
    }
  end

  layout 'signposthd/signed_in'
end
