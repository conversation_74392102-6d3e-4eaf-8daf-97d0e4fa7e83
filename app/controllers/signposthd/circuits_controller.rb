class Signposthd::CircuitsController < ApplicationController
  before_action :set_circuits_data, only: [:index, :show]

  def index
  end

  def show
    circuit_param = params[:circuit]
    @circuit = @circuits_data[circuit_param]
    render 'show'
  end

  private

    def set_circuits_data
      @circuits_data = {
        'tribal' => {
          name: 'Tribal',
          gates: [],
          channels: []
        },
        'collective_logic' => {
          name: 'Collective Logic',
          gates: [],
          channels: []
        },
        'collective_abstract' => {
          name: 'Collective Abstract',
          gates: [],
          channels: []
        },
        'individual' => {
          name: 'Individual',
          gates: [],
          channels: []
        },
        'integration' => {
          name: 'Integration',
          gates: [],
          channels: []
        }
      }
    end

  layout 'signposthd/signed_in'
end
