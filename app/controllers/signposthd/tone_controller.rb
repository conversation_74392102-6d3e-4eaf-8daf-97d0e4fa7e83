# app/controllers/tone_controller.rb
class Signposthd::ToneController < ApplicationController
  before_action :set_tone_data, only: [:index, :show]

  def index; end

  def show
    @tone = @tone_data[params[:tone]]
  end

  private

  def set_tone_data
    @tone_data = {
      '1' => {
        sense: 'Security',
        cognition: 'Smell',
        mood_setter: 'Success',
        tonal_binary: 'Splenic',
        binary: 'Concentrated',
        awareness_stream: 'Stream of Instinct'
      },
      '2' => {
        sense: 'Uncertainty',
        cognition: 'Taste',
        mood_setter: 'Success',
        tonal_binary: 'Splenic',
        binary: 'Concentrated',
        awareness_stream: 'Stream of Taste'
      },
      '3' => {
        sense: 'Action',
        cognition: 'Outer Vision',
        mood_setter: 'Respect',
        tonal_binary: 'Ajna',
        binary: 'Periodic',
        awareness_stream: 'Stream of Understanding'
      },
      '4' => {
        sense: 'Meditation',
        cognition: 'Inner Vision',
        mood_setter: 'Respect',
        tonal_binary: 'Ajna',
        binary: 'Periodic',
        awareness_stream: 'Stream of Sensing'
      },
      '5' => {
        sense: 'Judgment',
        cognition: 'Feeling',
        mood_setter: 'Feeling',
        tonal_binary: 'Solar Plexus',
        binary: 'Cyclic',
        awareness_stream: 'Stream of Feeling'
      },
      '6' => {
        sense: 'Acceptance',
        cognition: 'Touch',
        mood_setter: 'Feeling',
        tonal_binary: 'Solar Plexus',
        binary: 'Cyclic',
        awareness_stream: 'Stream of Sensitivity'
      }
    }
  end

  layout 'signposthd/signed_in'
end
