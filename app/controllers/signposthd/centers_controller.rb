# app/controllers/centers_controller.rb

class Signposthd::CentersController < ApplicationController
  before_action :set_centers_data

  def index; end

  def show
    @center = @centers_data[params[:center]]
  end

  private

  def set_centers_data
    @centers_data = {
      'root' => {
        name: 'Root',
        gates: [58, 38, 54, 53, 60, 52, 19, 39, 41],
        channels: ['18-58-judgment', '28-38-struggle', '54-32-transformation', '53-42-maturation', '60-03-mutation', '52-09-concentration', '19-49-synthesis-channel', '39-55-emoting', '41-30-recognition'],
        circuits: ['Tribal', 'Collective Logic', 'Individual', 'Collective Abstract']
      },
      'spleen' => {
        name: 'Spleen',
        gates: [18, 28, 32, 50, 44, 57, 48],
        channels: ['18-58-judgment', '28-38-struggle', '32-54-transformation', '50-27-preservation', '44-26-surrender', '57-10-power', '57-34-exploration', '57-20-charisma', '48-16-the-wavelength'],
        circuits: ['Tribal', 'Collective Logic', 'Individual', 'Integration']
      },
      'solar_plexus' => {
        name: 'Solar Plexus',
        gates: [6, 37, 36, 22, 49, 55, 30],
        channels: ['6-59-intimacy', '37-40-community', '36-35-transitoriness', '22-12-openness', '49-19-synthesis-channel', '55-39-emoting', '30-41-recognition'],
        circuits: ['Tribal', 'Individual', 'Collective Abstract']
      },
      'sacral' => {
        name: 'Sacral',
        gates: [2, 14, 5, 29, 59, 9, 3, 42, 34, 27],
        channels: ['2-14-the-beat', '5-15-rhythm', '29-46-discovery', '6-59-intimacy', '9-52-concentration', '3-60-mutation', '42-53-maturation', '34-10-awakening', '34-57-power', '34-20-the-brainwave', '27-50-preservation'],
        circuits: ['Tribal', 'Collective Logic', 'Individual', 'Collective Abstract', 'Integration']
      },
      'ego' => {
        name: 'Ego',
        gates: [51, 26, 21, 45],
        channels: ['25-51-initiation', '26-44-surrender', '21-45-the-money-line', '45-21-the-money-line'],
        circuits: ['Tribal', 'Individual']
      },
      'g_center' => {
        name: 'G Center',
        gates: [15, 14, 46, 25, 13, 7, 1, 10],
        channels: ['5-15-rhythm', '2-14-the-beat', '29-46-discovery', '25-51-initiation', '13-33-the-prodigal', '7-31-the-alpha', '1-8-inspiration', '10-57-perfected-form', '10-34-exploration', '10-20-awakening'],
        circuits: ['Collective Logic', 'Individual', 'Collective Abstract']
      },
      'throat' => {
        name: 'Throat',
        gates: [16, 62, 23, 56, 35, 12, 45, 33, 8, 31, 20],
        channels: ['16-48-the-wavelength', '17-62-acceptance', '23-43-structuring', '11-56-curiosity', '35-36-transitoriness', '12-22-openness', '45-21-the-money-line', '33-13-the-prodigal', '8-01-inspiration', '31-07-the-alpha', '20-57-the-brainwave', '20-34-charisma'],
        circuits: ['Tribal', 'Collective Logic', 'Individual', 'Collective Abstract', 'Integration']
      },
      'ajna' => {
        name: 'Ajna',
        gates: [47, 24, 4, 17, 43, 11],
        channels: ['64-47-abstraction', '61-24-awareness', '63-04-logic', '17-62-acceptance', '43-23-structuring', '11-56-curiosity'],
        circuits: ['Collective Logic', 'Individual', 'Collective Abstract']
      },
      'head' => {
        name: 'Head',
        gates: [64, 61, 63],
        channels: ['64-47-abstraction', '61-24-awareness', '63-04-logic'],
        circuits: ['Collective Logic', 'Individual', 'Collective Abstract']
      }
    }
  end

  layout 'signposthd/signed_in'
end
