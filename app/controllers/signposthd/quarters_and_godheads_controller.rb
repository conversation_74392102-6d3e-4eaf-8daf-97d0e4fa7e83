class Signposthd::QuartersAndGodheadsController < ApplicationController
  before_action :set_godheads_data, only: :show

  def index; end

  def show
    @godhead = @godheads_data[params[:godhead]]
  end

  private

  def set_godheads_data
    @godheads_data = {
      'kali' => {
        name: '<PERSON>',
        gates: [13, 49, 30, 55],
        circuits: ['Collective Abstract', 'Tribal', 'Individual'],
        quarter: 'Initiation'
      },
      'mitra' => {
        name: '<PERSON><PERSON>',
        gates: [37, 63, 22, 36],
        circuits: ['Collective Logic', 'Tribal', 'Individual'],
        quarter: 'Initiation'
      },
      'michael' => {
        name: '<PERSON>',
        gates: [25, 17, 21, 51],
        circuits: ['Collective Logic', 'Tribal', 'Individual'],
        quarter: 'Initiation'
      },
      'janus' => {
        name: '<PERSON><PERSON>',
        gates: [42, 3, 27, 24],
        circuits: ['Tribal', 'Individual'],
        quarter: 'Initiation'
      },
      'maia' => {
        name: '<PERSON>',
        gates: [2, 23, 8, 20],
        circuits: ['Individual', 'Collective Logic'],
        quarter: 'Civilization'
      },
      'lakshmi' => {
        name: 'Lakshmi',
        gates: [16, 35, 45, 12],
        circuits: ['Tribal', 'Individual'],
        quarter: 'Civilization'
      },
      'parvati' => {
        name: 'Parvati',
        gates: [15, 52, 39, 53],
        circuits: ['Individual', 'Collective Abstract'],
        quarter: 'Civilization'
      },
      'maat' => {
        name: 'Maat',
        gates: [62, 56, 31, 33],
        circuits: ['Collective Abstract', 'Tribal', 'Collective Logic'],
        quarter: 'Civilization'
      },
      'thoth' => {
        name: 'Thoth',
        gates: [7, 4, 29, 59],
        circuits: ['Tribal', 'Collective Logic'],
        quarter: 'Duality'
      },
      'harmonia' => {
        name: 'Harmonia',
        gates: [40, 64, 47, 6],
        circuits: ['Tribal', 'Collective Abstract'],
        quarter: 'Duality'
      },
      'christ' => {
        name: 'Christ',
        gates: [46, 18, 48, 57],
        circuits: ['Individual', 'Collective Logic'],
        quarter: 'Duality'
      },
      'minerva' => {
        name: 'Minerva',
        gates: [32, 50, 28, 44],
        circuits: ['Collective Logic', 'Tribal'],
        quarter: 'Duality'
      },
      'hades' => {
        name: 'Hades',
        gates: [1, 43, 14, 34],
        circuits: ['Individual'],
        quarter: 'Mutation'
      },
      'prometheus' => {
        name: 'Prometheus',
        gates: [9, 5, 26, 11],
        circuits: ['Individual', 'Collective Abstract'],
        quarter: 'Mutation'
      },
      'vishnu' => {
        name: 'Vishnu',
        gates: [10, 58, 38, 54],
        circuits: ['Individual', 'Collective Logic'],
        quarter: 'Mutation'
      },
      'keepers-of-the-wheel' => {
        name: 'Keepers of the Wheel',
        gates: [61, 60, 41, 19],
        circuits: ['Collective Abstract', 'Tribal'],
        quarter: 'Mutation'
      }
    }
  end

  layout 'signposthd/signed_in'
end
