# app/controllers/lines_controller.rb
class Signposthd::LinesController < ApplicationController
  before_action :set_lines_data, only: [:index, :show]

  def index; end

  def show
    @line = @lines_data[params[:line]]
  end

  private

  def set_lines_data
    @lines_data = {
      '1' => {
        name: 'Introspection',
        profiles: ['1/3', '1/4', '4/1', '5/1'],
        harmonic: 4,
        quality: 'yin',
        triplicity: 'fixed',
        reactivity: 'reactive'
      },
      '2' => {
        name: 'Projection',
        profiles: ['2/4', '2/5', '5/2', '6/2'],
        harmonic: 5,
        quality: 'yang',
        triplicity: 'cardinal',
        reactivity: 'reactive'
      },
      '3' => {
        name: 'Adaptation',
        profiles: ['1/3', '3/5', '3/6', '6/3'],
        harmonic: 6,
        quality: 'yin',
        triplicity: 'mutable',
        reactivity: 'opportunistic'
      },
      '4' => {
        name: 'Externalization',
        profiles: ['1/4', '2/5', '4/1', '4/6'],
        harmonic: 1,
        quality: 'yang',
        triplicity: 'fixed',
        reactivity: 'opportunistic'
      },
      '5' => {
        name: 'Universalization',
        profiles: ['3/5', '4/1', '5/1', '5/2'],
        harmonic: 2,
        quality: 'yin',
        triplicity: 'cardinal',
        reactivity: 'initiating'
      },
      '6' => {
        name: 'Transition',
        profiles: ['3/6', '4/6', '5/2', '6/3'],
        harmonic: 3,
        quality: 'yang',
        triplicity: 'mutable',
        reactivity: 'initiating'
      }
    }
  end

  layout 'signposthd/signed_in'
end
