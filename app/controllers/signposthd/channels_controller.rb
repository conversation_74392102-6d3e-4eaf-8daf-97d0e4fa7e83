class Signposthd::ChannelsController < ApplicationController
  before_action :set_channels_data

  def index; end

  def show
    @channel = @channels_data[params[:channel]]
  end

  private

  def set_channels_data
    @channels_data = {
      '1-8-inspiration' => {
        name: 'Inspiration',
        gates: [1, 8],
        circuit: 'Individual'
      },
      '2-14-the-beat' => {
        name: 'The Beat',
        gates: [2, 14],
        circuit: 'Individual'
      },
      '3-60-mutation' => {
        name: 'Mutation',
        gates: [3, 60],
        circuit: 'Individual'
      },
      '4-63-logic' => {
        name: 'Logic',
        gates: [4, 63],
        circuit: 'Collective Logic'
      },
      '5-15-rhythm' => {
        name: 'Rhythm',
        gates: [5, 15],
        circuit: 'Collective Logic'
      },
      '6-59-intimacy' => {
        name: 'Intimacy',
        gates: [6, 59],
        circuit: 'Tribal'
      },
      '7-31-the-alpha' => {
        name: 'The Alpha',
        gates: [7, 31],
        circuit: 'Collective Logic'
      },
      '9-52-concentration' => {
        name: 'Concentration',
        gates: [9, 52],
        circuit: 'Collective Logic'
      },
      '10-20-awakening' => {
        name: 'Awakening',
        gates: [10, 20],
        circuit: 'Individual'
      },
      '10-34-exploration' => {
        name: 'Exploration',
        gates: [10, 34],
        circuit: 'Individual'
      },
      '10-57-perfected-form' => {
        name: 'Perfected Form',
        gates: [10, 57],
        circuit: 'Integration'
      },
      '11-56-curiosity' => {
        name: 'Curiosity',
        gates: [11, 56],
        circuit: 'Collective Abstract'
      },
      '12-22-openness' => {
        name: 'Openness',
        gates: [12, 22],
        circuit: 'Individual'
      },
      '13-33-the-prodigal' => {
        name: 'The Prodigal',
        gates: [13, 33],
        circuit: 'Collective Abstract'
      },
      '16-48-the-wavelength' => {
        name: 'The Wavelength',
        gates: [16, 48],
        circuit: 'Collective Logic'
      },
      '17-62-acceptance' => {
        name: 'Acceptance',
        gates: [17, 62],
        circuit: 'Collective Logic'
      },
      '18-58-judgment' => {
        name: 'Judgment',
        gates: [18, 58],
        circuit: 'Collective Logic'
      },
      '19-49-synthesis-channel' => {
        name: 'Synthesis Channel',
        gates: [19, 49],
        circuit: 'Tribal'
      },
      '20-34-charisma' => {
        name: 'Charisma',
        gates: [20, 34],
        circuit: 'Integration'
      },
      '20-57-the-brainwave' => {
        name: 'The Brainwave',
        gates: [20, 57],
        circuit: 'Individual'
      },
      '21-45-the-money-line' => {
        name: 'The Money Line',
        gates: [21, 45],
        circuit: 'Tribal'
      },
      '23-43-structuring' => {
        name: 'Structuring',
        gates: [23, 43],
        circuit: 'Individual'
      },
      '24-61-awareness' => {
        name: 'Awareness',
        gates: [24, 61],
        circuit: 'Individual'
      },
      '25-51-initiation' => {
        name: 'Initiation',
        gates: [25, 51],
        circuit: 'Tribal'
      },
      '26-44-surrender' => {
        name: 'Surrender',
        gates: [26, 44],
        circuit: 'Tribal'
      },
      '27-50-preservation' => {
        name: 'Preservation',
        gates: [27, 50],
        circuit: 'Tribal'
      },
      '28-38-struggle' => {
        name: 'Struggle',
        gates: [28, 38],
        circuit: 'Individual'
      },
      '29-46-discovery' => {
        name: 'Discovery',
        gates: [29, 46],
        circuit: 'Collective Abstract'
      },
      '30-41-recognition' => {
        name: 'Recognition',
        gates: [30, 41],
        circuit: 'Collective Abstract'
      },
      '32-54-transformation' => {
        name: 'Transformation',
        gates: [32, 54],
        circuit: 'Tribal'
      },
      '34-57-power' => {
        name: 'Power',
        gates: [34, 57],
        circuit: 'Individual'
      },
      '35-36-transitoriness' => {
        name: 'Transitoriness',
        gates: [35, 36],
        circuit: 'Collective Abstract'
      },
      '37-40-community' => {
        name: 'Community',
        gates: [37, 40],
        circuit: 'Tribal'
      },
      '39-55-emoting' => {
        name: 'Emoting',
        gates: [39, 55],
        circuit: 'Individual'
      },
      '42-53-maturation' => {
        name: 'Maturation',
        gates: [42, 53],
        circuit: 'Collective Abstract'
      },
      '47-64-abstraction' => {
        name: 'Abstraction',
        gates: [47, 64],
        circuit: 'Collective Abstract'
      }
    }
  end

  layout 'signposthd/signed_in'
end
