class Signposthd::PublicBodygraphCommentsController < ApplicationController
  before_action :set_public_bodygraph_comment, only: %i[ show edit update destroy ]

  # GET /public_bodygraph_comments or /public_bodygraph_comments.json
  def index
    @public_bodygraph_comments = PublicBodygraphComment.all
  end

  # GET /public_bodygraph_comments/1 or /public_bodygraph_comments/1.json
  def show
  end

  # GET /public_bodygraph_comments/new
  def new
    @public_bodygraph_comment = PublicBodygraphComment.new
  end

  # GET /public_bodygraph_comments/1/edit
  def edit
  end

  # POST /public_bodygraph_comments or /public_bodygraph_comments.json
  def create
    @public_bodygraph_comment = PublicBodygraphComment.new(public_bodygraph_comment_params)

    respond_to do |format|
      if @public_bodygraph_comment.save
        format.html { redirect_to public_bodygraph_comment_url(@public_bodygraph_comment), notice: "Public bodygraph comment was successfully created." }
        format.json { render :show, status: :created, location: @public_bodygraph_comment }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @public_bodygraph_comment.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /public_bodygraph_comments/1 or /public_bodygraph_comments/1.json
  def update
    respond_to do |format|
      if @public_bodygraph_comment.update(public_bodygraph_comment_params)
        format.html { redirect_to public_bodygraph_comment_url(@public_bodygraph_comment), notice: "Public bodygraph comment was successfully updated." }
        format.json { render :show, status: :ok, location: @public_bodygraph_comment }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @public_bodygraph_comment.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /public_bodygraph_comments/1 or /public_bodygraph_comments/1.json
  def destroy
    @public_bodygraph_comment.destroy

    respond_to do |format|
      format.html { redirect_to public_bodygraph_comments_url, notice: "Public bodygraph comment was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_public_bodygraph_comment
      @public_bodygraph_comment = PublicBodygraphComment.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def public_bodygraph_comment_params
      params.require(:public_bodygraph_comment).permit(:title, :href, :file_attachment, :comment_type, :public_bodygraph_id)
    end
end
