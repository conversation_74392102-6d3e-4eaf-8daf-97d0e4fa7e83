# app/controllers/base_controller.rb
class Signposthd::BaseController < ApplicationController
  before_action :set_base_data, only: [:index, :show]

  def index; end

  def show
    @base = @base_data[params[:base]]
  end

  private

  def set_base_data
    @base_data = {
      '1' => {
        name: 'Uniqueness',
        statement: 'I Define',
        macrocosm: 'Movement',
        microcosm: 'Individuality',
        physics: 'Magnetic Monopole',
        microcosm_connections: ['1-4-earth', '1-3-friction'],
        macrocosm_connections: ['1-4-position', '1-3-force']
      },
      '2' => {
        name: 'Role',
        statement: 'I Remember',
        macrocosm: 'Evolution',
        microcosm: 'Mind',
        physics: 'Electron',
        microcosm_connections: ['2-3-humanity', '2-4-civilization'],
        macrocosm_connections: ['2-3-material', '2-4-speed']
      },
      '3' => {
        name: 'Genetics',
        statement: 'I Am',
        macrocosm: 'Being',
        microcosm: 'Body',
        physics: 'Quark',
        microcosm_connections: ['1-3-friction', '2-3-humanity'],
        macrocosm_connections: ['1-3-force', '2-3-material']
      },
      '4' => {
        name: 'Self',
        statement: 'I Design',
        macrocosm: 'Design',
        microcosm: 'Ego',
        physics: 'Neutrino',
        microcosm_connections: ['1-4-earth', '2-4-civilization'],
        macrocosm_connections: ['1-4-position', '2-4-speed']
      },
      '5' => {
        name: 'Presence',
        statement: 'I Think',
        macrocosm: 'Personality',
        microcosm: 'Space',
        physics: 'Space',
        microcosm_connections: [],
        macrocosm_connections: []
      }
    }
  end

  layout 'signposthd/signed_in'
end
