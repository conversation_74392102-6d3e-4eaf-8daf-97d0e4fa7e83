Activator = Struct.new(:id, :name)

class Signposthd::SequenceAnalyzerController < ApplicationController
  include BodygraphsHelper
  include SequenceAnalyzerHelper

  before_action :authenticate_user!

  def index
    @gates = GATES
    @harmonic_gates = HARMONIC_GATES
    @centers_by_gate = CENTERS_BY_GATE
    @planet_glyphs = PLANET_GLYPHS

    @user = current_user
    @bodygraphs = @user.bodygraphs
    @bodygraph = params[:bodygraph].present? ? @user.bodygraphs.find(params[:bodygraph]) : @bodygraphs.last
    @personality_activations = JSON.parse(@bodygraph.personality_activations)
    @design_activations = JSON.parse(@bodygraph.design_activations)

    activator_id = params[:activator]
    activator_name = activator_names_by_id[params[:activator]]
    @activators = activators
    @activator = Activator.new(activator_id, activator_name)
    @activations_by_gate = activations_by_gate

    @defined = {
      'root' => @bodygraph.root_defined,
      'solar plexus' => @bodygraph.solar_plexus_defined,
      'throat' => @bodygraph.throat_defined,
      'ajna' => @bodygraph.ajna_defined,
      'ego' => @bodygraph.ego_defined,
      'g center' => @bodygraph.g_center_defined,
      'head' => @bodygraph.head_defined,
      'sacral' => @bodygraph.sacral_defined,
      'spleen' => @bodygraph.spleen_defined
    }

    @date = Time.now - 6.hours
    @latest_entry = EphemerisEntry.where('ts <= ?', @date).order(ts: :desc).limit(1).first

    @planets_by_gate = planets_by_gate(@latest_entry)

    activator_gate = {
      'sun' => @latest_entry.sun_gate,
      'earth' => opposite_gate(@latest_entry.sun_gate),
      'north_node' => @latest_entry.north_node_gate,
      'south_node' => opposite_gate(@latest_entry.north_node_gate),
      'moon' => @latest_entry.moon_gate,
      'mercury' => @latest_entry.mercury_gate,
      'venus' => @latest_entry.venus_gate,
      'mars' => @latest_entry.mars_gate,
      'jupiter' => @latest_entry.jupiter_gate,
      'saturn' => @latest_entry.saturn_gate,
      'uranus' => @latest_entry.uranus_gate,
      'neptune' => @latest_entry.neptune_gate,
      'pluto' => @latest_entry.pluto_gate
    }
    @current_activator_gate = activator_gate[activator_id]
  end

  layout 'signposthd/signed_in'
end
