class Signposthd::GatesController < ApplicationController
  include BodygraphsHelper
  before_action :set_gates_data

  def index; end

  def show
    @gate = @gates_data[params[:gate]]
    @harmonic_gate = harmonic_gate(params[:gate]).to_s
    @opposite_gate = opposite_gate(params[:gate]).to_s
    @next_gate = next_gate(params[:gate]).to_s
    @previous_gate = previous_gate(params[:gate]).to_s
    @next_gate_number = next_gate_number(params[:gate]).to_s
    @previous_gate_number = previous_gate_number(params[:gate]).to_s
    @mirror_gate = mirror_gate(params[:gate]).to_s
  end

  private

  def set_gates_data
    @gates_data = {
      '1' => {
        name: 'The Creative',
        gate_of: 'Gate of Self-Expression',
        godhead: 'Kali',
        short_description: 'Creativity Rooted in Unique Direction'
      },
      '2' => {
        name: 'The Receptive',
        gate_of: 'Gate of the Direction of Self',
        godhead: '<PERSON>',
        short_description: 'The Driver'
      },
      '3' => {
        name: 'Difficulties at the Beginning',
        gate_of: 'Gate of Ordering',
        godhead: 'Jan<PERSON>',
        short_description: 'Mutation is Generated & Empowered'
      },
      '4' => {
        name: 'Youth<PERSON> Folly',
        gate_of: 'Gate of Formulization',
        godhead: 'Thoth',
        short_description: 'Logic Protects us From Misfortune'
      },
      '5' => {
        name: 'Waiting',
        gate_of: 'Gate of Fixed Rhythms',
        godhead: 'Prometheus',
        short_description: 'The Fixed Pattern is Sacred'
      },
      '6' => {
        name: 'Conflict',
        gate_of: 'Gate of Friction',
        godhead: 'Harmonia',
        short_description: 'Feeling, Emoting, and Sensitivity'
      },
      '7' => {
        name: 'The Army',
        gate_of: 'Gate of Role of the Self in Interaction',
        godhead: 'Thoth',
        short_description: 'The Elected Leader'
      },
      '8' => {
        name: 'Holding Together',
        gate_of: 'Gate of Contribution',
        godhead: 'Maia',
        short_description: 'Leading by Example'
      },
      '9' => {
        name: 'The Taming Power of the Small',
        gate_of: 'Gate of Focus',
        godhead: 'Prometheus',
        short_description: 'Focus Empowers the Entire Process'
      },
      '10' => {
        name: 'Treading',
        gate_of: 'Gate of Behavior of the Self',
        godhead: 'Vishnu',
        short_description: 'The Love of Self is Awakeness'
      },
      '11' => {
        name: 'Peace',
        gate_of: 'Gate of Ideas',
        godhead: 'Prometheus',
        short_description: 'Ideas are Stimulation for Reflection'
      },
      '12' => {
        name: 'Standstill',
        gate_of: 'Gate of Caution',
        godhead: 'Lakshmi',
        short_description: 'Releasing Awareness in the Proper Spirit'
      },
      '13' => {
        name: 'The Fellowship of Man',
        gate_of: 'Gate of the Listener',
        godhead: 'Kali',
        short_description: 'The Hearer of Secrets'
      },
      '14' => {
        name: 'Possession in Great Measure',
        gate_of: 'Gate of Power Skills',
        godhead: 'Hades',
        short_description: 'Fuel to Empower'
      },
      '15' => {
        name: 'Modesty',
        gate_of: 'Gate of Extremes',
        godhead: 'Parvati',
        short_description: 'The Love of Humanity'
      },
      '16' => {
        name: 'Enthusiasm',
        gate_of: 'Gate of Skills',
        godhead: 'Lakshmi',
        short_description: 'The Skills for Living - Life as Art'
      },
      '17' => {
        name: 'Following',
        gate_of: 'Gate of Opinions',
        godhead: 'Michael',
        short_description: 'Grounded in Detail'
      },
      '18' => {
        name: 'Work on What Has Been Spoilt',
        gate_of: 'Gate of Correction',
        godhead: 'Christ',
        short_description: 'Essential Learning'
      },
      '19' => {
        name: 'Approach',
        gate_of: 'Gate of Wanting',
        godhead: 'Keepers of the Wheel',
        short_description: 'Fuel for our Social Needs'
      },
      '20' => {
        name: 'Contemplation',
        gate_of: 'Gate of the Now',
        godhead: 'Maia',
        short_description: 'I Am Now'
      },
      '21' => {
        name: 'Biting Through',
        gate_of: 'Gate of the Hunter/Huntress',
        godhead: 'Michael',
        short_description: 'Strength of Will'
      },
      '22' => {
        name: 'Grace',
        gate_of: 'Gate of Openness',
        godhead: 'Mitra',
        short_description: 'Sharing Spirit with Others'
      },
      '23' => {
        name: 'Splitting Apart',
        gate_of: 'Gate of Assimilation',
        godhead: 'Maia',
        short_description: 'The Elimination of Intolerance'
      },
      '24' => {
        name: 'Returning',
        gate_of: 'Gate of Rationalizing',
        godhead: 'Janus',
        short_description: 'Inpiration Must be Given a Rational Form'
      },
      '25' => {
        name: 'Innocence',
        gate_of: 'Gate of Spirit of Self',
        godhead: 'Michael',
        short_description: 'The Spiritual Warrior'
      },
      '26' => {
        name: 'The Taming Power of the Great',
        gate_of: 'Gate of the Egotist',
        godhead: 'Prometheus',
        short_description: 'Fools are Liars and Prophets Too'
      },
      '27' => {
        name: 'Nourishment',
        gate_of: 'Gate of Caring',
        godhead: 'Janus',
        short_description: 'Care for Oneself First'
      },
      '28' => {
        name: 'Preponderance of the Great',
        gate_of: 'Gate of the Game Player',
        godhead: 'Minerva',
        short_description: 'The Challenge is Life Itself'
      },
      '29' => {
        name: 'The Abysmal',
        gate_of: 'Gate of Saying Yes',
        godhead: 'Thoth',
        short_description: 'Energy to Persevere Despite Circumstance'
      },
      '30' => {
        name: 'The Clinging Fire',
        gate_of: 'Gate of Recognition of Feelings',
        godhead: 'Kali',
        short_description: 'Surrendering to Fate'
      },
      '31' => {
        name: 'Influence',
        gate_of: 'Gate of Leading',
        godhead: 'Maat',
        short_description: 'I Lead'
      },
      '32' => {
        name: 'Duration',
        gate_of: 'Gate of Continuity',
        godhead: 'Minerva',
        short_description: 'Conservatism'
      },
      '33' => {
        name: 'Retreat',
        gate_of: 'Gate of Privacy',
        godhead: 'Maat',
        short_description: 'The Revelation of Secrets'
      },
      '34' => {
        name: 'The Power of the Great',
        gate_of: 'Gate of Might',
        godhead: 'Hades',
        short_description: 'Pure Unconditional Power'
      },
      '35' => {
        name: 'Progress',
        gate_of: 'Gate of Change',
        godhead: 'Lakshmi',
        short_description: 'The Secret is "No Expectation"'
      },
      '36' => {
        name: 'Darkening of the Light',
        gate_of: 'Gate of Crisis',
        godhead: 'Mitra',
        short_description: 'Wait for Emotional Clarity'
      },
      '37' => {
        name: 'The Family',
        gate_of: 'Gate of Friendship',
        godhead: 'Mitra',
        short_description: 'Ready to Embrace the Outsider'
      },
      '38' => {
        name: 'Opposition',
        gate_of: 'Gate of Fighter',
        godhead: 'Vishnu',
        short_description: 'Stubborness Can Overcome the Odds'
      },
      '39' => {
        name: 'Obstruction',
        gate_of: 'Gate of the Provocateur',
        godhead: 'Parvati',
        short_description: 'The Journey is Toward Spirit'
      },
      '40' => {
        name: 'Deliverance',
        gate_of: 'Gate of Aloneness',
        godhead: 'Harmonia',
        short_description: 'The Power of the Will to Deliver'
      },
      '41' => {
        name: 'Decrease',
        gate_of: 'Gate of Contraction',
        godhead: 'Keepers of the Wheel',
        short_description: 'Patience is the Great Virtue'
      },
      '42' => {
        name: 'Increase',
        gate_of: 'Gate of Growth',
        godhead: 'Janus',
        short_description: 'The "Grail" is the Experience'
      },
      '43' => {
        name: 'Breakthrough',
        gate_of: 'Gate of Insight',
        godhead: 'Hades',
        short_description: 'Listen to Your Own Inner Voice'
      },
      '44' => {
        name: 'Coming to Meet',
        gate_of: 'Gate of Alertness',
        godhead: 'Minerva',
        short_description: 'The Personnel Manager'
      },
      '45' => {
        name: 'Gathering Together',
        gate_of: 'Gate of Gatherer',
        godhead: 'Lakshmi',
        short_description: 'The Ruler'
      },
      '46' => {
        name: 'Pushing Upward',
        gate_of: 'Gate of the Determination of the Self',
        godhead: 'Christ',
        short_description: 'The Body is the Temple'
      },
      '47' => {
        name: 'Oppression',
        gate_of: 'Gate of Realizing',
        godhead: 'Harmonia',
        short_description: 'Wait for the Moment of Realization'
      },
      '48' => {
        name: 'The Well',
        gate_of: 'Gate of Depth',
        godhead: 'Christ',
        short_description: 'A Resource Available in the Now'
      },
      '49' => {
        name: 'Revolution',
        gate_of: 'Gate of Rejection',
        godhead: 'Kali',
        short_description: 'Potentially Aware and Ritualistically Spiritual'
      },
      '50' => {
        name: 'The Cauldron',
        gate_of: 'Gate of Values',
        godhead: 'Minerva',
        short_description: 'Guarding and Maintaining the Tribe'
      },
      '51' => {
        name: 'The Arousing',
        gate_of: 'Gate of Shock',
        godhead: 'Michael',
        short_description: 'Arousing Empowers the Direction of Love'
      },
      '52' => {
        name: 'Keeping Still',
        gate_of: 'Gate of Inaction',
        godhead: 'Parvati',
        short_description: 'Focused and Channeled Energy'
      },
      '53' => {
        name: 'Development',
        gate_of: 'Gate of Beginnings',
        godhead: 'Parvati',
        short_description: 'Transition and Change'
      },
      '54' => {
        name: 'The Marrying Maiden',
        gate_of: 'Gate of Ambition',
        godhead: 'Vishnu',
        short_description: 'The Drive to Rise Up'
      },
      '55' => {
        name: 'Abundance',
        gate_of: 'Gate of Spirit',
        godhead: 'Kali',
        short_description: 'The Quality of Spirit is in the Emotional Now'
      },
      '56' => {
        name: 'The Wanderer',
        gate_of: 'Gate of Stimulation',
        godhead: 'Maat',
        short_description: 'The Storyteller'
      },
      '57' => {
        name: 'The Gentle',
        gate_of: 'Gate of Intuition',
        godhead: 'Christ',
        short_description: 'Penetrate to the Core in the Now'
      },
      '58' => {
        name: 'The Joyous',
        gate_of: 'Gate of Aliveness',
        godhead: 'Vishnu',
        short_description: 'The Vitality to Challenge'
      },
      '59' => {
        name: 'Dispersion',
        gate_of: 'Gate of Sexuality',
        godhead: 'Thoth',
        short_description: 'Bonding and Intimacy Beyond Words'
      },
      '60' => {
        name: 'Limitation',
        gate_of: 'Gate of Acceptance',
        godhead: 'Keepers of the Wheel',
        short_description: 'The Pulsing Pressure to Mutate'
      },
      '61' => {
        name: 'Inner Truth',
        gate_of: 'Gate of Mystery',
        godhead: 'Keepers of the Wheel',
        short_description: 'The Pulsing Pressure to Mutate'
      },
      '62' => {
        name: 'Preponderance of the Small',
        gate_of: 'Gate of Detail',
        godhead: 'Maat',
        short_description: 'Manifestation Through Detail'
      },
      '63' => {
        name: 'After Completion',
        gate_of: 'Gate of Doubt',
        godhead: 'Mitra',
        short_description: 'Doubt is an Essential Inspiration'
      },
      '64' => {
        name: 'Before Completion',
        gate_of: 'Gate of Confusion',
        godhead: 'Harmonia',
        short_description: 'Pressurized Mental Activity'
      }
    }
  end

  layout 'signposthd/signed_in'
end
