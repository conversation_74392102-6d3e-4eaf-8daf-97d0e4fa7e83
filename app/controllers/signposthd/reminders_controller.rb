# app/controllers/reminders_controller.rb
class Signposthd::Reminders<PERSON>ontroller < ApplicationController
  before_action :authenticate_user!
  before_action :set_reminder, only: [:show, :edit, :update, :destroy]

  # GET /reminders
  # GET /reminders.json
  def index
    @reminders = current_user.reminders
  end

  # GET /reminders/1
  # GET /reminders/1.json
  def show; end

  # GET /reminders/new
  def new
    @reminder = current_user.reminders.build
  end

  # GET /reminders/1/edit
  def edit; end

  # POST /reminders
  # POST /reminders.json
  def create
    @reminder = current_user.reminders.build(reminder_params)

    respond_to do |format|
      if @reminder.save
        format.html { redirect_to @reminder, notice: 'Reminder was successfully created.' }
        format.json { render :show, status: :created, location: @reminder }
      else
        format.html { render :new }
        format.json { render json: @reminder.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /reminders/1
  # PATCH/PUT /reminders/1.json
  def update
    respond_to do |format|
      if @reminder.update(reminder_params)
        format.html { redirect_to @reminder, notice: '<PERSON>minder was successfully updated.' }
        format.json { render :show, status: :ok, location: @reminder }
      else
        format.html { render :edit }
        format.json { render json: @reminder.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /reminders/1
  # DELETE /reminders/1.json
  def destroy
    @reminder.destroy
    respond_to do |format|
      format.html { redirect_to reminders_url, notice: 'Reminder was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_reminder
    @reminder = current_user.reminders.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def reminder_params
    params.require(:reminder).permit(:utc_time, :timezone, :title, :description)
  end

  layout 'signposthd/signed_in'
end
