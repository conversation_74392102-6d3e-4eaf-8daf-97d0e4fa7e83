class Signposthd::StaticPagesController < ApplicationController
  include StaticPagesHelper
  include BodygraphsHelper

  layout :choose_layout

  before_action :authenticate_user!, only: %i[dashboard] # This will redirect unauthenticated users to sign in

  # Signed out pages.

  def home
    set_meta_tags title: 'Signpost | The only tool for Human Design Professionals',
                  description: 'Homepage for Signpost, the Human Design app.',
                  keywords: 'signpost, Human Design'
    # Get the date and other parameters from params
    @date = if params[:date].present?
              Time.parse(params[:date]) - 7.hours
            else
              Time.now - 6.hours
            end

    @direction = params[:direction]
    @planet = params[:planet]
    @division = params[:division]
    @gate_or_line_number = params[:gate_or_line_number]

    # Call the service to get the daily bodygraph data
    bodygraph_service = DashboardBodygraphService.new(@date, @direction, @planet, @division, @gate_or_line_number)
    bodygraph_data = bodygraph_service.call

    # Set instance variables to be used in the view
    @latest_entry_json = bodygraph_data[:latest_entry_json]
    @next_sun_line_entry = bodygraph_data[:next_sun_line_entry]
  end

  def product
    set_meta_tags title: 'Signpost | About Our Product',
                  description: 'About our product, Signpost.',
                  keywords: 'Human Design, product'
  end

  def features
    set_meta_tags title: 'Signpost | Features',
                  description: 'Features of the Signpost Human Design app.',
                  keywords: 'signpost, features, Human Design'
  end

  def shop
    set_meta_tags title: 'Signpost | Shop',
                  description: 'Shop for Signpost merch here!',
                  keywords: 'signpost, Human Design, merchandise, shop'
  end

  def company
    set_meta_tags title: 'Signpost | About Our Company',
                  description: 'Information about the company Signpost.',
                  keywords: 'Company about page for Signpost, the Human Design app.'
  end

  # Signed-in pages.

  # Dashboard
  def dashboard
    set_meta_tags title: 'Signpost | Dashboard',
                  description: 'Signpost Dashboard.',
                  keywords: 'Dashboard for Signpost, the Human Design app.'

    # Get the date and other parameters from params
    @date = if params[:date].present?
              Time.parse(params[:date]) - 7.hours
            else
              Time.now - 6.hours
            end
    @direction = params[:direction]
    @planet = params[:planet]
    @division = params[:division]
    @gate_or_line_number = params[:gate_or_line_number]

    # Call the service to get the daily bodygraph data
    bodygraph_service = DashboardBodygraphService.new(@date, @direction, @planet, @division, @gate_or_line_number)
    bodygraph_data = bodygraph_service.call

    # Set instance variables to be used in the view
    @latest_entry_json = bodygraph_data[:latest_entry_json]
    @next_sun_line_entry = bodygraph_data[:next_sun_line_entry]
  end

  def reference; end

  def research_center
    @public_bodygraphs = PublicBodygraph.all
  end

  def famous_people
    @public_bodygraphs = PublicBodygraph.where(famous: true)
  end

  def historical_figures
    @public_bodygraphs = PublicBodygraph.where(famous: false)
  end

  def historical_events
    @public_bodygraphs = PublicBodygraph.where(historical_event: true)
  end

  def global_cycles; end

  def line_calendar
    @latest_entry = EphemerisEntry.where('ts <= ?', Time.now - 6.hours).order(ts: :desc).limit(1).first

    # Initialize a hash to store entries by day
    @entries_by_day = Hash.new { |hash, key| hash[key] = [] }

    # Determine the line for the previous entry
    line = @latest_entry.sun_line.floor
    line = 6 if line == 0
    previous_entry = EphemerisEntry.where("ts <= ? AND sun_line = ?", @latest_entry.ts, line)
                                   .order(ts: :desc).limit(1).first

    @entries_by_day[previous_entry.ts.strftime("%Y-%m-%d")] << previous_entry

    # Fetch the next 7 entries
    current_entry = @latest_entry
    7.times do
      # Determine the line for the next entry
      line = current_entry.sun_line.floor + 1.0
      line = 1 if line >= 7

      # Query the database for the next entry
      next_entry = EphemerisEntry.where("ts >= ? AND sun_line = ?", current_entry.ts, line)
                                 .order(ts: :asc).limit(1).first

      # Store the entry in the hash with its associated day
      @entries_by_day[next_entry.ts.strftime("%Y-%m-%d")] << next_entry if next_entry

      # Update the current entry for the next iteration
      current_entry = next_entry
    end
  end

  private

  def cookie_policy; end

  def choose_layout
    case action_name
    when 'product'
      'signposthd/fullscreen'
    when 'home', 'shop', 'features', 'company', 'success', 'privacy_policy', 'terms_and_conditions', 'cookie_policy'
      'signposthd/application'
    else
      'signposthd/signed_in'
    end
  end
end
