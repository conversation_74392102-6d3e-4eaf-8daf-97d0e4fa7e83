class Signposthd::PublicBodygraphsController < ApplicationController
  include ActionView::Helpers::SanitizeHelper
  include BodygraphsHelper
  require 'swe4r'

  before_action :set_public_bodygraph, only: %i[show edit update destroy]
  before_action :authenticate_user! # This will redirect unauthenticated users to sign in
  before_action :check_admin_access, only: %i[new edit]

  # GET /public_bodygraphs or /public_bodygraphs.json
  def index
    @bodygraphs = PublicBodygraph.all
  end

  # GET /public_bodygraphs/1 or /public_bodygraphs/1.json
  def show; end

  # GET /public_bodygraphs/new
  def new
    @public_bodygraph = PublicBodygraph.new
  end

  # GET /public_bodygraphs/1/edit
  def edit; end

  # POST /public_bodygraphs or /public_bodygraphs.json
  def create
    respond_to do |format|
      if (@public_bodygraph = build_bodygraph(public_bodygraph_params, current_user, nil, true)) && @public_bodygraph.save
        format.html { redirect_to public_bodygraph_url(@public_bodygraph), notice: 'Bodygraph was successfully created.' }
        format.json { render :show, status: :created, location: @public_bodygraph }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @public_bodygraph.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /public_bodygraphs/1 or /public_bodygraphs/1.json
  def update
    respond_to do |format|
      if (@public_bodygraph = build_bodygraph(public_bodygraph_params, current_user, @public_bodygraph.id, true)) && @public_bodygraph.update(public_bodygraph_params)
        format.html { redirect_to public_bodygraph_url(@public_bodygraph), notice: 'Bodygraph was successfully updated.' }
        format.json { render :show, status: :ok, location: @public_bodygraph }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @public_bodygraph.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /public_bodygraphs/1 or /public_bodygraphs/1.json
  def destroy
    @public_bodygraph.destroy

    respond_to do |format|
      format.html { redirect_to public_bodygraphs_url, notice: "Public bodygraph was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_public_bodygraph
    @public_bodygraph = PublicBodygraph.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def public_bodygraph_params
    params.require(:public_bodygraph).permit(:name, :birth_name, :birth_date_local, :birth_date_utc, :design_date_utc, :birth_country, :birth_city, :birth_data_source, :birth_data_source_notes, :birth_data_collector, :rodden_rating, :gender, :aura_type, :inner_authority, :definition, :profile, :incarnation_cross, :determination, :environment, :view, :motivation, :cognition, :sense, :variable, :personality_activations, :design_activations, :head_defined, :ajna_defined, :throat_defined, :spleen_defined, :solar_plexus_defined, :g_center_defined, :sacral_defined, :root_defined, :ego_defined, :personality_nodes_tone, :design_nodes_tone, :timezone, :birth_name, :birth_date, :birth_time, :birth_data_source, :birth_data_source_notes, :birth_data_collector, :rodden_rating, :gender, :notable_for, :profession, :famous, :historical_event, :portrait, :description, :all_activated_gates)
  end

  layout 'signposthd/signed_in'
end
