class Hdhd::ConferencePhotosController < ApplicationController
  def index
    @conference = Hdhd::Conference.find_by!(start_date: DateTime.new(params[:conference_year].to_i))
    @conference_photos = @conference.conference_photos
  end

  def show
    @conference_photo = Hdhd::ConferencePhoto.find(params[:id])
  end

  def new
    @conference_photo = Hdhd::ConferencePhoto.new
  end

  def create
    @conference_photo = Hdhd::ConferencePhoto.new(conference_photo_params)

    if @conference_photo.save
      redirect_to hdhd_conference_photo_path(@conference_photo), notice: "Photo uploaded!"
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def conference_photo_params
    params.require(:hdhd_conference_photo).permit(:title, :photographer, :photo, :conference_id)
  end
  layout 'hdhd/application'

end
