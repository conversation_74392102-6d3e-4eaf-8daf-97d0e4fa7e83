module Hdhd
  class StaticPagesController < ApplicationController
    def home; end
    def about; end
    def terms_and_conditions; end
    def cookie_policy; end
    def privacy_policy; end
    def principles; end
    def attendee_agreement; end

    def dashboard
      @conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Time.now.year).first
      @current_year = Time.now.year
      render layout: 'hdhd/signed_in'
    end

    # New actions
    def printouts
      render layout: 'hdhd/signed_in'
    end

    def statistics
      render layout: 'hdhd/signed_in'
    end

    def extras
      render layout: 'hdhd/signed_in'
    end

    def schedule
      render layout: 'hdhd/signed_in'
    end

    def map
      render layout: 'hdhd/signed_in'
    end

    layout 'hdhd/application'
  end
end
