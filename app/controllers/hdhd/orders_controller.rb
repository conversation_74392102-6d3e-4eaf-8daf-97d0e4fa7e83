module Hdhd
  class OrdersController < ApplicationController
    before_action :authenticate_user!

    def create_checkout_session
      @product = Product.find(params[:product_id])

      # Create Stripe product and price if they don't exist
      unless @product.stripe_price_id.present?
        stripe_product = Stripe::Product.create({
          name: @product.title,
          description: @product.short_description
        })

        stripe_price = Stripe::Price.create({
          unit_amount: (@product.price * 100).to_i,
          currency: 'usd',
          product: stripe_product.id
        })

        @product.update(
          stripe_product_id: stripe_product.id,
          stripe_price_id: stripe_price.id
        )
      end

      session = Stripe::Checkout::Session.create(
        customer_email: current_user.email,
        payment_method_types: ['card'],
        line_items: [{
          price: @product.stripe_price_id,
          quantity: 1
        }],
        mode: 'payment',
        metadata: {
          product_id: @product.id.to_s
        },
        success_url: success_hdhd_orders_url(product_id: @product.id),
        cancel_url: cancel_hdhd_orders_url
      )

      redirect_to session.url, allow_other_host: true
    end

    def success
      @product = Product.find_by(id: params[:product_id]) if params[:product_id]
    end

    def cancel; end

    layout 'hdhd/application'
  end
end
