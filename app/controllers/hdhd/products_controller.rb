module Hdhd
  class ProductsController < ApplicationController
    before_action :set_product, only: [:show, :edit, :update, :destroy]

    # GET /products
    def index
      @products = Product.all
    end

    # GET /products/:id
    def show; end

    # GET /products/new
    def new
      @product = Product.new
    end

    # POST /products
    def create
      @product = Product.new(product_params)

      if @product.save
        # Create product and price in Stripe
        create_stripe_product(@product)
        redirect_to @product, notice: 'Product was successfully created.'
      else
        render :new
      end
    end

    # GET /products/:id/edit
    def edit; end

    # PATCH/PUT /products/:id
    def update
      if @product.update(product_params)
        # Update the corresponding Stripe product and price
        update_stripe_product(@product)
        redirect_to @product, notice: 'Product was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /products/:id
    def destroy
      if @product.destroy
        # Delete product and price in Stripe
        if @product.stripe_product_id.present?
          Stripe::Product.delete(@product.stripe_product_id)
          Stripe::Price.delete(@product.stripe_price_id)
        end
        redirect_to products_url, notice: 'Product was successfully deleted.'
      else
        render :show
      end
    end

    private

    # Set product for show, edit, update, destroy actions
    def set_product
      @product = Product.find(params[:id])
    end

    # Only allow a list of trusted parameters through
    def product_params
      params.require(:product).permit(:title, :description, :price, :image)
    end

    # Create Stripe product and price
    def create_stripe_product(product)
      stripe_product = Stripe::Product.create({
        name: product.title,
        description: product.description
      })

      stripe_price = Stripe::Price.create({
        unit_amount: (product.price * 100).to_i, # Stripe expects the price in cents
        currency: 'usd',
        product: stripe_product.id
      })

      # Save Stripe product and price IDs to the product record
      product.update(stripe_product_id: stripe_product.id, stripe_price_id: stripe_price.id)
    end

    # Update Stripe product and price
    def update_stripe_product(product)
      if product.stripe_product_id.present?
        # Update the Stripe product
        Stripe::Product.update(
          product.stripe_product_id,
          {
            name: product.title,
            description: product.description
          }
        )

        # Create a new price if the price has changed
        if product.saved_change_to_price?
          # Archive the old price
          Stripe::Price.update(product.stripe_price_id, { active: false })

          # Create a new price
          stripe_price = Stripe::Price.create({
            unit_amount: (product.price * 100).to_i,
            currency: 'usd',
            product: product.stripe_product_id
          })

          # Update the product record with the new price ID
          product.update(stripe_price_id: stripe_price.id)
        end
      else
        # Create new Stripe product and price if they don't exist
        create_stripe_product(product)
      end
    end

    layout 'hdhd/application'
  end
end
