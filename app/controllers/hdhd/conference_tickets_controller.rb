module Hdhd
  class ConferenceTicketsController < ApplicationController
    before_action :authenticate_user!
    
    def new
      @conference = Hdhd::Conference.find_by("EXTRACT(YEAR FROM start_date) = ?", params[:conference_year].to_i)
      
      if current_user.has_ticket_for_year?(@conference.year)
        redirect_to dashboard_path, notice: "You already have a ticket for HDHD #{@conference.year}"
        return
      end
    end
    
    def create
      @conference = Hdhd::Conference.find(params[:conference_id])
      
      if current_user.has_ticket_for_year?(@conference.year)
        redirect_to dashboard_path, notice: "You already have a ticket for HDHD #{@conference.year}"
        return
      end
      
      # Create Stripe checkout session
      session = Stripe::Checkout::Session.create(
        customer_email: current_user.email,
        payment_method_types: ['card'],
        line_items: [{
          price_data: {
            unit_amount: (@conference.ticket_price * 100).to_i,
            currency: 'usd',
            product_data: {
              name: "HDHD #{@conference.year} Conference Ticket"
            }
          },
          quantity: 1
        }],
        mode: 'payment',
        metadata: {
          conference_id: @conference.id.to_s,
          user_id: current_user.id.to_s,
          ticket_type: 'conference'
        },
        success_url: success_hdhd_conference_tickets_url,
        cancel_url: cancel_hdhd_conference_tickets_url
      )
      
      redirect_to session.url, allow_other_host: true
    end
    
    def success
      @conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Time.now.year).first
    end
    
    def cancel
      @conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Time.now.year).first
    end
    
    layout 'hdhd/application'
  end
end
