module Hdhd
  class Stripe<PERSON>ontroller < ApplicationController
    before_action :authenticate_user!
    before_action :set_speaker, except: [:callback]

    def oauth
      # Use a hardcoded redirect URI that matches what's configured in Stripe Dashboard
      redirect_uri = "https://www.highdeserthumandesign.com/hdhd/stripe/callback"

      redirect_to "https://connect.stripe.com/oauth/authorize?response_type=code" \
                  "&client_id=#{ENV.fetch('STRIPE_CLIENT_ID')}" \
                  "&scope=read_write" \
                  "&redirect_uri=#{redirect_uri}" \
                  "&state=#{@speaker.id}",
                  allow_other_host: true
    end

    def callback
      code = params[:code]
      speaker = Hdhd::Speaker.find(params[:state])

      response = Stripe::OAuth.token({
                                       grant_type: 'authorization_code',
                                       code: code
                                     })

      # Save both the user_id and publishable_key
      speaker.update!(
        stripe_user_id: response.stripe_user_id,
        stripe_publishable_key: response.stripe_publishable_key
      )
      redirect_to edit_speaker_path(speaker), notice: 'Successfully connected Stripe account!'
    rescue StandardError => e
      Rails.logger.error "Stripe OAuth Error: #{e.message}"
      redirect_to edit_speaker_path(speaker), alert: 'Failed to connect Stripe account. Please try again.'
    end

    def unlink_confirmation
      render layout: 'hdhd/signed_in'
      @speaker = Hdhd::Speaker.find(params[:speaker_id])
    end

    def unlink
      # Log the speaker and client ID for debugging
      Rails.logger.info "Unlinking Stripe account for speaker: #{@speaker.id} with stripe_user_id: #{@speaker.stripe_user_id}"

      begin
        # In development mode, skip the Stripe API call
        if Rails.env.development?
          Rails.logger.info "Development mode: Skipping Stripe API call"
        else
          Rails.logger.info "Using client_id: #{ENV.fetch('STRIPE_CLIENT_ID')}"
          # Deauthorize the connected account
          Stripe::OAuth.deauthorize(
            client_id: ENV.fetch('STRIPE_CLIENT_ID'),
            stripe_user_id: @speaker.stripe_user_id
          )
        end

        # Update the speaker record
        @speaker.update!(
          stripe_user_id: nil,
          stripe_publishable_key: nil
        )

        redirect_to edit_speaker_path(@speaker), notice: 'Successfully unlinked Stripe account!'
      rescue StandardError => e
        Rails.logger.error "Stripe Unlink Error: #{e.message}"
        Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
        redirect_to edit_speaker_path(@speaker), alert: 'Failed to unlink Stripe account. Please try again.'
      end
    end

    def payment_success
      session_id = params[:session_id]

      # Log the session ID for debugging
      Rails.logger.info "Payment success with session ID: #{session_id}"

      begin
        # First try to retrieve the session directly
        @session = Stripe::Checkout::Session.retrieve(session_id)
        Rails.logger.info "Retrieved session: #{@session.id}"

        # Try to get the payment intent
        if @session.payment_intent
          payment_intent = Stripe::PaymentIntent.retrieve(@session.payment_intent)
          Rails.logger.info "Payment intent account: #{payment_intent.on_behalf_of || payment_intent.account}"
          account_id = payment_intent.on_behalf_of || payment_intent.account
          @speaker = Hdhd::Speaker.find_by(stripe_user_id: account_id)
        elsif @session.metadata&.speaker_id
          # If no payment intent, try to find the speaker from the session metadata
          @speaker = Hdhd::Speaker.find(@session.metadata.speaker_id)
        end
      rescue StandardError => e
        Rails.logger.error "Error retrieving session: #{e.message}"

        # Try to get the speaker from the URL parameters as a fallback
        @speaker = Hdhd::Speaker.find(params[:speaker_id]) if params[:speaker_id]
      end

      unless @speaker
        Rails.logger.error "Could not determine speaker for session: #{session_id}"
        redirect_to root_path, alert: 'Unable to verify payment. Please contact support.'
        return
      end

      # Log success
      Rails.logger.info "Payment successful for speaker: #{@speaker.full_name} (ID: #{@speaker.id})"
    rescue StandardError => e
      Rails.logger.error "Payment Success Error: #{e.message}"
      redirect_to root_path, alert: 'Unable to verify payment. Please contact support.'
    end

    def payment_cancel
      @speaker = Hdhd::Speaker.find(params[:speaker_id])
    end

    def create_checkout_session
      # Skip authentication for this endpoint
      speaker = Hdhd::Speaker.find(params[:speaker_id])

      unless speaker.stripe_connected?
        render json: { error: 'Speaker not connected to Stripe' }, status: :bad_request
        return
      end

      # Get the current year's conference
      conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Date.today.year).first
      unless conference
        render json: { error: 'No conference found for the current year' }, status: :bad_request
        return
      end

      # Calculate platform fee percentage based on tickets already sold
      platform_fee_percentage = speaker.platform_fee_percentage_for_next_ticket(conference.year)

      # Calculate application fee amount if applicable
      application_fee_amount = nil
      if platform_fee_percentage > 0
        # Calculate 50% of the ticket price as the application fee
        application_fee_amount = ((params[:price_amount].to_f * platform_fee_percentage) / 100.0).to_i
      end

      # Log the speaker's Stripe account ID and fee information for debugging
      Rails.logger.info "Creating checkout session with Stripe account: #{speaker.stripe_user_id}"
      Rails.logger.info "Platform fee percentage: #{platform_fee_percentage}%"
      Rails.logger.info "Application fee amount: #{application_fee_amount || 'None'}"

      # Create a checkout session with the speaker's connected account
      session_params = {
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: params[:description] || 'HDHD Conference Ticket'
              },
              unit_amount: params[:price_amount] || 44_730 # Default to $447.30 in cents
            },
            quantity: 1
          }
        ],
        mode: 'payment',
        success_url: hdhd_stripe_payment_success_url(session_id: '{CHECKOUT_SESSION_ID}', speaker_id: speaker.id),
        cancel_url: hdhd_stripe_payment_cancel_url(speaker_id: speaker.id),
        metadata: {
          speaker_id: speaker.id.to_s,
          referrer_speaker_id: speaker.id.to_s,
          conference_id: conference.id.to_s,
          ticket_type: 'conference',
          discount_type: 'friends_and_family',
          platform_fee_percentage: platform_fee_percentage.to_s
        }
      }

      # Add application fee if applicable
      if application_fee_amount && application_fee_amount > 0
        session_params[:payment_intent_data] = {
          application_fee_amount: application_fee_amount
        }
      end

      # Create the session
      session = Stripe::Checkout::Session.create(
        session_params,
        { stripe_account: speaker.stripe_user_id }
      )

      # Log the created session ID for debugging
      Rails.logger.info "Created checkout session with ID: #{session.id}"

      render json: { id: session.id }
    rescue StandardError => e
      Rails.logger.error "Create Checkout Session Error: #{e.message}"
      render json: { error: e.message }, status: :internal_server_error
    end

    private

    def set_speaker
      @speaker = Hdhd::Speaker.find(params[:speaker_id])
      return if @speaker.owned_by?(current_user) || current_user.email == "<EMAIL>"

      redirect_to root_path, alert: 'Unauthorized access'
    end
  end
end
