module Hdhd
  class ScheduledEventsController < ApplicationController
    include Hdhd::ScheduledEventsHelper
    def index
      @conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", params[:conference_year] || Time.current.year).first

      if @conference.nil?
        redirect_to conferences_path, alert: "No conference found for the specified year"
        return
      end

      @scheduled_events = @conference.scheduled_events.includes(:speakers).order(start_date: :asc)

      # Filter by event type if provided
      if params[:event_type].present? && Hdhd::ScheduledEvent::EVENT_TYPES.include?(params[:event_type])
        @scheduled_events = @scheduled_events.where(event_type: params[:event_type])
        @current_filter = params[:event_type]
      end

      # Group events by day for the schedule view
      @events_by_day = @scheduled_events.group_by { |event| event.start_date.to_date }
    end

    def show
      @scheduled_event = Hdhd::ScheduledEvent.includes(:speakers, :conference).find(params[:id])
    end

    layout 'hdhd/signed_in'
  end
end
