module Hdhd
  class SpeakersController < ApplicationController
    before_action :authenticate_user!, only: [:edit, :update, :payment_link, :create_payment_link]
    before_action :set_current_user, only: [:edit, :update]
    before_action :authorize_speaker_edit!, only: [:edit, :update, :payment_link, :create_payment_link]

    def index
      @conference = Hdhd::Conference.find_by!(start_date: DateTime.new(params[:conference_year].to_i))
      @speakers = @conference.speakers
    end

    def show
      @speaker = Hdhd::Speaker.find(params[:id])

      # Get bodygraph data if available
      return unless @speaker.birth_date_utc.present? && @speaker.birth_date_local.present? && @speaker.birth_location.present?

      @bodygraph_data = SpeakerBodygraphService.new(@speaker).call
    end

    def edit
      @speaker = Hdhd::Speaker.find(params[:id])
    end

    def update
      @speaker = Hdhd::Speaker.find(params[:id])

      # Handle profile picture upload
      @speaker.profile_picture.attach(params[:hdhd_speaker][:profile_picture]) if params[:hdhd_speaker][:profile_picture].present? && params[:hdhd_speaker][:profile_picture].is_a?(ActionDispatch::Http::UploadedFile)

      # Handle company logo upload
      @speaker.company_logo.attach(params[:hdhd_speaker][:company_logo]) if params[:hdhd_speaker][:company_logo].present? && params[:hdhd_speaker][:company_logo].is_a?(ActionDispatch::Http::UploadedFile)

      if @speaker.update(speaker_params)
        redirect_to speaker_path(@speaker), notice: "Profile updated successfully."
      else
        render :edit
      end
    end

    def payment_link
      @speaker = Hdhd::Speaker.find(params[:speaker_id])
      # The view will handle displaying the form or a message if not connected
    end

    def create_payment_link
      @speaker = Hdhd::Speaker.find(params[:speaker_id])

      unless @speaker.stripe_connected?
        redirect_to hdhd_speaker_payment_link_path(@speaker), alert: "Please connect your Stripe account first."
        return
      end

      amount = params[:amount].to_f
      description = params[:description].presence || "Session with #{@speaker.full_name}"

      @payment_link = @speaker.create_payment_link(amount, description)

      if @payment_link
        render :payment_link
      else
        redirect_to hdhd_speaker_payment_link_path(@speaker), alert: "Failed to create payment link. Please try again."
      end
    end

    def friends_and_family
      @speaker = Hdhd::Speaker.find(params[:speaker_id])
      @page_title = "#{@speaker.full_name}'s Friends & Family Discount | High Desert Human Design"

      # Ensure the speaker has a password set
      return unless @speaker.friends_and_family_password.blank?

      @speaker.update(friends_and_family_password: @speaker.generate_friends_and_family_password)
    end

    private

    def determine_layout
      if [:edit, :update].include?(action_name.to_sym)
        'hdhd/signed_in_fullscreen'
      else
        'hdhd/application'
      end
    end

    def set_current_user
      @current_user = current_user
    end

    def authorize_speaker_edit!
      @speaker = Hdhd::Speaker.find(params[:id] || params[:speaker_id])
      return if @speaker.owned_by?(current_user) || current_user.email == "<EMAIL>"

      redirect_to dashboard_path, alert: "You are not authorized to edit this speaker profile."
    end

    def speaker_params
      params.require(:hdhd_speaker).permit(
        :full_name, :email, :bio, :profile_picture, :company_logo,
        :birth_date_local, :birth_location, :aura_type, :inner_authority, :profile,
        :friends_and_family_password, :websites, :website_titles
      )
    end

    layout :determine_layout
  end
end
