module Hdhd
  class ConferencesController < ApplicationController
    include Hdhd::ScheduledEventsHelper
    def index
      @conferences = Hdhd::Conference.where("start_date < ?", Date.today)
                                     .order(start_date: :desc)
    end

    def show
      conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", params[:year].to_i).first

      if conference.nil?
        redirect_to conferences_path, alert: "No conference found for the year #{params[:year]}"
      else
        @conference = conference
        # Include scheduled events and speakers for the conference
        @conference = Hdhd::Conference.includes(scheduled_events: :speakers, speakers: {}).find(@conference.id)
      end
    end

    def conference_dashboard
      @conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", Time.now.year).first
      render layout: 'hdhd/signed_in'
    end

    layout 'hdhd/application'
  end
end
