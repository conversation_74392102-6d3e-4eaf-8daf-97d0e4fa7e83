class Users::OmniauthCallbacksController < Devise::OmniauthCallbacksController
  def google_oauth2
    logger.debug "Omniauth callback received: #{request.env['omniauth.auth']}"
    logger.debug "OmniAuth Auth Data: #{request.env['omniauth.auth'].inspect}"
    logger.debug "Request Headers: #{request.headers.to_h}"
    logger.debug "Request Params: #{request.params.inspect}"
    logger.debug "OmniAuth Auth Data: #{request.env['omniauth.auth']}"
    logger.debug "Session Data: #{session.to_hash}"

    @user = User.from_omniauth(request.env['omniauth.auth'])

    if @user.persisted?
      sign_in_and_redirect @user, event: :authentication
      set_flash_message(:notice, :success, kind: 'Google') if is_navigational_format?
    else
      session['devise.google_data'] = request.env['omniauth.auth']
      redirect_to new_user_registration_url
    end
  end
end
