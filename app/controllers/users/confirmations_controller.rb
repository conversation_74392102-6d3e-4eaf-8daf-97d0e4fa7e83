# frozen_string_literal: true

class Users::ConfirmationsController < Devise::ConfirmationsController
  layout 'signposthd/fullscreen'

  # We're using the default Devise methods for new, create, and show
  # Only customizing the paths after actions

  protected

  # The path used after resending confirmation instructions.
  def after_resending_confirmation_instructions_path_for(_resource_name)
    new_user_session_path
  end

  # The path used after confirmation.
  def after_confirmation_path_for(_resource_name, resource)
    sign_in(resource)
    dashboard_path
  end
end
