class Users::SettingsController < ApplicationController
  before_action :authenticate_user!

  def edit
    @user = current_user
  end

  def update
    @user = current_user

    if @user.update(user_params)
      @user.avatar.attach(params[:user][:avatar]) if params[:user][:avatar].present?

      redirect_to edit_users_settings_path, notice: 'Settings updated successfully'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:name, :email, :avatar)
  end

  layout 'hdhd/signed_in'
end
