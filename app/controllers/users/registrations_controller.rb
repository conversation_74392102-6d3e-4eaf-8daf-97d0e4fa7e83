# frozen_string_literal: true

class Users::RegistrationsController < Devise::RegistrationsController
  layout 'signposthd/fullscreen'

  skip_before_action :require_no_authentication, only: [:new]
  before_action :authenticate_user!
  before_action :configure_permitted_parameters

  # before_action :configure_sign_up_params, only: [:create]
  # before_action :configure_account_update_params, only: [:update]

  # GET /resource/sign_up
  def new
    redirect_to dashboard_path, alert: "You are signed in as #{current_user.email}." and return if user_signed_in?

    super
  end

  # POST /resource
  def create
    super do |user|
      # Welcome email is now sent by <PERSON><PERSON> as part of the confirmation process
      # We don't need to send a separate welcome email here anymore
    end
  end

  # The path used after sign up for inactive accounts (those requiring confirmation).
  def after_inactive_sign_up_path_for(_resource)
    new_user_session_path
  end

  # GET /resource/edit
  # def edit
  #   super
  # end

  # PUT /resource
  def update
    # Find the current user
    @user = current_user

    # Attempt to update the user with the submitted parameters
    if @user.update(user_params)
      # Handle successful update
      redirect_to root_path, notice: 'Profile updated successfully.'
    else
      # Handle update failure
      render :edit
    end
  end

  # DELETE /resource
  # def destroy
  #   super
  # end

  # GET /resource/cancel
  # Forces the session data which is usually expired after sign
  # in to be expired now. This is useful if the user wants to
  # cancel oauth signing in/up in the middle of the process,
  # removing all OAuth session data.
  # def cancel
  #   super
  # end

  # protected

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_sign_up_params
  #   devise_parameter_sanitizer.permit(:sign_up, keys: [:attribute])
  # end

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_account_update_params
  #   devise_parameter_sanitizer.permit(:account_update, keys: [:attribute])
  # end

  # The path used after sign up.
  # def after_sign_up_path_for(resource)
  #   super(resource)
  # end

  # The path used after sign up for inactive accounts.
  # def after_inactive_sign_up_path_for(resource)
  #   super(resource)
  # end

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:account_update, keys: [:email])
  end

  def after_sign_up_path_for(_resource)
    dashboard_path
  end

  private

  def user_params
    params.require(:user).permit(:email, :password, :password_confirmation, :other_attributes)
  end
end
