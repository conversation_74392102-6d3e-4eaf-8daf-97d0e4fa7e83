# frozen_string_literal: true

require "google-id-token"

class Users::SessionsController < Devise::SessionsController
  skip_before_action :verify_authenticity_token, only: [:google_login] # Skip CSRF for API endpoint
  layout 'signposthd/fullscreen'

  def after_sign_in_path_for(_resource)
    dashboard_path
  end

  def google_login
    request.env["devise.mapping"] = Devise.mappings[:user] # Explicitly set Devise mapping
    token = params[:token]

    Rails.logger.info "Google login attempt with token: #{token.present? ? 'present' : 'missing'}"

    if token.blank?
      Rails.logger.error "Google token is missing"
      return render json: { success: false, error: "Token is missing" }, status: :bad_request
    end

    begin
      # Use the google-id-token gem to validate the token
      validator = GoogleIDToken::Validator.new
      payload = validator.check(token, ENV.fetch("GOOGLE_CLIENT_ID", nil))

      Rails.logger.info "Google payload received: #{payload ? 'valid' : 'invalid'}"

      if payload.nil?
        Rails.logger.error "Invalid Google token - no payload returned"
        return render json: { success: false, error: "Invalid token" }, status: :unauthorized
      end

      # Log key information from the payload
      Rails.logger.info "Google auth for email: #{payload['email']}, sub: #{payload['sub']}"

      # Find or create the user
      user = User.from_google(
        email: payload["email"],
        name: payload["name"],
        uid: payload["sub"],
        image: payload["picture"]
      )

      if user.nil? || !user.persisted?
        Rails.logger.error "Failed to create or find user from Google data"
        return render json: { success: false, error: "User creation failed" }, status: :unprocessable_entity
      end

      Rails.logger.info "User found/created: ID=#{user.id}, Email=#{user.email}"

      # Sign in the user with Devise
      sign_in(:user, user)

      # Set a session variable to confirm the user is signed in
      session[:user_signed_in_at] = Time.current.to_i
      session[:google_login] = true

      # Determine the redirect URL based on the user's role
      redirect_url = user.admin? ? admin_dashboard_path : dashboard_path

      Rails.logger.info "Google login successful for user ID: #{user.id}"
      render json: {
        success: true,
        user: { id: user.id, email: user.email, name: user.name },
        redirect_url: redirect_url
      }
    rescue GoogleIDToken::ValidationError => e
      Rails.logger.error "Google token validation error: #{e.message}"
      render json: { success: false, error: "Invalid token: #{e.message}" }, status: :unauthorized
    rescue StandardError => e
      Rails.logger.error "Google login error: #{e.class.name} - #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render json: { success: false, error: "Authentication error: #{e.message}" }, status: :internal_server_error
    end
  end

  def google_oauth2
    # Google response contains user info
    auth = request.env['omniauth.auth']

    # Find or create the user based on the Google information
    user = User.find_or_create_by(email: auth.info.email) do |u|
      u.name = auth.info.name
      u.google_uid = auth.uid
      u.image = auth.info.image
    end

    # Log the user in (you can set your own session handling here)
    session[:user_id] = user.id

    # Redirect the user to the homepage or wherever you want
    redirect_to root_path, notice: 'Signed in successfully with Google!'
  end
end
