module Users
  class SpeakerConnectionsController < ApplicationController
    before_action :authenticate_user!

    def new
      # Check if user already has a connected speaker
      if current_user.speaker.present?
        redirect_to edit_users_settings_path, notice: "You already have a connected speaker profile."
        return
      end

      # Try to find a speaker with the same email as the current user
      @speaker = Hdhd::Speaker.find_by(email: current_user.email)

      return unless @speaker.present?

      # If found, associate it with the user
      @speaker.update(user_id: current_user.id)
      redirect_to edit_users_settings_path, notice: "Speaker profile connected successfully."

      # Otherwise, render the form to enter an email address
    end

    def create
      # Check if user already has a connected speaker
      if current_user.speaker.present?
        redirect_to edit_users_settings_path, notice: "You already have a connected speaker profile."
        return
      end

      # Try to find a speaker with the provided email
      @speaker = Hdhd::Speaker.find_by(email: params[:email])

      if @speaker.present?
        # If found, associate it with the user
        @speaker.update(user_id: current_user.id)
        redirect_to edit_users_settings_path, notice: "Speaker profile connected successfully."
      else
        # If not found, redirect back with an error message
        flash[:alert] = "No speaker profile found with that email address. Please contact <PERSON> if you run into any issues connecting your speaker profile."
        redirect_to new_users_speaker_connection_path
      end
    end

    layout 'hdhd/signed_in'
  end
end
