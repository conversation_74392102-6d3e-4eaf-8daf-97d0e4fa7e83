ActiveAdmin.register Donor do
  menu parent: "Santa Fe Human Design Library", priority: 2
  permit_params :name, :email

  index do
    selectable_column
    id_column
    column :name
    column :email
    column :created_at
    actions
  end

  filter :name
  filter :email
  filter :created_at

  form do |f|
    f.inputs do
      f.input :name
      f.input :email
    end
    f.actions
  end

  show do
    attributes_table do
      row :name
      row :email
      row :created_at
      row :updated_at
    end

    panel "Books" do
      table_for donor.books do
        column :title
        column :publication_date
        column :number_of_pages
        column do |book|
          link_to "View", admin_book_path(book)
        end
      end
    end

    panel "CDs" do
      table_for donor.cds do
        column :title
        column :publication_date
        column :audio_length
        column do |cd|
          link_to "View", admin_cd_path(cd)
        end
      end
    end

    panel "Tapes" do
      table_for donor.tapes do
        column :title
        column :publication_date
        column :audio_length
        column do |tape|
          link_to "View", admin_tape_path(tape)
        end
      end
    end
  end
end
