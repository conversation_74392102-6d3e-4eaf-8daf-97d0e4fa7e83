ActiveAdmin.register_page "Center for Human Design Pages" do
  menu parent: "The Center for Human Design", priority: 1

  content title: "Center for Human Design Pages" do
    panel "About" do
      para "This section is for managing content for The Center for Human Design website."
      para "Currently, the site has the following pages:"
      ul do
        li link_to("Home Page", center_for_human_design_home_path, target: "_blank")
        li link_to("About Page", center_for_human_design_about_path, target: "_blank")
        li link_to("History Page", center_for_human_design_history_path, target: "_blank")
        li link_to("Initiatives Page", center_for_human_design_initiatives_path, target: "_blank")
      end
    end

    panel "Content Management" do
      para "To edit the content of these pages, you need to modify the view templates directly."
      para "The templates are located at:"
      ul do
        li "app/views/center_for_human_design/home.html.erb"
        li "app/views/center_for_human_design/about.html.erb"
        li "app/views/center_for_human_design/history.html.erb"
        li "app/views/center_for_human_design/initiatives.html.erb"
      end
    end
  end
end
