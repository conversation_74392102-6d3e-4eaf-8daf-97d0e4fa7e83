# app/admin/products.rb

ActiveAdmin.register Product do
  menu parent: "High Desert Human Design", priority: 4
  permit_params :title, :short_description, :long_description, :price, :image,
                :media_type, :media_description, :highlights, :thumbnail,
                download_links_attributes: [:id, :title, :url, :_destroy]

  filter :title
  filter :short_description
  filter :long_description
  filter :price
  filter :media_type
  filter :media_description
  filter :highlights
  filter :created_at
  filter :updated_at

  # Configure the index page to display products
  index do
    selectable_column
    id_column
    column :title
    column :short_description
    column :long_description
    column :price do |product|
      helpers.format_currency(product.price)
    end
    column :media_type
    column :media_description
    column :highlights
    column :created_at
    actions
  end

  # Configure the form for creating/editing products
  form do |f|
    f.inputs 'Product Details' do
      f.input :title
      f.input :short_description
      f.input :long_description
      f.input :price
      f.input :media_type
      f.input :media_description
      f.input :highlights
      f.input :image, as: :file, hint: (f.object.image&.attached? ? image_tag(f.object.image) : content_tag(:span, 'No image yet'))
      f.input :thumbnail, as: :file, hint: (f.object.thumbnail&.attached? ? image_tag(f.object.thumbnail) : content_tag(:span, 'No thumbnail yet'))
    end

    f.inputs 'Download Links' do
      f.has_many :download_links, allow_destroy: true, heading: false do |link|
        link.input :title
        link.input :url
      end
    end

    f.actions
  end

  # Configure the show page for a product
  show do
    attributes_table do
      row :title
      row :short_description
      row :long_description
      row :price do |product|
        helpers.format_currency(product.price)
      end
      row :media_type
      row :media_description
      row :highlights
      row :image do |product|
        image_tag url_for(product.image) if product.image.attached?
      end
      row :thumbnail do |product|
        image_tag url_for(product.thumbnail) if product.thumbnail.attached?
      end
      row :created_at
    end

    panel 'Download Links' do
      table_for product.download_links do
        column :title
        column :url do |link|
          link_to link.url, link.url, target: '_blank'
        end
      end
    end
  end
end
