ActiveAdmin.register Hdhd::Speaker do
  menu parent: "High Desert Human Design", priority: 1, label: "Speakers"

  permit_params :full_name, :email, :birth_date_utc, :design_date_utc,
                :birth_date_local, :birth_location, :websites, :website_titles,
                :bio, :profile, :aura_type, :inner_authority, :profile_picture,
                :company_logo, :role, :user_id, conference_ids: [],
                                                speaker_year_roles_attributes: [:id, :conference_id, :role, :_destroy]

  filter :role, as: :select, collection: Hdhd::Speaker::ROLES
  filter :full_name
  filter :email
  filter :birth_date_utc
  filter :design_date_utc
  filter :birth_date_local
  filter :birth_location
  filter :websites
  filter :website_titles
  filter :bio
  filter :profile
  filter :aura_type
  filter :inner_authority

  index do
    selectable_column
    id_column
    column :full_name
    column :role do |speaker|
      status_tag speaker.role, class: speaker.role.parameterize
    end
    column :email
    column "Birth Date" do |speaker|
      speaker.birth_date_local&.strftime("%Y-%m-%d %H:%M") if speaker.birth_date_local.present?
    end
    column :birth_location
    column :inner_authority
    column :aura_type
    column "Conferences" do |speaker|
      if speaker.conferences.any?
        ul do
          speaker.conferences.order(start_date: :desc).each do |conference|
            li do
              role = speaker.role_for_year(conference.year)
              span "HDHD #{conference.year}: "
              status_tag role, class: role.parameterize
            end
          end
        end
      end
    end
    column :created_at
    actions
  end

  show do
    attributes_table do
      row :full_name
      row :email
      row :role
      row :websites
      row :website_titles
      row :bio
    end

    panel "Human Design Information" do
      attributes_table_for resource do
        row :birth_date_local
        row :birth_location
        row :birth_date_utc
        row "Design Date" do |speaker|
          if speaker.birth_date_utc.present?
            # Use the helper method directly from the speaker model
            design_date = speaker.calculate_accurate_design_date
            if design_date.present?
              "#{design_date.strftime('%Y-%m-%d %H:%M')} UTC"
            else
              "Could not be calculated"
            end
          else
            "N/A"
          end
        end
        row :profile
        row :inner_authority
        row :aura_type

        # Display bodygraph if birth data is available
        if resource.birth_date_utc.present? && resource.birth_date_local.present? && resource.birth_location.present?
          row :bodygraph do
            div class: "bodygraph-preview" do
              # Create a simple preview of the bodygraph
              div style: "width: 430px; height: 430px; position: relative;" do
                image_tag("bodygraph-bg.svg", style: "position: absolute; left: 0; top: 0; opacity: 0.8; width: 430px;")
                image_tag("bodygraph-blank.svg", style: "position: absolute; left: 0; top: 0; width: 430px;")
              end

              div style: "margin-top: 10px;" do
                text_node "Birth: #{resource.birth_date_local.strftime('%Y-%m-%d %H:%M')} at #{resource.birth_location}"
                br
                if resource.birth_date_utc.present?
                  design_date = resource.calculate_accurate_design_date
                  text_node "Design: #{design_date.strftime('%Y-%m-%d %H:%M')} UTC" if design_date.present?
                end
              end
            end
          end
        end
      end
    end

    panel "Media" do
      attributes_table_for resource do
        row :profile_picture do |speaker|
          if speaker.profile_picture.attached?
            div do
              image_tag(url_for(speaker.profile_picture.variant(resize_to_limit: [200, 200])))
            end
          else
            "No profile picture uploaded"
          end
        end

        row :company_logo do |speaker|
          if speaker.company_logo.attached?
            div do
              image_tag(url_for(speaker.company_logo.variant(resize_to_limit: [200, 200])))
            end
          else
            "No company logo uploaded"
          end
        end
      end
    end

    panel "Conferences" do
      attributes_table_for resource do
        row :conferences do |speaker|
          speaker.conferences.map { |c| "HDHD #{c.start_date.year}" }.join(", ")
        end

        # Display roles for each conference year
        if resource.conferences.any?
          row "Roles by Year" do
            table_for resource.conferences.order(start_date: :desc) do
              column "Year" do |conference|
                "HDHD #{conference.year}"
              end
              column "Role" do |conference|
                role = resource.role_for_year(conference.year)
                status_tag role, class: role.parameterize
              end
            end
          end
        end
      end
    end

    panel "System Information" do
      attributes_table_for resource do
        row :id
        row :user
        row :created_at
        row :updated_at
      end
    end

    panel "Actions" do
      div class: "action_items" do
        span class: "action_item" do
          link_to "New Speaker", new_admin_hdhd_speaker_path, class: "view_link"
        end

        span class: "action_item" do
          link_to "View on Website", speaker_path(resource), class: "view_link", target: "_blank"
        end
      end
    end
  end

  form multipart: true do |f|
    tabs do
      tab "Basic Information" do
        f.inputs "Speaker Details" do
          f.input :full_name
          f.input :role, as: :select, collection: Hdhd::Speaker::ROLES
          f.input :email
          f.input :websites, hint: "Comma-separated list of websites"
          f.input :website_titles, hint: "Comma-separated list of website titles (should match the number of websites)"
          f.input :bio, input_html: { rows: 10 } # Make bio field larger
        end
      end

      tab "Human Design" do
        f.inputs "Birth Information" do
          f.input :birth_date_local, as: :datetime_picker, hint: "Local date and time of birth"
          f.input :birth_location, hint: "Format: City, Country (e.g., 'Santa Fe, USA')"

          # Read-only fields for calculated values
          f.input :birth_date_utc, input_html: { disabled: true }, hint: "Automatically calculated from local birth date" if f.object.birth_date_utc.present?

          f.input :design_date_utc, input_html: { disabled: true }, hint: "Automatically calculated from birth date" if f.object.design_date_utc.present?
        end

        f.inputs "Human Design Attributes" do
          f.input :profile, hint: "Format: X/X (e.g., '1/3' or '5/1')"
          f.input :inner_authority, as: :select, collection: ["Sacral", "Emotional", "Splenic", "Ego", "Self Projected", "Outer Authority", "Lunar"], include_blank: true
          f.input :aura_type, as: :select, collection: ["Generator", "Manifesting Generator", "Manifestor", "Projector", "Reflector"], include_blank: true
        end
      end

      tab "Media" do
        f.inputs "Images" do
          f.input :profile_picture, as: :file,
                                    hint: if f.object.profile_picture.attached?
                                            f.template.image_tag(f.object.profile_picture.variant(resize_to_limit: [100, 100]))
                                          else
                                            "No image uploaded yet"
                                          end

          f.input :company_logo, as: :file,
                                 hint: if f.object.company_logo.attached?
                                         f.template.image_tag(f.object.company_logo.variant(resize_to_limit: [100, 100]))
                                       else
                                         "No company logo uploaded yet"
                                       end
        end
      end

      tab "Conferences" do
        f.inputs "Conference Participation" do
          f.input :conferences, as: :select,
                                collection: Hdhd::Conference.all.collect { |conference|
                                  ["HDHD #{conference.start_date.year}", conference.id]
                                },
                                input_html: { multiple: true }
        end

        f.inputs "Roles for Specific Years" do
          # Only show this section if the speaker has conferences
          if f.object.persisted? && f.object.conferences.any?
            f.object.conferences.each do |conference|
              # Find or build a year role for this conference
              year_role = f.object.speaker_year_roles.find_by(conference_id: conference.id) ||
                          f.object.speaker_year_roles.build(conference_id: conference.id, role: f.object.role)

              panel "Role for HDHD #{conference.year}" do
                f.fields_for :speaker_year_roles, year_role do |yr_form|
                  yr_form.input :conference_id, as: :hidden, input_html: { value: conference.id }
                  yr_form.input :role, as: :select, collection: Hdhd::Speaker::ROLES,
                                       label: "Role for #{conference.year}",
                                       hint: "Default role is '#{f.object.role}' if none is specified"
                end
              end
            end
          else
            para "Please select conferences first and save to set specific roles for each year."
          end
        end
      end

      tab "System" do
        f.inputs "System Information" do
          f.input :user, as: :select,
                         collection: User.all.map { |u| [u.email, u.id] },
                         include_blank: true,
                         hint: "User account that owns this speaker profile"
        end
      end
    end

    f.actions
  end

  controller do
    def update
      if params[:hdhd_speaker][:profile_picture].present?
        resource.profile_picture.purge # Remove existing attachment if any
        resource.profile_picture.attach(params[:hdhd_speaker][:profile_picture])
      end

      if params[:hdhd_speaker][:company_logo].present?
        resource.company_logo.purge # Remove existing attachment if any
        resource.company_logo.attach(params[:hdhd_speaker][:company_logo])
      end

      # If birth date or location changed, make sure to recalculate birth_date_utc and design_date_utc
      if params[:hdhd_speaker][:birth_date_local].present? || params[:hdhd_speaker][:birth_location].present?
        # The model callbacks will handle the calculations
      end

      # Store the previous conference IDs before update
      previous_conference_ids = resource.conference_ids.dup

      result = super

      # After update, check if conferences were added or removed
      if result && params[:hdhd_speaker][:conference_ids].present?
        current_conference_ids = resource.reload.conference_ids

        # For newly added conferences, create default year roles if they don't exist
        (current_conference_ids - previous_conference_ids).each do |conf_id|
          Hdhd::Conference.find(conf_id)
          resource.speaker_year_roles.create(conference_id: conf_id, role: resource.role) unless resource.speaker_year_roles.exists?(conference_id: conf_id)
        end

        # For removed conferences, remove the corresponding year roles
        (previous_conference_ids - current_conference_ids).each do |conf_id|
          resource.speaker_year_roles.where(conference_id: conf_id).destroy_all
        end
      end

      result
    end

    def show
      # Calculate bodygraph data if birth information is available
      @bodygraph_data = SpeakerBodygraphService.new(resource).call if resource.birth_date_utc.present? && resource.birth_date_local.present? && resource.birth_location.present?

      super
    end
  end
end
