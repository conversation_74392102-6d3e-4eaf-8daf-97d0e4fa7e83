ActiveAdmin.register User do
  filter :email
  filter :name
  filter :created_at
  filter :updated_at
  filter :id

  # Allow actions like edit, show, etc.
  menu parent: "Users", priority: 1, label: "Users"

  permit_params :email, :name, :role, :speaker_id

  # You can customize the index page, form, and other actions here
  index do
    selectable_column
    id_column
    column :email
    column :name
    column :created_at
    actions
  end

  form do |f|
    f.inputs "User Details" do
      f.input :email
      f.input :name
      # Check if the user has an associated speaker
      f.input :speaker, as: :select,
                        collection: Hdhd::Speaker.where(user_id: f.object.id).pluck(:full_name, :id),
                        include_blank: true,
                        selected: f.object.speaker&.id # Preselect the associated speaker if it exists
    end
    f.actions
  end

  controller do
    def update
      # Set the speaker_id when updating the user
      if params[:user][:speaker_id].present?
        speaker = Hdhd::Speaker.find_or_initialize_by(id: params[:user][:speaker_id])
        speaker.user_id = resource.id
        speaker.save
      end

      super
    end
  end
end
