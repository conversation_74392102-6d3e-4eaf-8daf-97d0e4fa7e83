ActiveAdmin.register Hdhd::Conference do
  menu parent: "High Desert Human Design", priority: 2, label: "Conferences"
  permit_params :start_date, :description, :product_id

  filter :start_date
  filter :description
  filter :product

  controller do
    def find_resource
      year = params[:id].to_i
      Rails.logger.debug "Year parameter: #{year}"
      start_date_range = Date.new(year).beginning_of_year..Date.new(year).end_of_year
      conference = Hdhd::Conference.find_by(start_date: start_date_range)
      Rails.logger.debug "Finding conference for year: #{year}, found: #{conference.inspect}"
      conference
    end

    def resource_path(resource)
      admin_hdhd_conference_path(resource.year)
    end

    def edit_resource_path(resource)
      edit_admin_hdhd_conference_path(resource.year)
    end

    def update_resource_path(resource)
      admin_hdhd_conference_path(resource.year)
    end

    def destroy_resource_path(resource)
      admin_hdhd_conference_path(resource.year)
    end
  end

  form do |f|
    f.inputs do
      f.input :start_date, as: :date_picker
      f.input :description
      f.input :product, collection: Product.all.map { |p| [p.title, p.id] }
    end
    f.actions
  end

  index do
    selectable_column
    id_column
    column :start_date
    column :description
    column :product
    actions
  end

  show do
    attributes_table do
      row :start_date
      row :description
      row :product
      row :created_at
      row :updated_at
    end
  end
end
