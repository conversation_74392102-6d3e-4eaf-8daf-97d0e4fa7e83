ActiveAdmin.register Video do
  menu parent: "Santa Fe Human Design Library", priority: 8
  permit_params :title, :description, :publication_date, :acquisition_date,
                :disposal_date, :disposal_reason, :physical_location, :audio_length,
                :view_url, :download_url, :cover, author_ids: []

  index do
    selectable_column
    id_column
    column :title
    column :publication_date
    column :audio_length
    column :created_at
    actions
  end

  filter :title
  filter :authors
  filter :publication_date
  filter :acquisition_date
  filter :audio_length
  filter :created_at

  form do |f|
    f.inputs do
      f.input :title
      f.input :description, as: :text
      f.input :authors, as: :select, collection: Author.all, input_html: { multiple: true }
      f.input :publication_date, as: :datepicker
      f.input :acquisition_date, as: :datepicker
      f.input :disposal_date, as: :datepicker
      f.input :disposal_reason
      f.input :physical_location
      f.input :audio_length
      f.input :view_url
      f.input :download_url
      f.input :cover, as: :file
    end
    f.actions
  end

  show do
    attributes_table do
      row :title
      row :description
      row :authors do |video|
        video.authors.map do |author|
          link_to author.name, admin_author_path(author)
        end.join(', ').html_safe
      end
      row :publication_date
      row :acquisition_date
      row :disposal_date
      row :disposal_reason
      row :physical_location
      row :audio_length
      row :view_url do |video|
        link_to video.view_url, video.view_url, target: '_blank' if video.view_url.present?
      end
      row :download_url do |video|
        link_to video.download_url, video.download_url, target: '_blank' if video.download_url.present?
      end
      row :cover do |video|
        image_tag url_for(video.cover), style: 'max-width: 300px; max-height: 300px' if video.cover.attached?
      end
      row :created_at
      row :updated_at
    end
  end
end
