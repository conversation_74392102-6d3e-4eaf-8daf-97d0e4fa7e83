ActiveAdmin.register Book do
  menu parent: "Santa Fe Human Design Library", priority: 3
  permit_params :title, :description, :publication_date, :acquisition_date,
                :disposal_date, :disposal_reason, :physical_location, :number_of_pages,
                :donor_id, :cover, author_ids: []

  index do
    selectable_column
    id_column
    column :title
    column :publication_date
    column :number_of_pages
    column :donor
    column :created_at
    actions
  end

  filter :title
  filter :authors
  filter :donor
  filter :publication_date
  filter :acquisition_date
  filter :number_of_pages
  filter :created_at

  form do |f|
    f.inputs do
      f.input :title
      f.input :description, as: :text
      f.input :authors, as: :select, collection: Author.all, input_html: { multiple: true }
      f.input :publication_date, as: :datepicker
      f.input :acquisition_date, as: :datepicker
      f.input :disposal_date, as: :datepicker
      f.input :disposal_reason
      f.input :physical_location
      f.input :number_of_pages
      f.input :donor, as: :select, collection: Donor.all
      f.input :cover, as: :file
    end
    f.actions
  end

  show do
    attributes_table do
      row :title
      row :description
      row :authors do |book|
        book.authors.map do |author|
          link_to author.name, admin_author_path(author)
        end.join(', ').html_safe
      end
      row :publication_date
      row :acquisition_date
      row :disposal_date
      row :disposal_reason
      row :physical_location
      row :number_of_pages
      row :donor
      row :cover do |book|
        image_tag url_for(book.cover), style: 'max-width: 300px; max-height: 300px' if book.cover.attached?
      end
      row :created_at
      row :updated_at
    end
  end
end
