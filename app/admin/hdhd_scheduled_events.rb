ActiveAdmin.register Hdhd::ScheduledEvent do
  menu parent: "High Desert Human Design", priority: 3, label: "Scheduled Events"

  permit_params :title, :description, :start_date, :duration_minutes,
                :event_type, :conference_id, :cover_image, speaker_ids: []

  filter :title
  filter :event_type, as: :select, collection: Hdhd::ScheduledEvent::EVENT_TYPES
  filter :conference
  filter :start_date
  filter :speakers

  index do
    selectable_column
    id_column
    column :title
    column :event_type
    column :conference do |event|
      "HDHD #{event.conference.year}"
    end
    column :start_date do |event|
      event.start_date.strftime("%Y-%m-%d %H:%M")
    end
    column :duration do |event|
      "#{event.duration_hours} hours"
    end
    column "Speakers" do |event|
      event.speakers.map(&:full_name).join(", ")
    end
    actions
  end

  show do
    attributes_table do
      row :title
      row :description
      row :event_type
      row :conference do |event|
        "HDHD #{event.conference.year}"
      end
      row :start_date do |event|
        event.start_date.strftime("%Y-%m-%d %H:%M")
      end
      row :end_date do |event|
        event.end_date.strftime("%Y-%m-%d %H:%M")
      end
      row :duration do |event|
        "#{event.duration_hours} hours (#{event.duration_minutes} minutes)"
      end
      row :speakers do |event|
        event.speakers.map(&:full_name).join(", ")
      end
      row :cover_image do |event|
        if event.cover_image.attached?
          div do
            image_tag(url_for(event.cover_image.variant(resize_to_limit: [300, 300])))
          end
        else
          "No cover image uploaded"
        end
      end
      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs do
      f.input :title
      f.input :description, as: :text
      f.input :event_type, as: :select, collection: Hdhd::ScheduledEvent::EVENT_TYPES
      f.input :conference, collection: Hdhd::Conference.all.map { |c| ["HDHD #{c.year}", c.id] }
      f.input :start_date, as: :datetime_picker
      f.input :duration_minutes, label: "Duration (minutes)"
      f.input :speakers, as: :select, collection: Hdhd::Speaker.all.map { |s| [s.full_name, s.id] },
                         input_html: { multiple: true }
      f.input :cover_image, as: :file,
                            hint: if f.object.cover_image.attached?
                                    image_tag(f.object.cover_image.variant(resize_to_limit: [200, 200]))
                                  else
                                    "No cover image uploaded yet"
                                  end
    end
    f.actions
  end
end
