# app/admin/streaming_video.rb
ActiveAdmin.register StreamingVideo do
  menu parent: "Signpost", priority: 4
  permit_params :title, :is_4k, :runtime_minutes, :url, :short_description, :year, :cover, :cover_small
  filter :title
  filter :year
  filter :is_4k
  filter :runtime_minutes
  filter :url
  filter :short_description
  filter :created_at
  filter :updated_at

  # Index view
  index do
    selectable_column
    column :title
    column :year
    column :is_4k
    column :runtime_minutes
    column :url
    column :short_description
    column :created_at
    column :updated_at
    column :cover do |video|
      if video.cover.attached?
        image_tag url_for(video.cover)
      else
        'No cover uploaded'
      end
    end
    column :cover_small do |video|
      if video.cover_small.attached?
        image_tag url_for(video.cover_small)
      else
        'No small cover uploaded'
      end
    end
    actions
  end

  # Show view
  show do
    attributes_table do
      row :title
      row :is_4k
      row :runtime_minutes
      row :url
      row :short_description
      row :year
      row :created_at
      row :updated_at
      row :cover do |video|
        image_tag url_for(video.cover) if video.cover.attached?
      end
      row :cover_small do |video|
        image_tag url_for(video.cover_small) if video.cover_small.attached?
      end
    end
  end

  # Form view
  form do |f|
    f.inputs 'Streaming Video Details' do
      f.input :title
      f.input :is_4k
      f.input :runtime_minutes
      f.input :url
      f.input :short_description
      f.input :year
      f.input :cover, as: :file, hint: f.object.cover.attached? ? image_tag(f.object.cover) : 'No cover uploaded yet'
      f.input :cover_small, as: :file, hint: f.object.cover_small.attached? ? image_tag(f.object.cover_small) : 'No small cover uploaded yet'
    end
    f.actions
  end
end
