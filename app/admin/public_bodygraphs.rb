ActiveAdmin.register PublicBodygraph do
  menu parent: "Signpost", priority: 2, label: "Public Bodygraphs"

  permit_params :name, :birth_name, :birth_date_local, :birth_date_utc, :design_date_utc,
                :birth_country, :birth_city, :birth_data_source, :birth_data_source_notes,
                :birth_data_collector, :rodden_rating, :gender, :aura_type, :inner_authority,
                :definition, :profile, :incarnation_cross, :determination, :environment,
                :view, :motivation, :cognition, :sense, :variable, :personality_activations,
                :design_activations, :head_defined, :ajna_defined, :throat_defined,
                :spleen_defined, :solar_plexus_defined, :g_center_defined, :sacral_defined,
                :root_defined, :ego_defined, :personality_nodes_tone, :design_nodes_tone,
                :timezone, :latitude, :longitude, :description, :notable_for, :famous,
                :historical_event, :profession, :portrait

  filter :name
  filter :aura_type
  filter :inner_authority
  filter :profile
  filter :famous
  filter :historical_event
  filter :profession
  filter :created_at

  index do
    selectable_column
    id_column
    column :name
    column :aura_type
    column :inner_authority
    column :profile
    column :notable_for
    column :famous
    column :historical_event
    column :profession
    column :created_at
    actions
  end

  show do
    attributes_table do
      row :name
      row :birth_name
      row :birth_date_local
      row :birth_date_utc
      row :design_date_utc
      row :birth_country
      row :birth_city
      row :birth_data_source
      row :birth_data_source_notes
      row :birth_data_collector
      row :rodden_rating
      row :gender
      row :aura_type
      row :inner_authority
      row :definition
      row :profile
      row :incarnation_cross
      row :determination
      row :environment
      row :view
      row :motivation
      row :cognition
      row :sense
      row :variable
      row :notable_for
      row :famous
      row :historical_event
      row :profession
      row :description
      row :portrait do |bodygraph|
        image_tag url_for(bodygraph.portrait), style: 'max-width: 300px; max-height: 300px' if bodygraph.portrait.attached?
      end
      row :created_at
      row :updated_at
    end

    panel "Comments" do
      table_for resource.comments do
        column :id
        column :content
        column :created_at
        column do |comment|
          link_to "View", admin_public_bodygraph_comment_path(comment)
        end
      end
    end
  end

  form do |f|
    f.inputs "Basic Information" do
      f.input :name
      f.input :birth_name
      f.input :birth_date_local, as: :datetime_picker
      f.input :birth_country
      f.input :birth_city
      f.input :birth_data_source
      f.input :birth_data_source_notes
      f.input :birth_data_collector
      f.input :rodden_rating
      f.input :gender
      f.input :notable_for
      f.input :profession
      f.input :famous
      f.input :historical_event
      f.input :description, as: :text
      f.input :portrait, as: :file
    end

    f.inputs "Human Design Information" do
      f.input :aura_type, as: :select, collection: ["Generator", "Manifesting Generator", "Manifestor", "Projector", "Reflector"]
      f.input :inner_authority, as: :select, collection: ["Sacral", "Emotional", "Splenic", "Ego", "Self Projected", "Outer Authority", "Lunar"]
      f.input :definition
      f.input :profile
      f.input :incarnation_cross
      f.input :determination
      f.input :environment
      f.input :view
      f.input :motivation
      f.input :cognition
      f.input :sense
      f.input :variable
    end

    f.actions
  end
end
