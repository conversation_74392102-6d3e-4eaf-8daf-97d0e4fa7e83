ActiveAdmin.register PublicBodygraphComment do
  menu parent: "Signpost", priority: 3, label: "Public Bodygraph Comments"

  permit_params :content, :public_bodygraph_id, :user_id

  filter :public_bodygraph
  filter :user
  filter :content
  filter :created_at

  index do
    selectable_column
    id_column
    column :public_bodygraph
    column :user
    column :content
    column :created_at
    actions
  end

  show do
    attributes_table do
      row :public_bodygraph
      row :user
      row :content
      row :created_at
      row :updated_at
    end
  end

  form do |f|
    f.inputs do
      f.input :public_bodygraph
      f.input :user
      f.input :content
    end
    f.actions
  end
end
