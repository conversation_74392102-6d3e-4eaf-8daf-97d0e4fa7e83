module Ticket<PERSON>reator
  # Create a ticket for a specific email
  def create_ticket_for_email_action(email, status, conference_id)
    # Validate inputs
    if email.blank? || status.blank? || conference_id.blank?
      return { error: "Email, status, and conference are required." }
    end
    
    # Find the conference
    conference = Hdhd::Conference.find_by(id: conference_id)
    unless conference
      return { error: "Conference not found." }
    end
    
    # Find or create the user
    user = User.find_by(email: email)
    
    if user.nil?
      # Create a new user
      user = create_user_for_ticket(email)
      user_created = true
    else
      user_created = false
    end
    
    # Check if the user already has a ticket for this conference
    if user.has_ticket_for_year?(conference.year)
      return { error: "User #{email} already has a ticket for HDHD #{conference.year}." }
    end
    
    # Create the ticket
    ticket = ConferenceTicket.create!(
      user: user,
      conference: conference,
      price: conference.ticket_price,
      status: status,
      discount_type: status == 'complimentary' ? 'admin' : 'regular',
      stripe_session_id: nil,
      stripe_payment_intent_id: nil
    )
    
    # Create notification for the user
    Notification.create!(
      user: user,
      title: "Conference Ticket Created",
      message: status == 'complimentary' ? 
        "A complimentary ticket for HDHD #{conference.year} has been created for you." :
        "A ticket for HDHD #{conference.year} has been created for you.",
      action_url: "/dashboard"
    )
    
    # Send confirmation email
    Hdhd::ConferenceTicketMailer.with(
      user: user,
      conference: conference,
      ticket: ticket
    ).confirmation_email.deliver_later
    
    # Return success message
    message = "Ticket created for #{email} (#{status})."
    message += " New user account was created." if user_created
    
    { message: message }
  end
  
  # Helper method to create a user for a ticket
  def create_user_for_ticket(email)
    # Generate a random password
    password = SecureRandom.hex(10)
    
    # Create the user
    user = User.new(
      email: email,
      password: password,
      password_confirmation: password
    )
    
    # Skip confirmation
    user.skip_confirmation!
    user.save!
    
    # Send welcome email with password
    AdminMailer.with(
      user: user,
      password: password
    ).new_user_from_ticket_email.deliver_later
    
    user
  end
end
