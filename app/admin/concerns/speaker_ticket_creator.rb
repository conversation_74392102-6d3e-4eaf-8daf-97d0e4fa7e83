module SpeakerTicketCreator
  # Helper method to create tickets for speakers
  def create_tickets_for_current_year_speakers
    # Find the current year's conference
    current_year = Date.today.year
    conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", current_year).first

    return { error: "No conference found for the current year (#{current_year})." } unless conference

    # Find all speakers for the current year's conference who have associated users
    speakers_with_users = conference.speakers.where.not(user_id: nil)

    # Initialize counters
    tickets_created = 0
    speakers_with_tickets = 0

    # Create tickets for speakers who don't already have tickets
    speakers_with_users.each do |speaker|
      user = speaker.user

      # Skip if the user already has a ticket for this conference
      if user.has_ticket_for_year?(conference.year)
        speakers_with_tickets += 1
        next
      end

      create_speaker_ticket(user, conference)
      tickets_created += 1
    end

    # Calculate speakers without users
    speakers_without_users = conference.speakers.where(user_id: nil).count

    # Return a summary message
    {
      message: "Created #{tickets_created} tickets for speakers. #{speakers_with_tickets} speakers already had tickets. #{speakers_without_users} speakers don't have user accounts."
    }
  end

  # Helper method to create a single ticket
  def create_speaker_ticket(user, conference)
    # Create a new ticket for the user
    ConferenceTicket.create!(
      user: user,
      conference: conference,
      price: conference.ticket_price,
      status: 'complimentary',
      discount_type: 'speaker',
      stripe_session_id: nil,
      stripe_payment_intent_id: nil
    )

    # Create notification for the user
    Notification.create!(
      user: user,
      title: "Conference Ticket Created",
      message: "A complimentary ticket for HDHD #{conference.year} has been created for you as a speaker.",
      action_url: "/dashboard"
    )
  end
end
