ActiveAdmin.register Bodygraph do
  menu parent: "Signpost", priority: 1
  
  permit_params :name, :birth_date_local, :birth_date_utc, :design_date_utc, 
                :birth_country, :birth_city, :aura_type, :inner_authority, 
                :definition, :profile, :incarnation_cross, :determination, 
                :environment, :view, :motivation, :cognition, :sense, :variable, 
                :personality_activations, :design_activations, :head_defined, 
                :ajna_defined, :throat_defined, :spleen_defined, :solar_plexus_defined, 
                :g_center_defined, :sacral_defined, :root_defined, :ego_defined, 
                :personality_nodes_tone, :design_nodes_tone, :timezone, :user_id, 
                :all_activated_gates
  
  filter :name
  filter :user
  filter :aura_type
  filter :inner_authority
  filter :profile
  filter :created_at
  
  index do
    selectable_column
    id_column
    column :name
    column :user
    column :aura_type
    column :inner_authority
    column :profile
    column :created_at
    actions
  end
  
  show do
    attributes_table do
      row :name
      row :user
      row :birth_date_local
      row :birth_date_utc
      row :design_date_utc
      row :birth_country
      row :birth_city
      row :aura_type
      row :inner_authority
      row :definition
      row :profile
      row :incarnation_cross
      row :determination
      row :environment
      row :view
      row :motivation
      row :cognition
      row :sense
      row :variable
      row :created_at
      row :updated_at
    end
  end
  
  form do |f|
    f.inputs "Basic Information" do
      f.input :name
      f.input :user
      f.input :birth_date_local, as: :datetime_picker
      f.input :birth_country
      f.input :birth_city
    end
    
    f.inputs "Human Design Information" do
      f.input :aura_type, as: :select, collection: ["Generator", "Manifesting Generator", "Manifestor", "Projector", "Reflector"]
      f.input :inner_authority, as: :select, collection: ["Sacral", "Emotional", "Splenic", "Ego", "Self Projected", "Outer Authority", "Lunar"]
      f.input :definition
      f.input :profile
      f.input :incarnation_cross
      f.input :determination
      f.input :environment
      f.input :view
      f.input :motivation
      f.input :cognition
      f.input :sense
      f.input :variable
    end
    
    f.actions
  end
end
