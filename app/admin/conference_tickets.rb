ActiveAdmin.register ConferenceTicket do
  menu parent: "High Desert Human Design", priority: 5, label: "Conference Tickets"

  permit_params :user_id, :conference_id, :price, :status, :referrer_speaker_id,
                :discount_type, :platform_fee_percentage, :stripe_session_id, :stripe_payment_intent_id

  # Action item to create tickets for all speakers
  action_item :create_speaker_tickets, only: :index do
    link_to 'Create tickets for all speakers for the current year', create_speaker_tickets_admin_conference_tickets_path,
            method: :post,
            data: { confirm: 'This will create tickets for all speakers of the current year who have user accounts. Continue?' }
  end

  # Filters
  filter :user, collection: proc { User.all.map { |u| [u.email, u.id] } }
  filter :conference, collection: proc { Hdhd::Conference.all.map { |c| ["HDHD #{c.year}", c.id] } }
  filter :referrer_speaker, collection: proc { Hdhd::Speaker.all.map { |s| [s.full_name, s.id] } }
  filter :price
  filter :status
  filter :discount_type
  filter :created_at

  # Index page
  index do
    selectable_column
    id_column
    column :user do |ticket|
      link_to ticket.user.email, admin_user_path(ticket.user)
    end
    column :conference do |ticket|
      link_to "HDHD #{ticket.conference.year}", admin_hdhd_conference_path(ticket.conference.year)
    end
    column :price do |ticket|
      number_to_currency(ticket.price)
    end
    column :status
    column :discount_type
    column :referrer_speaker do |ticket|
      link_to ticket.referrer_speaker.full_name, admin_hdhd_speaker_path(ticket.referrer_speaker) if ticket.referrer_speaker
    end
    column :platform_fee_percentage do |ticket|
      if ticket.platform_fee_percentage.present?
        "#{ticket.platform_fee_percentage}%"
      else
        "None"
      end
    end
    column :created_at
    actions
  end

  # Custom panels for the index page
  sidebar "Speaker Tickets", only: :index do
    para "Use the 'Create tickets for all speakers for the current year' button above to automatically create tickets for speakers."
    para "Note: This will only create tickets for speakers who have a user attached. If the speaker already has a ticket, it will not create extra tickets unnecessarily."
  end

  sidebar "Create Ticket for Email", only: :index do
    para "Create a ticket for a specific email address. If the user doesn't exist, a new account will be created."

    active_admin_form_for :create_ticket_for_email, url: create_ticket_for_email_admin_conference_tickets_path, method: :post do |f|
      f.inputs do
        f.input :email, as: :string, label: "Email Address", required: true, input_html: { name: "email" }
        f.input :status, as: :select, collection: ["purchased", "complimentary"], label: "Ticket Status", required: true, input_html: { name: "status" }
        f.input :conference_id, as: :select, collection: Hdhd::Conference.all.map { |c| ["HDHD #{c.year}", c.id] }, label: "Conference", required: true, input_html: { name: "conference_id" }
      end
      f.actions do
        f.action :submit, label: "Create Ticket"
      end
    end
  end

  # Show page
  show do
    attributes_table do
      row :id
      row :user do |ticket|
        link_to ticket.user.email, admin_user_path(ticket.user)
      end
      row :conference do |ticket|
        link_to "HDHD #{ticket.conference.year}", admin_hdhd_conference_path(ticket.conference.year)
      end
      row :price do |ticket|
        number_to_currency(ticket.price)
      end
      row :status
      row :discount_type
      row :referrer_speaker do |ticket|
        link_to ticket.referrer_speaker.full_name, admin_hdhd_speaker_path(ticket.referrer_speaker) if ticket.referrer_speaker
      end
      row :platform_fee_percentage do |ticket|
        if ticket.platform_fee_percentage.present?
          "#{ticket.platform_fee_percentage}%"
        else
          "None"
        end
      end
      row :stripe_session_id
      row :stripe_payment_intent_id
      row :created_at
      row :updated_at
    end
  end

  # Form for creating/editing
  form do |f|
    f.inputs "Conference Ticket Details" do
      f.input :user, as: :select, collection: User.all.map { |u| [u.email, u.id] }
      f.input :conference, as: :select, collection: Hdhd::Conference.all.map { |c| ["HDHD #{c.year}", c.id] }
      f.input :price
      f.input :status, as: :select, collection: ["purchased", "complimentary", "refunded", "cancelled"]
      f.input :discount_type, as: :select, collection: ["regular", "friends_and_family", "early_bird"]
      f.input :referrer_speaker, as: :select, collection: Hdhd::Speaker.all.map { |s| [s.full_name, s.id] }
      f.input :platform_fee_percentage
      f.input :stripe_session_id
      f.input :stripe_payment_intent_id
    end
    f.actions
  end

  # Custom action to send confirmation email
  action_item :send_confirmation, only: :show do
    link_to 'Send Confirmation Email', send_confirmation_admin_conference_ticket_path(resource), method: :post
  end

  member_action :send_confirmation, method: :post do
    ticket = ConferenceTicket.find(params[:id])
    Hdhd::ConferenceTicketMailer.with(
      user: ticket.user,
      conference: ticket.conference,
      ticket: ticket
    ).confirmation_email.deliver_later

    redirect_to admin_conference_ticket_path(ticket), notice: "Confirmation email sent to #{ticket.user.email}"
  end

  # Collection action to create tickets for all speakers
  collection_action :create_speaker_tickets, method: :post do
    result = create_tickets_for_current_year_speakers
    redirect_to admin_conference_tickets_path, notice: result[:message], alert: result[:error]
  end

  # Collection action to create a ticket for a specific email
  collection_action :create_ticket_for_email, method: :post do
    result = create_ticket_for_email_action(params[:email], params[:status], params[:conference_id])
    if result[:error]
      redirect_to admin_conference_tickets_path, alert: result[:error]
    else
      redirect_to admin_conference_tickets_path, notice: result[:message]
    end
  end

  controller do
    include SpeakerTicketCreator
    include TicketCreator
  end
end
