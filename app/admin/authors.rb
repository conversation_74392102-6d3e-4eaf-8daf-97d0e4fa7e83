ActiveAdmin.register Author do
  menu parent: "Santa Fe Human Design Library", priority: 1
  permit_params :name, :email, :biography, :profile_picture

  index do
    selectable_column
    id_column
    column :name
    column :email
    column :created_at
    actions
  end

  filter :name
  filter :email
  filter :created_at

  form do |f|
    f.inputs do
      f.input :name
      f.input :email
      f.input :biography, as: :text
      f.input :profile_picture, as: :file
    end
    f.actions
  end

  show do
    attributes_table do
      row :name
      row :email
      row :biography
      row :profile_picture do |author|
        image_tag url_for(author.profile_picture), style: 'max-width: 300px; max-height: 300px' if author.profile_picture.attached?
      end
      row :created_at
      row :updated_at
    end

    panel "Books" do
      table_for author.books do
        column :title
        column :publication_date
        column :number_of_pages
        column do |book|
          link_to "View", admin_book_path(book)
        end
      end
    end

    panel "Ebooks" do
      table_for author.ebooks do
        column :title
        column :publication_date
        column :number_of_pages
        column do |ebook|
          link_to "View", admin_ebook_path(ebook)
        end
      end
    end

    panel "CDs" do
      table_for author.cds do
        column :title
        column :publication_date
        column :audio_length
        column do |cd|
          link_to "View", admin_cd_path(cd)
        end
      end
    end

    panel "Tapes" do
      table_for author.tapes do
        column :title
        column :publication_date
        column :audio_length
        column do |tape|
          link_to "View", admin_tape_path(tape)
        end
      end
    end

    panel "Audios" do
      table_for author.audios do
        column :title
        column :publication_date
        column :audio_length
        column do |audio|
          link_to "View", admin_audio_path(audio)
        end
      end
    end

    panel "Videos" do
      table_for author.videos do
        column :title
        column :publication_date
        column :audio_length
        column do |video|
          link_to "View", admin_video_path(video)
        end
      end
    end
  end
end
