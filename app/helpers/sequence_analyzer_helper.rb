module SequenceAnalyzerHelper
  def activators
    [
      Activator.new('sun', 'Sun'),
      Activator.new('earth', 'Earth'),
      Activator.new('north_node', 'North Node'),
      Activator.new('south_node', 'South Node'),
      Activator.new('moon', 'Moon'),
      Activator.new('mercury', 'Mercury'),
      Activator.new('venus', 'Venus'),
      Activator.new('mars', 'Mars'),
      Activator.new('jupiter', 'Jupiter'),
      Activator.new('saturn', 'Saturn'),
      Activator.new('uranus', 'Uranus'),
      Activator.new('neptune', 'Neptune'),
      Activator.new('pluto', 'Pluto')
    ]
  end

  def activator_names_by_id
    {
      'sun' => 'Sun',
      'earth' => 'Earth',
      'north_node' => 'North Node',
      'south_node' => 'South Node',
      'moon' => 'Moon',
      'mercury' => 'Mercury',
      'venus' => 'Venus',
      'mars' => 'Mars',
      'jupiter' => 'Jupiter',
      'saturn' => 'Saturn',
      'uranus' => 'Uranus',
      'neptune' => 'Neptune',
      'pluto' => 'Pluto'
    }
  end

  def activations_by_gate
    activations_by_gate = {}
    gate_attributes = %i[sun_gate earth_gate north_node_gate south_node_gate moon_gate mercury_gate venus_gate mars_gate jupiter_gate saturn_gate uranus_gate neptune_gate pluto_gate]

    gate_attributes.each do |gate_attribute|
      personality_gate = @personality_activations[gate_attribute.to_s]
      activations_by_gate[personality_gate] = [] if activations_by_gate[personality_gate].nil?
      activations_by_gate[personality_gate] << "personality_#{gate_attribute}"
      design_gate = @design_activations[gate_attribute.to_s]
      activations_by_gate[design_gate] = [] if activations_by_gate[design_gate].nil?
      activations_by_gate[design_gate] << "design_#{gate_attribute}"
    end
    activations_by_gate
  end

  def planets_by_gate(personality_activations)
    planets_by_gate = {}
    gate_attributes = %i[sun_gate earth_gate north_node_gate south_node_gate moon_gate mercury_gate venus_gate mars_gate jupiter_gate saturn_gate uranus_gate neptune_gate pluto_gate]

    gate_attributes.each do |gate_attribute|
      personality_gate = if gate_attribute == :earth_gate
                           opposite_gate(personality_activations['sun_gate'])
                         elsif gate_attribute == :south_node_gate
                           opposite_gate(personality_activations['north_node_gate'])
                         else
                           personality_activations[gate_attribute.to_s]
                         end
      planets_by_gate[personality_gate] = [] if planets_by_gate[personality_gate].nil?
      planets_by_gate[personality_gate] << gate_attribute.to_s.gsub('_gate', '')
    end
    planets_by_gate
  end
end
