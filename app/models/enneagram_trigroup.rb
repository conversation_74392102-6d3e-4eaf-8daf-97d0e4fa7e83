class EnneagramTrigroup
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :id, :string
  attribute :name, :string
  attribute :enneatypes, default: []
  attribute :triads, default: []

  TRIGROUPS = {
    "object-relations" => {
      id: "object-relations",
      name: "Object Relations",
      triads: ["147", "258", "369"]
    },
    "hornevian-social-style" => {
      id: "hornevian-social-style",
      name: "Hornevian Social Style",
      triads: ["378", "459", "126"]
    },
    "harmonics-handling-stress" => {
      id: "harmonics-handling-stress",
      name: "Harmonics Handling Stress",
      triads: ["279", "468", "135"]
    },
    "group-a-validation-through" => {
      id: "group-a-validation-through",
      name: "Group A - Validation Through",
      triads: ["479", "125", "368"]
    },
    "group-b-getting-your-needs" => {
      id: "group-b-getting-your-needs",
      name: "Group B - Getting Your Needs",
      triads: ["478", "259", "136"]
    },
    "group-c-gift" => {
      id: "group-c-gift",
      name: "Group C - Gift",
      triads: ["137", "269", "458"]
    },
    "group-d-movement" => {
      id: "group-d-movement",
      name: "Group D - Movement",
      triads: ["379", "145", "268"]
    },
    "group-e-intellectually" => {
      id: "group-e-intellectually",
      name: "Group E - Intellectually",
      triads: ["278", "146", "359"]
    },
    "group-f-emotionally" => {
      id: "group-f-emotionally",
      name: "Group F - Emotionally",
      triads: ["127", "469", "358"]
    }
  }.freeze

  def initialize(attributes = {})
    super
    return unless attributes[:id]

    data = TRIGROUPS[attributes[:id].to_s]
    return unless data

    self.id = data[:id]
    self.name = data[:name]
    self.triads = data[:triads]
  end

  def self.all
    TRIGROUPS.values.map { |data| new(data) }
  end

  def self.find(id)
    data = TRIGROUPS[id.to_s]
    return nil unless data

    new(data)
  end

  def to_param
    id
  end

  def triad_objects
    triads.map { |triad_id| EnneagramTriad.find(triad_id) }.compact
  end
end
