class PublicBodygraph < ApplicationRecord
  has_one_attached :portrait
  has_many :comments, class_name: 'PublicBodygraphComment'

  attribute :name, :string
  attribute :birth_name, :string
  attribute :birth_date_local, :datetime
  attribute :birth_date_utc, :datetime
  attribute :design_date_utc, :datetime
  attribute :birth_country, :string
  attribute :birth_city, :string
  attribute :birth_data_source, :string
  attribute :birth_data_source_notes, :string
  attribute :birth_data_collector, :string
  attribute :rodden_rating, :string
  attribute :gender, :string
  attribute :aura_type, :string
  attribute :inner_authority, :string
  attribute :definition, :string
  attribute :profile, :string
  attribute :incarnation_cross, :string
  attribute :determination, :string
  attribute :environment, :string
  attribute :view, :string
  attribute :motivation, :string
  attribute :cognition, :string
  attribute :sense, :string
  attribute :variable, :string
  attribute :personality_activations, :string
  attribute :design_activations, :string
  attribute :head_defined, :boolean
  attribute :ajna_defined, :boolean
  attribute :throat_defined, :boolean
  attribute :spleen_defined, :boolean
  attribute :solar_plexus_defined, :boolean
  attribute :g_center_defined, :boolean
  attribute :sacral_defined, :boolean
  attribute :root_defined, :boolean
  attribute :ego_defined, :boolean
  attribute :personality_nodes_tone, :integer
  attribute :design_nodes_tone, :integer
  attribute :timezone, :string
  attribute :latitude, :float
  attribute :longitude, :float
  attribute :description, :string
  attribute :notable_for, :string
  attribute :famous, :boolean
  attribute :historical_event, :boolean
  attribute :profession, :string
  attribute :portrait, :string
  attribute :birth_date, :string # e.g. 23 / 09 / 1926
  attribute :birth_time, :string # e.g. 17:00
  attribute :all_activated_gates, :string
end
