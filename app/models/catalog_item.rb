class CatalogItem < ApplicationRecord
  self.abstract_class = true
  
  has_one_attached :cover
  
  validates :title, presence: true
  
  def self.ransackable_attributes(_auth_object = nil)
    ["acquisition_date", "created_at", "description", "disposal_date", "disposal_reason", 
     "id", "physical_location", "publication_date", "title", "updated_at"]
  end
  
  def self.ransackable_associations(_auth_object = nil)
    ["cover_attachment", "cover_blob"]
  end
end
