module Hdhd
  class ScheduledEvent < ApplicationRecord
    self.table_name = 'hdhd_scheduled_events'
    
    EVENT_TYPES = ['talk', 'workshop', 'performance', 'special_event', 'interview', 'panel'].freeze
    
    belongs_to :conference, class_name: 'Hdhd::Conference'
    has_and_belongs_to_many :speakers, 
                           join_table: 'hdhd_scheduled_events_speakers',
                           class_name: 'Hdhd::Speaker'
    
    has_one_attached :cover_image, dependent: :purge_later
    
    validates :title, presence: true
    validates :start_date, presence: true
    validates :duration_minutes, presence: true, numericality: { greater_than: 0 }
    validates :event_type, presence: true, inclusion: { in: EVENT_TYPES }
    validates :conference, presence: true
    
    def self.ransackable_attributes(_auth_object = nil)
      ["id", "title", "description", "start_date", "duration_minutes", 
       "event_type", "conference_id", "created_at", "updated_at"]
    end
    
    def self.ransackable_associations(_auth_object = nil)
      ["conference", "speakers", "cover_image_attachment", "cover_image_blob"]
    end
    
    def end_date
      start_date + duration_minutes.minutes
    end
    
    def duration_hours
      (duration_minutes / 60.0).round(1)
    end
    
    # Scopes for easy filtering
    scope :talks, -> { where(event_type: 'talk') }
    scope :workshops, -> { where(event_type: 'workshop') }
    scope :performances, -> { where(event_type: 'performance') }
    scope :special_events, -> { where(event_type: 'special_event') }
    scope :interviews, -> { where(event_type: 'interview') }
    scope :panels, -> { where(event_type: 'panel') }
    
    # Upcoming events
    scope :upcoming, -> { where('start_date > ?', Time.current).order(start_date: :asc) }
    
    # Past events
    scope :past, -> { where('start_date < ?', Time.current).order(start_date: :desc) }
    
    # Events for a specific day
    scope :for_day, ->(date) { 
      where('DATE(start_date) = ?', date.to_date).order(start_date: :asc) 
    }
  end
end
