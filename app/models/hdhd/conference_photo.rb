class Hdhd::ConferencePhoto < ApplicationRecord
  self.table_name = 'hdhd_conference_photos'  # Explicitly set the table name

  belongs_to :conference, class_name: "Hdhd::Conference", foreign_key: "hdhd_conference_id"

  has_one_attached :photo

  validates :title, presence: true
  validates :photographer, presence: true
  validates :photo, presence: true

  def self.ransackable_attributes(auth_object = nil)
    ["conference_id", "created_at", "id", "photographer", "title", "updated_at"]
  end
end
