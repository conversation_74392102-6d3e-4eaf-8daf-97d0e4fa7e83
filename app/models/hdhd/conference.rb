class Hdhd::Conference < ApplicationRecord
  self.table_name = 'hdhd_conferences' # Explicitly set the table name

  has_and_belongs_to_many :speakers, class_name: 'Hdhd::Speaker'
  has_many :conference_photos, class_name: 'Hdhd::ConferencePhoto', dependent: :destroy
  has_many :scheduled_events, class_name: 'Hdhd::ScheduledEvent', dependent: :destroy
  belongs_to :product, optional: true
  has_many :conference_tickets
  has_many :attendees, through: :conference_tickets, source: :user

  validates :start_date, presence: true
  validates :description, presence: true

  def self.ransackable_attributes(_auth_object = nil)
    ["created_at", "description", "id", "start_date", "updated_at", "product_id"]
  end

  def self.ransackable_associations(_auth_object = nil)
    ["conference_photos", "speakers", "product", "conference_tickets", "attendees"]
  end

  def ticket_price
    497.00 # Default price, could be stored in the database
  end

  def year
    start_date.year
  end

  def to_param
    year.to_s # Generates URLs like /conferences/2021
  end
end
