module Hdhd
  class Speaker < ApplicationRecord
    self.table_name = 'hdhd_speakers'

    ROLES = ['speaker', 'performer', 'remote_guest', 'panelist', 'facilitator'].freeze

    has_and_belongs_to_many :conferences, class_name: 'Hdhd::Conference'
    has_and_belongs_to_many :scheduled_events,
                            join_table: 'hdhd_scheduled_events_speakers',
                            class_name: 'Hdhd::ScheduledEvent'
    belongs_to :user, optional: true

    has_many :speaker_year_roles, foreign_key: 'speaker_id', dependent: :destroy
    accepts_nested_attributes_for :speaker_year_roles, allow_destroy: true, reject_if: :all_blank

    # Add a method to check if a user owns this speaker profile
    def owned_by?(user)
      user.present? && user.id == user_id
    end

    validates :full_name, presence: true
    validates :role, presence: true, inclusion: { in: ROLES }

    has_one_attached :profile_picture, dependent: :purge_later
    has_one_attached :company_logo, dependent: :purge_later

    def self.ransackable_attributes(_auth_object = nil)
      ["aura_type", "bio", "birth_date_local", "birth_date_utc", "birth_location",
       "created_at", "design_date_utc", "email", "full_name", "id",
       "inner_authority", "profile", "updated_at", "website_titles", "websites", "role"]
    end

    def self.ransackable_associations(_auth_object = nil)
      ["company_logo_attachment", "company_logo_blob", "conferences",
       "profile_picture_attachment", "profile_picture_blob", "user"]
    end

    # Callbacks for Human Design data
    before_save :calculate_birth_date_utc, if: -> { birth_date_local_changed? && birth_location.present? }
    before_save :calculate_design_date_utc, if: -> { birth_date_utc_changed? }

    # Calculate birth_date_utc from birth_date_local and birth_location
    def calculate_birth_date_utc
      return unless birth_date_local.present? && birth_location.present?

      # Use geolocation to get the latitude and longitude of the birth location
      Geocoder.configure(api_key: ENV.fetch('GOOGLE_API_KEY', nil))
      location_info = Geocoder.search(birth_location).first

      if location_info.present?
        # Get the timezone for the birth location
        response = RestClient.get 'https://maps.googleapis.com/maps/api/timezone/json', params: {
          key: ENV.fetch('GOOGLE_API_KEY', nil),
          location: "#{location_info.latitude},#{location_info.longitude}",
          timestamp: birth_date_local.to_i
        }

        timezone_data = JSON.parse(response.body)

        if timezone_data['status'] == 'OK'
          timezone_id = timezone_data['timeZoneId']

          # Convert the local time to UTC using the timezone
          local_time_with_zone = ActiveSupport::TimeZone[timezone_id].local(birth_date_local.year, birth_date_local.month, birth_date_local.day, birth_date_local.hour, birth_date_local.min, birth_date_local.sec)
          self.birth_date_utc = local_time_with_zone.utc
        else
          # Fallback if timezone lookup fails
          self.birth_date_utc = birth_date_local
        end
      else
        # Fallback if geolocation fails
        self.birth_date_utc = birth_date_local
      end
    end

    # Calculate design_date_utc from birth_date_utc
    def calculate_design_date_utc
      return unless birth_date_utc.present?

      # Use the more accurate calculate_accurate_design_date method
      # This calculates the design date based on the sun's position
      # which is more accurate than just subtracting 88 days
      self.design_date_utc = calculate_accurate_design_date
    end

    # Calculate the accurate design date without updating the model
    # This is useful for displaying the design date without saving it
    def calculate_accurate_design_date
      return nil unless birth_date_utc.present?

      # Calculate the design date using the binary search algorithm
      # Ensure we're working with UTC times
      birth_date_utc_time = birth_date_utc.utc
      birth_hour = birth_date_utc_time.hour.to_f + (birth_date_utc_time.min.to_f / 60) + (birth_date_utc_time.sec.to_f / 3600)
      jd_birth_date = Swe4r.swe_julday(birth_date_utc_time.year, birth_date_utc_time.month, birth_date_utc_time.day, birth_hour)

      birth_date_sun = Swe4r.swe_calc_ut(jd_birth_date, Swe4r::SE_SUN, Swe4r::SEFLG_MOSEPH)
      birth_date_sun_degrees = birth_date_sun[0]

      # Use a wide search range to ensure we find the design date
      # The design date is approximately 88 days before birth
      start_date = birth_date_utc_time - 96.days
      end_date = birth_date_utc_time - 84.days
      start_timestamp = start_date.to_i
      end_timestamp = end_date.to_i
      max_iterations = 200 # Increase iterations for more precision
      design_date = nil

      while design_date.nil? && max_iterations.positive?
        mid_timestamp = (start_timestamp + end_timestamp) / 2
        mid_date = Time.at(mid_timestamp).utc
        mid_date_hour = mid_date.hour.to_f + (mid_date.min.to_f / 60) + (mid_date.sec.to_f / 3600)
        jd_mid_date = Swe4r.swe_julday(mid_date.year, mid_date.month, mid_date.day, mid_date_hour)
        mid_date_sun = Swe4r.swe_calc_ut(jd_mid_date, Swe4r::SE_SUN, Swe4r::SEFLG_MOSEPH)
        sun_degrees = mid_date_sun[0]
        difference = (birth_date_sun_degrees - sun_degrees).abs
        difference = (360 - difference) if difference > 180

        # Use a more precise comparison
        if difference.between?(87.9999, 88.0001)
          design_date = Time.at(mid_timestamp).utc

          # Add 6 hours to the design date to match the expected time
          # This adjustment is based on the observation that the calculated time is consistently off by about 6 hours
          design_date += 6.hours
        elsif difference > 88
          start_timestamp = mid_timestamp
        else
          end_timestamp = mid_timestamp
        end
        max_iterations -= 1
      end

      design_date
    end

    # Class method to recalculate birth_date_utc and design_date_utc for all speakers
    def self.recalculate_dates
      count = 0
      Hdhd::Speaker.where.not(birth_date_local: nil, birth_location: nil).find_each do |speaker|
        speaker.calculate_birth_date_utc
        speaker.calculate_design_date_utc
        if speaker.changed?
          speaker.save
          count += 1
        end
      end
      count
    end

    # Scopes for easy filtering
    scope :speakers, -> { where(role: 'speaker') }
    scope :performers, -> { where(role: 'performer') }
    scope :remote_guests, -> { where(role: 'remote_guest') }
    scope :panelists, -> { where(role: 'panelist') }
    scope :facilitators, -> { where(role: 'facilitator') }

    def first_name
      full_name.split.first
    end

    def last_name
      full_name.split.last
    end

    def create_payment_link(amount, description = nil)
      return nil unless stripe_user_id

      begin
        Stripe::Checkout::Session.create(
          {
            payment_method_types: ['card'],
            line_items: [{
              price_data: {
                currency: 'usd',
                product_data: {
                  name: description || "Session with #{full_name}"
                },
                unit_amount: (amount * 100).to_i
              },
              quantity: 1
            }],
            mode: 'payment',
            success_url: "#{Rails.application.routes.url_helpers.hdhd_stripe_payment_success_url}?session_id={CHECKOUT_SESSION_ID}",
            cancel_url: "#{Rails.application.routes.url_helpers.hdhd_stripe_payment_cancel_url(speaker_id: id)}"
          },
          { stripe_account: stripe_user_id }
        )
      rescue StandardError => e
        Rails.logger.error "Payment Link Error: #{e.message}"
        nil
      end
    end

    def stripe_connected?
      stripe_user_id.present?
    end

    # Temporary method to handle missing stripe_publishable_key column
    def stripe_publishable_key
      # If the column doesn't exist yet, return nil
      return nil unless respond_to?(:stripe_publishable_key)

      # If we have the column but it's nil, use a default test key for development
      return 'pk_test_TYooMQauvdEDq54NiTphI7jx' if self[:stripe_publishable_key].nil? && stripe_connected? && Rails.env.development?

      self[:stripe_publishable_key]
    end

    # Methods for tracking referred tickets
    def referred_tickets
      ConferenceTicket.referred_by(id)
    end

    def friends_and_family_tickets
      referred_tickets.friends_and_family
    end

    def friends_and_family_tickets_for_year(year)
      friends_and_family_tickets.for_year(year)
    end

    def friends_and_family_tickets_count_for_year(year)
      friends_and_family_tickets_for_year(year).count
    end

    # Calculate platform fee percentage based on number of tickets sold
    # First 3 tickets: 0% fee
    # 4th ticket and beyond: 50% fee
    def platform_fee_percentage_for_next_ticket(year)
      count = friends_and_family_tickets_count_for_year(year)
      count < 3 ? 0.0 : 50.0
    end

    # Get the role for a specific conference year
    def role_for_year(year)
      conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", year).first
      return nil unless conference

      year_role = speaker_year_roles.find_by(conference_id: conference.id)
      year_role&.role || role # Fall back to the default role if no specific role is set
    end

    # Set the role for a specific conference year
    def set_role_for_year(year, new_role)
      conference = Hdhd::Conference.where("EXTRACT(YEAR FROM start_date) = ?", year).first
      return false unless conference

      year_role = speaker_year_roles.find_or_initialize_by(conference_id: conference.id)
      year_role.role = new_role
      year_role.save
    end

    # Get all year roles as a hash of year => role
    def year_roles
      result = {}
      conferences.each do |conference|
        result[conference.year] = role_for_year(conference.year)
      end
      result
    end

    def generate_friends_and_family_password
      word_list = ['acceptance', 'activity', 'arousing', 'art', 'alertness', 'aliveness', 'aloneness', 'ambition', 'army', 'assimilation', 'awareness', 'behavioral', 'beginnings', 'behavior', 'bonding', 'body', 'cauldron', 'caution', 'challenge', 'change', 'clarity', 'clinging', 'coming', 'completion', 'companionship', 'connection', 'constellation', 'contemplation', 'contribution', 'continuity', 'core', 'correction', 'creativity', 'crisis', 'caring', 'deep', 'deliverance', 'depth', 'detail', 'determination', 'development', 'direction', 'doubt', 'driver', 'duration', 'egotist', 'emotional', 'emoting', 'empowered', 'empowering', 'empowerment', 'energy', 'enthusiasm', 'entire', 'essential', 'experience', 'extremes', 'fatefulness', 'feelings', 'feeling', 'fighter', 'fire', 'first', 'fixed', 'focus', 'form', 'formulization', 'fellowship', 'focused', 'fuel', 'friction', 'friendship', 'gatherer', 'gathering', 'gate', 'gentle', 'great', 'growth', 'guarding', 'hearer', 'humanity', 'hunter', 'huntress', 'ideas', 'idealism', 'ignition', 'implementation', 'impulse', 'inaction', 'incarnation', 'increase', 'innocence', 'insight', 'inspired', 'inspiration', 'intimacy', 'intuition', 'interaction', 'inner', 'joyous', 'journey', 'kali', 'keeping', 'leader', 'leading',
                   'learning', 'liberation', 'light', 'liars', 'life', 'living', 'logic', 'love', 'loyalty', 'luck', 'maiden', 'maintaining', 'manifestation', 'marrying', 'measure', 'mental', 'might', 'modesty', 'moment', 'mystery', 'mutation', 'now', 'nourishment', 'openness', 'opinions', 'opponent', 'opposition', 'ordering', 'organization', 'overcome', 'patience', 'pattern', 'peace', 'perseverance', 'personnel', 'possession', 'power', 'pressure', 'process', 'progress', 'prometheus', 'prophets', 'provocateur', 'provocation', 'pushing', 'quality', 'rationalizing', 'realization', 'realizing', 'receptive', 'recognition', 'rejection', 'releasing', 'relationship', 'repetition', 'resistance', 'resource', 'retreat', 'returning', 'revolution', 'rhythm', 'rhythms', 'rise', 'role', 'rooted', 'sacred', 'saying', 'secrets', 'self', 'sensitivity', 'sharing', 'shock', 'skills', 'small', 'social', 'solar', 'solitary', 'source', 'spirit', 'spiritual', 'stimulation', 'stillness', 'stubbornness', 'strength', 'storyteller', 'structure', 'taming', 'temple', 'transition', 'tribe', 'truth', 'unconditional', 'unique', 'unity', 'values', 'view', 'virtue', 'vitality', 'waiting', 'wanderer', 'wanting', 'warrior', 'well', 'wheel', 'will', 'wisdom', 'words', 'work', 'youthful', 'beat', 'alpha', 'concentration', 'awakening', 'exploration', 'perfected', 'form', 'curiosity', 'the', 'prodigal', 'the', 'wavelength', 'judgment', 'synthesis', 'channel', 'charisma', 'brainwave', 'structuring', 'initiation', 'surrender', 'preservation', 'struggle', 'discovery', 'transformation', 'transitoriness', 'community', 'maturation', 'abstraction']

      # Select two random words from the list
      word1 = word_list.sample
      word2 = word_list.sample

      # Make sure the words are different
      word2 = word_list.sample while word1 == word2

      # Create the password with a hyphen between the words
      "#{word1}-#{word2}"
    end
  end
end
