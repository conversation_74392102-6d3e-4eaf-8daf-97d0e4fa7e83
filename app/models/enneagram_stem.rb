class EnneagramStem
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :id, :string
  attribute :name, :string
  attribute :enneatypes, default: []
  attribute :centers, :string

  STEMS = {
    "1-2" => { id: "1-2", name: "Stem 1-2", enneatypes: [1, 2], centers: "Body–Heart" },
    "1-3" => { id: "1-3", name: "Stem 1-3", enneatypes: [1, 3], centers: "Body–Heart" },
    "1-4" => { id: "1-4", name: "Stem 1-4", enneatypes: [1, 4], centers: "Body–Heart" },
    "1-5" => { id: "1-5", name: "Stem 1-5", enneatypes: [1, 5], centers: "Body–Head" },
    "1-6" => { id: "1-6", name: "Stem 1-6", enneatypes: [1, 6], centers: "Body–Head" },
    "1-7" => { id: "1-7", name: "Stem 1-7", enneatypes: [1, 7], centers: "Body–Head" },

    "2-5" => { id: "2-5", name: "Stem 2-5", enneatypes: [2, 5], centers: "Heart–Head" },
    "2-6" => { id: "2-6", name: "Stem 2-6", enneatypes: [2, 6], centers: "Heart–Head" },
    "2-7" => { id: "2-7", name: "Stem 2-7", enneatypes: [2, 7], centers: "Heart–Head" },
    "2-8" => { id: "2-8", name: "Stem 2-8", enneatypes: [2, 8], centers: "Heart–Body" },
    "2-9" => { id: "2-9", name: "Stem 2-9", enneatypes: [2, 9], centers: "Heart–Body" },

    "3-5" => { id: "3-5", name: "Stem 3-5", enneatypes: [3, 5], centers: "Heart–Head" },
    "3-6" => { id: "3-6", name: "Stem 3-6", enneatypes: [3, 6], centers: "Heart–Head" },
    "3-7" => { id: "3-7", name: "Stem 3-7", enneatypes: [3, 7], centers: "Heart–Head" },
    "3-8" => { id: "3-8", name: "Stem 3-8", enneatypes: [3, 8], centers: "Heart–Body" },
    "3-9" => { id: "3-9", name: "Stem 3-9", enneatypes: [3, 9], centers: "Heart–Body" },

    "4-5" => { id: "4-5", name: "Stem 4-5", enneatypes: [4, 5], centers: "Heart–Head" },
    "4-6" => { id: "4-6", name: "Stem 4-6", enneatypes: [4, 6], centers: "Heart–Head" },
    "4-7" => { id: "4-7", name: "Stem 4-7", enneatypes: [4, 7], centers: "Heart–Head" },
    "4-8" => { id: "4-8", name: "Stem 4-8", enneatypes: [4, 8], centers: "Heart–Body" },
    "4-9" => { id: "4-9", name: "Stem 4-9", enneatypes: [4, 9], centers: "Heart–Body" },

    "5-8" => { id: "5-8", name: "Stem 5-8", enneatypes: [5, 8], centers: "Head–Body" },
    "5-9" => { id: "5-9", name: "Stem 5-9", enneatypes: [5, 9], centers: "Head–Body" },

    "6-8" => { id: "6-8", name: "Stem 6-8", enneatypes: [6, 8], centers: "Head–Body" },
    "6-9" => { id: "6-9", name: "Stem 6-9", enneatypes: [6, 9], centers: "Head–Body" },

    "7-8" => { id: "7-8", name: "Stem 7-8", enneatypes: [7, 8], centers: "Head–Body" },
    "7-9" => { id: "7-9", name: "Stem 7-9", enneatypes: [7, 9], centers: "Head–Body" }
  }.freeze

  def initialize(attributes = {})
    super
    return unless attributes[:id]

    data = STEMS[attributes[:id].to_s]
    return unless data

    self.id = data[:id]
    self.name = data[:name]
    self.enneatypes = data[:enneatypes]
    self.centers = data[:centers]
  end

  def self.all
    STEMS.values.map { |data| new(data) }
  end

  def self.find(id)
    data = STEMS[id.to_s]
    return nil unless data

    new(data)
  end

  def self.for_triad(triad)
    triad.enneatypes.combination(2).filter_map do |pair|
      stem_id = pair.sort.join('-')
      find(stem_id) # returns EnneagramStem object
    end
  end

  def to_param
    id
  end

  def enneatype_objects
    enneatypes.map { |type_id| Enneatype.find(type_id) }.compact
  end

  def triads
    EnneagramTriad.all.select do |triad|
      stem_pairs = triad.enneatypes.combination(2).map { |pair| pair.sort.join('-') }
      stem_pairs.include?(id)
    end
  end
end
