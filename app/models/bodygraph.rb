class Bodygraph < ApplicationRecord
  belongs_to :user

  # Define attributes
  attribute :name, :string
  attribute :birth_date_local, :datetime
  attribute :birth_date_utc, :datetime
  attribute :design_date_utc, :datetime
  attribute :birth_country, :string
  attribute :birth_city, :string
  attribute :aura_type, :string
  attribute :inner_authority, :string
  attribute :definition, :string
  attribute :profile, :string
  attribute :incarnation_cross, :string
  attribute :determination, :string
  attribute :environment, :string
  attribute :view, :string
  attribute :motivation, :string
  attribute :cognition, :string
  attribute :sense, :string
  attribute :variable, :string
  attribute :personality_activations, :string
  attribute :design_activations, :string
  attribute :head_defined, :boolean
  attribute :ajna_defined, :boolean
  attribute :throat_defined, :boolean
  attribute :spleen_defined, :boolean
  attribute :solar_plexus_defined, :boolean
  attribute :g_center_defined, :boolean
  attribute :sacral_defined, :boolean
  attribute :root_defined, :boolean
  attribute :egoß∑∑_defined, :boolean
  attribute :personality_nodes_tone, :integer
  attribute :design_nodes_tone, :integer
  attribute :timezone, :string
  attribute :latitude, :float
  attribute :longitude, :float
  attribute :birth_date, :string # e.g. 23 / 09 / 1926
  attribute :birth_time, :string # e.g. 17:00
  attribute :all_activated_gates, :string

  # Allowable attributes for Ransack search
  def self.ransackable_attributes(_auth_object = nil)
    # List the attributes you want to allow for searching
    ["ajna_defined", "all_activated_gates", "aura_type", "birth_city", "birth_country", "birth_date", "birth_date_utc", "birth_time", "cognition", "created_at", "definition", "design_activations", "design_date_utc", "design_nodes_tone", "determination", "ego_defined", "environment", "g_center_defined", "head_defined", "id", "incarnation_cross", "inner_authority", "latitude", "longitude", "motivation", "name", "personality_activations", "personality_nodes_tone", "profile", "root_defined", "sacral_defined", "sense", "solar_plexus_defined", "spleen_defined", "throat_defined", "timezone", "type", "updated_at", "user_id", "variable", "view"]
  end
end
