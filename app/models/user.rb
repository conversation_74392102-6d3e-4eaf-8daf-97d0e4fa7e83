class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :confirmable,
         :omniauthable, omniauth_providers: [:google_oauth2]

  # Active Storage association
  has_one_attached :avatar, dependent: :purge_later do |attachable|
    attachable.variant :thumb, resize_to_limit: [100, 100]
  end

  # Other associations
  has_many :bodygraphs
  has_many :reminders
  has_one :speaker, class_name: 'Hdhd::Speaker', foreign_key: 'user_id'
  has_many :purchased_products
  has_many :products, through: :purchased_products
  has_many :notifications, dependent: :destroy
  has_many :conference_tickets
  has_many :attended_conferences, through: :conference_tickets, source: :conference, class_name: 'Hdhd::Conference'

  # Avatar helper methods
  def avatar_url
    if avatar.attached?
      Rails.application.routes.url_helpers.rails_blob_path(avatar, only_path: true)
    else
      image_url || "default_avatar.png"
    end
  end

  # Allowable attributes for Ransack search
  def self.ransackable_attributes(_auth_object = nil)
    ["email", "name", "created_at", "updated_at", "id"] # Add more attributes you want to allow for searching
  end

  # Allowable associations for Ransack search
  def self.ransackable_associations(_auth_object = nil)
    ["speaker", "bodygraphs", "reminders", "avatar_attachment", "avatar_blob", "conference_tickets", "attended_conferences"]
  end

  def has_ticket_for_year?(year)
    conference_tickets.for_year(year).exists?
  end

  def password_required?
    return false if provider.present? # No password required for Google users

    super # Use Devise's default logic otherwise
  end

  # Skip confirmation for OAuth users
  def send_confirmation_notification?
    provider.blank? && super
  end

  # Override to skip confirmation for OAuth users
  def confirmation_required?
    provider.blank? && super
  end

  def self.from_google(email:, name:, uid:, image:)
    Rails.logger.info "Finding or creating user with email: #{email}"
    user = find_or_initialize_by(email: email)

    if user.new_record?
      Rails.logger.info "Creating new user from Google authentication"
      user.password = SecureRandom.hex(10)
      user.provider = "google"
      user.google_uid = uid # Use google_uid instead of uid
      user.image_url = image
      user.name = name
      # Auto-confirm Google users
      user.skip_confirmation!
      user.save!
      Rails.logger.info "New user created: #{user.inspect}"
    else
      Rails.logger.info "Updating existing user from Google authentication"
      # Update provider and uid if they're not set
      user.provider = "google" if user.provider.blank?
      user.google_uid = uid if user.google_uid.blank? # Use google_uid instead of uid
      # Ensure the user is confirmed
      user.confirm unless user.confirmed?
      user.update!(name: name, image_url: image)
      Rails.logger.info "User updated: #{user.inspect}"
    end

    user
  end

  # Class method to create or update a user based on Google OAuth2 data
  def self.from_omniauth(auth)
    user = find_or_create_by(email: auth.info.email) do |u|
      u.password = Devise.friendly_token[0, 20]
      u.provider = "google"
      # Auto-confirm OAuth users
      u.skip_confirmation!
    end

    # Ensure the user is confirmed
    user.confirm unless user.confirmed?

    user.update!(
      name: auth.info.name,
      image: auth.info.image,
      google_token: auth.credentials.token,
      google_refresh_token: auth.credentials.refresh_token.presence || user.google_refresh_token
    )
    user
  end
end
