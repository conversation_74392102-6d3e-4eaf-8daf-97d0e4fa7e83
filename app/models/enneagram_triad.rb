class EnneagramTriad
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :id, :string
  attribute :name, :string
  attribute :nickname, :string
  attribute :enneatypes, default: []

  TRIADS = {
    "125" => { id: "125", name: "Self-Validation", enneatypes: [1, 2, 5], nickname: "The Know It All/Friend Zone" },
    "126" => { id: "126", name: "Compliant/Superego", enneatypes: [1, 2, 6], nickname: "The Mom/Ok Boomer" },
    "127" => { id: "127", name: "Upward Emotion", enneatypes: [1, 2, 7], nickname: "The Priest/Cool Teacher" },
    "135" => { id: "135", name: "Competency", enneatypes: [1, 3, 5], nickname: "The Robot/Robocelibate" },
    "136" => { id: "136", name: "Earning It Through Actions", enneatypes: [1, 3, 6], nickname: "The Worker/Middle Manager" },
    "137" => { id: "137", name: "<PERSON>", enneatypes: [1, 3, 7], nickname: "The Startup/Welcome To My Ted Talk" },
    "145" => { id: "145", name: "Inflexible", enneatypes: [1, 4, 5], nickname: "The Inflexible/Insectoid" },
    "146" => { id: "146", name: "Critical", enneatypes: [1, 4, 6], nickname: "The Critic/Big Pain" },
    "147" => { id: "147", name: "Frustration", enneatypes: [1, 4, 7], nickname: "The Lunatic/Princess And The Pea" },
    "258" => { id: "258", name: "Rejection", enneatypes: [2, 5, 8], nickname: "The Monk/Cult Classic" },
    "259" => { id: "259", name: "Ignoring Own Needs", enneatypes: [2, 5, 9], nickname: "The Psychotherapist/Spineless Saint" },
    "268" => { id: "268", name: "Moving Others", enneatypes: [2, 6, 8], nickname: "The Superhero Rebel/I Wouldn't Hurt A Fly" },
    "269" => { id: "269", name: "Empathy", enneatypes: [2, 6, 9], nickname: "The Stockholm Syndrome" },
    "278" => { id: "278", name: "Optimistic", enneatypes: [2, 7, 8], nickname: "The Venusian Brat/Smothering Jazz Hands" },
    "279" => { id: "279", name: "Positive Outlook", enneatypes: [2, 7, 9], nickname: "The Rainbow/Hippie Burnout" },
    "358" => { id: "358", name: "Emotionally Detached", enneatypes: [3, 5, 8], nickname: "The Serial Killer/American Psycho" },
    "359" => { id: "359", name: "Diplomatic", enneatypes: [3, 5, 9], nickname: "The Coder/Flatline" },
    "368" => { id: "368", name: "Reality", enneatypes: [3, 6, 8], nickname: "The Warrior/Kyle/Kylie" },
    "369" => { id: "369", name: "Attachment", enneatypes: [3, 6, 9], nickname: "The Bermuda Triangle/Tofu" },
    "378" => { id: "378", name: "Assertive", enneatypes: [3, 7, 8], nickname: "The Comet/Chad/Stacey" },
    "379" => { id: "379", name: "Moving/Flexible", enneatypes: [3, 7, 9], nickname: "The Sex/DJ" },
    "458" => { id: "458", name: "Value", enneatypes: [4, 5, 8], nickname: "The Demon/Useless Beast" },
    "459" => { id: "459", name: "Withdrawing", enneatypes: [4, 5, 9], nickname: "The Ghost/Hateful Ghost" },
    "468" => { id: "468", name: "Reactive", enneatypes: [4, 6, 8], nickname: "The Grenade/Public Display Of Affliction" },
    "469" => { id: "469", name: "Downward Emotion", enneatypes: [4, 6, 9], nickname: "The Charlie Brown/Whiny Tears" },
    "478" => { id: "478", name: "Focusing on Your Own Needs", enneatypes: [4, 7, 8], nickname: "The Uranus/Revolving Door Rehab" },
    "479" => { id: "479", name: "Fantasy/Ideal", enneatypes: [4, 7, 9], nickname: "The Palm Reader/Huh?" }
  }.freeze

  def initialize(attributes = {})
    super
    return unless attributes[:id]

    data = TRIADS[attributes[:id].to_s]
    return unless data

    self.id = data[:id]
    self.name = data[:name]
    self.nickname = data[:nickname]
    self.enneatypes = data[:enneatypes]
  end

  def self.all
    TRIADS.values.map { |data| new(data) }
  end

  def self.find(id)
    data = TRIADS[id.to_s]
    return nil unless data

    new(data)
  end

  def to_param
    id
  end

  def enneatype_objects
    enneatypes.map { |type_id| Enneatype.find(type_id) }.compact
  end

  def trigroup
    EnneagramTrigroup.all.find { |trigroup| trigroup.triads.include?(id) }
  end

  def qualities
    qualities = []
    stems = EnneagramStem.for_triad(self)

    stems.each do |stem|
      # Find other triads that contain this stem
      other_triads = EnneagramTriad.all.select do |triad|
        next if triad.id == id

        stem_pairs = triad.enneatypes.combination(2).map { |pair| pair.sort.join('-') }
        stem_pairs.include?(stem.id)
      end

      other_triads.each do |other_triad|
        qualities << {
          name: other_triad.name,
          triad_id: other_triad.id,
          stem_id: stem.id
        }
      end
    end

    qualities.uniq { |q| [q[:name], q[:stem_id]] }
  end

  def qualities_with_triads
    # Return primary qualities (same as qualities but with triad objects)
    qualities.map do |quality|
      {
        name: quality[:name],
        triad: EnneagramTriad.find(quality[:triad_id])
      }
    end
  end

  def secondary_qualities
    # Qualities from other triads that have a single fix from this triad
    secondary_qualities = []

    EnneagramTriad.all.each do |other_triad|
      next if other_triad.id == id

      # Count how many enneatypes this triad shares with the other triad
      shared_enneatypes = enneatypes & other_triad.enneatypes

      # If exactly one enneatype is shared, this is a secondary quality
      next unless shared_enneatypes.length == 1

      secondary_qualities << {
        name: other_triad.name,
        triad: other_triad,
        shared_enneatype: shared_enneatypes.first
      }
    end

    secondary_qualities
  end

  def missing_qualities
    # Qualities from triads that don't belong to this triad (no shared enneatypes)
    missing_qualities = []

    EnneagramTriad.all.each do |other_triad|
      next if other_triad.id == id

      # Count how many enneatypes this triad shares with the other triad
      shared_enneatypes = enneatypes & other_triad.enneatypes

      # If no enneatypes are shared, this is a missing quality
      next unless shared_enneatypes.empty?

      missing_qualities << {
        name: other_triad.name,
        triad: other_triad
      }
    end

    missing_qualities
  end

  def statement_for_triad_name(triad_name, enneatype_id)
    case triad_name
    when "Fantasy"
      "The most fantasy or ideal-oriented #{enneatype_id}."
    when "Your Own Needs"
      "The most focused on your own needs #{enneatype_id}."
    when "Self-Validation"
      "The most self-validating #{enneatype_id}."
    when "Ignoring Own Needs"
      "The most likely to ignore own needs #{enneatype_id}."
    when "Reality"
      "The most reality or externally focused #{enneatype_id}."
    when "Earning It Through Actions"
      "The most earning through actions #{enneatype_id}."
    when "Upward Emotion"
      "The most emotionally upward #{enneatype_id}."
    when "Moving/Flexible"
      "The most moving or flexible #{enneatype_id}."
    when "Compliant/Superego"
      "The most compliant or superego-driven #{enneatype_id}."
    when "Moving Others"
      "The most focused on moving others #{enneatype_id}."
    when "Positive Outlook"
      "The most positive in outlook #{enneatype_id}."
    when "Downward Emotion"
      "The most emotionally downward #{enneatype_id}."
    when "Emotionally Detached"
      "The most emotionally detached #{enneatype_id}."
    when "Vision"
      "The most visionary #{enneatype_id}."
    when "Competency"
      "The most competent #{enneatype_id}."
    else
      "The most #{triad_name.downcase} #{enneatype_id}."
    end
  end

  def statements_for_enneatype(enneatype_id)
    return [] unless enneatypes.include?(enneatype_id)

    statements = []

    # Primary statement for this enneatype based on this triad's name
    statements << statement_for_triad_name(name, enneatype_id)

    # Find the other two enneatypes in this tritype, excluding the current one
    other_two = enneatypes - [enneatype_id]

    # We want triads that include BOTH of these other two enneatypes but NOT the current enneatype
    shared_triads = EnneagramTriad.all.select do |triad|
      (other_two - triad.enneatypes).empty? &&
        !triad.enneatypes.include?(enneatype_id)
    end

    shared_triads.each do |shared_triad|
      statements << statement_for_triad_name(shared_triad.name, enneatype_id)
    end

    statements.uniq
  end
end
