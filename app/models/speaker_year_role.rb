class SpeakerYearRole < ApplicationRecord
  belongs_to :speaker, class_name: 'Hdhd::Speaker'
  belongs_to :conference, class_name: 'Hdhd::Conference'
  
  validates :role, presence: true, inclusion: { in: Hdhd::Speaker::ROLES }
  validates :speaker_id, uniqueness: { scope: :conference_id, message: "already has a role for this conference year" }
  
  def self.ransackable_attributes(_auth_object = nil)
    ["conference_id", "created_at", "id", "role", "speaker_id", "updated_at"]
  end
  
  def self.ransackable_associations(_auth_object = nil)
    ["conference", "speaker"]
  end
end
