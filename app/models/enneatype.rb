class Enneatype
  include ActiveModel::Model
  include ActiveModel::Attributes

  attribute :id, :integer
  attribute :name, :string

  ENNEATYPES = {
    1 => { id: 1, name: "Type 1 - The Perfectionist" },
    2 => { id: 2, name: "Type 2 - The Helper" },
    3 => { id: 3, name: "Type 3 - The Achiever" },
    4 => { id: 4, name: "Type 4 - The Individualist" },
    5 => { id: 5, name: "Type 5 - The Investigator" },
    6 => { id: 6, name: "Type 6 - The Loyalist" },
    7 => { id: 7, name: "Type 7 - The Enthusiast" },
    8 => { id: 8, name: "Type 8 - The Challenger" },
    9 => { id: 9, name: "Type 9 - The Peacemaker" }
  }.freeze

  def initialize(attributes = {})
    super
    return unless attributes[:id]

    data = ENNEATYPES[attributes[:id]]
    self.id = data[:id] if data
    self.name = data[:name] if data
  end

  def self.all
    ENNEATYPES.values.map { |data| new(data) }
  end

  def self.find(id)
    data = ENNEATYPES[id.to_i]
    return nil unless data

    new(data)
  end

  def to_param
    id.to_s
  end

  def triads
    EnneagramTriad.all.select { |triad| triad.enneatypes.include?(id) }
  end

  def qualities
    triads.map(&:name)
  end

  def missing_qualities
    all_qualities = EnneagramTriad.all.map(&:name)
    current_qualities = qualities
    all_qualities - current_qualities
  end

  def trigroup_data
    trigroups = EnneagramTrigroup.all

    # Define the order with full titles
    ordered_trigroups = [
      { key: "object-relations", name: "Object Relations" },
      { key: "group-a-validation-through", name: "A - Validation Through" },
      { key: "group-b-getting-your-needs", name: "B - Getting Your Needs" },
      { key: "hornevian-social-style", name: "Hornevian" },
      { key: "group-c-gift", name: "C - Gift" },
      { key: "group-d-movement", name: "D - Movement" },
      { key: "harmonics-handling-stress", name: "Harmonics" },
      { key: "group-e-intellectually", name: "E - Intellectually" },
      { key: "group-f-emotionally", name: "F - Emotionally" }
    ]

    ordered_trigroups.map do |tg_info|
      trigroup = trigroups.find { |tg| tg.id == tg_info[:key] }
      next unless trigroup

      triad_data = trigroup.triad_objects.map do |triad|
        {
          id: triad.id,
          name: triad.name,
          belongs_to: triad.enneatypes.include?(id)
        }
      end

      {
        id: trigroup.id,
        name: tg_info[:name],
        triads: triad_data
      }
    end.compact
  end

  def qualities_with_triads
    triads.map { |triad| { name: triad.name, triad: triad } }
  end

  def missing_qualities_with_triads
    all_triads = EnneagramTriad.all
    current_triad_names = triads.map(&:name)
    missing_triads = all_triads.reject { |triad| current_triad_names.include?(triad.name) }
    missing_triads.map { |triad| { name: triad.name, triad: triad } }
  end
end
