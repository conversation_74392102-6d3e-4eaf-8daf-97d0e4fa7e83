# frozen_string_literal: true

# This is an entry containing ephmeris data (Gate, Line, Color, Tone, Base)
# for a given timestamp in UTC. Only the Sun and Nodes contain info down to Base
# level. All other planetary bodies contain only to Color level.
# The entries are Sun, Moon, NorthNode, Mercury, Venus, Mars, Jupiter, Saturn,
# Neptune, Pluto. There are no entries for Earth or SouthNode as these can be
# extrapolated from the existing data.

class EphemerisEntry < ApplicationRecord
  attribute :ts, :datetime
  attribute :sun_gate, :integer
  attribute :sun_line, :float
  attribute :north_node_gate, :integer
  attribute :north_node_line, :float
  attribute :moon_gate, :integer
  attribute :moon_line, :integer
  attribute :moon_color, :integer
  attribute :mercury_gate, :integer
  attribute :mercury_line, :integer
  attribute :mercury_color, :integer
  attribute :venus_gate, :integer
  attribute :venus_line, :integer
  attribute :venus_color, :integer
  attribute :mars_gate, :integer
  attribute :mars_line, :integer
  attribute :mars_color, :integer
  attribute :jupiter_gate, :integer
  attribute :jupiter_line, :integer
  attribute :jupiter_color, :integer
  attribute :saturn_gate, :integer
  attribute :saturn_line, :integer
  attribute :saturn_color, :integer
  attribute :uranus_gate, :integer
  attribute :uranus_line, :integer
  attribute :uranus_color, :integer
  attribute :neptune_gate, :integer
  attribute :neptune_line, :integer
  attribute :neptune_color, :integer
  attribute :pluto_gate, :integer
  attribute :pluto_line, :integer
  attribute :pluto_color, :integer
end
