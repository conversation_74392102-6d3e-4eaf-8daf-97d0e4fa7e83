class Author < ApplicationRecord
  has_one_attached :profile_picture
  
  has_and_belongs_to_many :books
  has_and_belongs_to_many :ebooks
  has_and_belongs_to_many :cds
  has_and_belongs_to_many :tapes
  has_and_belongs_to_many :audios
  has_and_belongs_to_many :videos
  
  validates :name, presence: true
  
  def self.ransackable_attributes(_auth_object = nil)
    ["biography", "created_at", "email", "id", "name", "updated_at"]
  end
  
  def self.ransackable_associations(_auth_object = nil)
    ["books", "ebooks", "cds", "tapes", "audios", "videos", "profile_picture_attachment", "profile_picture_blob"]
  end
end
