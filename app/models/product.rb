class Product < ApplicationRecord
  has_many :download_links, dependent: :destroy
  has_many :purchased_products
  has_many :users, through: :purchased_products
  has_one_attached :image
  has_one_attached :thumbnail

  accepts_nested_attributes_for :download_links, allow_destroy: true

  validates :title, presence: true

  def self.ransackable_attributes(_auth_object = nil)
    [
      'title',
      'short_description',
      'long_description',
      'price',
      'media_type',
      'media_description',
      'highlights',
      'created_at',
      'updated_at',
      'id',
      'min_price',
      'recommended_price'
    ]
  end

  def self.ransackable_associations(_auth_object = nil)
    ['download_links', 'purchased_products', 'users', 'image_attachment', 'image_blob']
  end
end
