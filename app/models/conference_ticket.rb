class ConferenceTicket < ApplicationRecord
  belongs_to :user
  belongs_to :conference, class_name: 'Hdhd::Conference'
  belongs_to :referrer_speaker, class_name: 'Hdhd::Speaker', optional: true

  validates :user_id, uniqueness: { scope: :conference_id, message: "already has a ticket for this conference" }

  scope :for_year, ->(year) { joins(:conference).where("EXTRACT(YEAR FROM hdhd_conferences.start_date) = ?", year) }
  scope :referred_by, ->(speaker_id) { where(referrer_speaker_id: speaker_id) }
  scope :friends_and_family, -> { where(discount_type: 'friends_and_family') }

  def self.ransackable_attributes(_auth_object = nil)
    ["created_at", "id", "price", "status", "stripe_payment_intent_id", "stripe_session_id",
     "updated_at", "user_id", "conference_id", "referrer_speaker_id", "discount_type", "platform_fee_percentage"]
  end

  def self.ransackable_associations(_auth_object = nil)
    ["user", "conference", "referrer_speaker"]
  end
end
