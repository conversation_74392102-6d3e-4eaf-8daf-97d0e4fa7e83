class Book < CatalogItem
  belongs_to :donor, optional: true
  has_and_belongs_to_many :authors
  
  validates :number_of_pages, numericality: { only_integer: true, greater_than: 0 }, allow_nil: true
  
  def self.ransackable_attributes(_auth_object = nil)
    super + ["number_of_pages", "donor_id"]
  end
  
  def self.ransackable_associations(_auth_object = nil)
    super + ["donor", "authors"]
  end
end
