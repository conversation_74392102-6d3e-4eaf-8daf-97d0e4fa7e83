class DashboardBodygraphService
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def initialize(date = nil, direction = nil, planet = nil, division = nil, gate_or_line_number = nil)
    @date = date
    @direction = direction
    @planet = planet
    @division = division
    @gate_or_line_number = gate_or_line_number
  end

  def call
    # Get the latest EphemerisEntry for the given date
    Rails.logger.debug "Requested date: #{@date}"
    @latest_entry = EphemerisEntry.where('ts <= ?', @date).order(ts: :desc).limit(1).first
    Rails.logger.debug "Requested latest_entry: #{@latest_entry}"

    # Process and adjust for planet and division (as per your logic)
    process_planet_and_division

    # Build the JSON response for the latest entry
    sun_base = base(@latest_entry.sun_line)
    sun_tone = tone(@latest_entry.sun_line)
    sun_color = color(@latest_entry.sun_line)
    north_node_base = base(@latest_entry.north_node_line)
    north_node_tone = tone(@latest_entry.north_node_line)
    north_node_color = color(@latest_entry.north_node_line)

    latest_entry_json = build_latest_entry_json(sun_base, sun_tone, sun_color, north_node_base, north_node_tone, north_node_color)
    Rails.logger.debug "latest_entry_json: #{@latest_entry_json}"

    # Get the next sun line entry
    next_sun_line = @latest_entry.sun_line.floor == 6 ? 1 : @latest_entry.sun_line.floor + 1
    @next_sun_line_entry = EphemerisEntry.where('ts > ?', @date)
                                         .where(sun_line: next_sun_line)
                                         .order(ts: :asc).limit(1).first

    {
      latest_entry_json:,
      next_sun_line_entry: @next_sun_line_entry
    }
  end

  private

  def process_planet_and_division
    return unless @direction.present? && @planet.present? && @division.present?

    # Handle the planet and adjust as needed
    case @planet
    when 'Earth'
      @planet = 'Sun'
      @gate_or_line_number = opposite_gate(@gate_or_line_number) if @division == 'Gate #'
    when 'NorthNode'
      @planet = 'north_node'
    when 'SouthNode'
      @planet = 'north_node'
      @gate_or_line_number = opposite_gate(@gate_or_line_number) if @division == 'Gate #'
    end

    # Determine the qualifier based on the planet and division
    qualifier = "#{@planet.downcase}_#{@division.split(' ').first.downcase}"

    # Set the next_value depending on the division
    @next_value = case @division
                  when 'Gate #'
                    @gate_or_line_number.empty? ? @latest_entry["#{@planet.downcase}_gate"] : @gate_or_line_number
                  when 'Line #'
                    @gate_or_line_number.empty? ? @latest_entry["#{@planet.downcase}_line"] : @gate_or_line_number
                  when 'Gate'
                    step_function = (@direction == 'Next' && @planet != 'north_node') || (@direction != 'Next' && @planet == 'north_node') ? method(:next_gate) : method(:previous_gate)
                    step_function.call(@latest_entry[qualifier])
                  when 'Line'
                    step_function = (@direction == 'Next' && @planet != 'north_node') || (@direction != 'Next' && @planet == 'north_node') ? method(:next_line) : method(:previous_line)
                    step_function.call(@latest_entry[qualifier])
                  end

    # Set the retrograde_next_value depending on the division
    @retrograde_next_value = case @division
                             when 'Gate #'
                               @gate_or_line_number.empty? ? @latest_entry["#{@planet.downcase}_gate"] : @gate_or_line_number
                             when 'Line #'
                               @gate_or_line_number.empty? ? @latest_entry["#{@planet.downcase}_line"] : @gate_or_line_number
                             when 'Gate'
                               retrograde_step_function = (@direction == 'Next' && @planet != 'north_node') || (@direction != 'Next' && @planet == 'north_node') ? method(:previous_gate) : method(:next_gate)
                               retrograde_step_function.call(@latest_entry[qualifier])
                             when 'Line'
                               retrograde_step_function = (@direction == 'Next' && @planet != 'north_node') || (@direction != 'Next' && @planet == 'north_node') ? method(:previous_line) : method(:next_line)
                               retrograde_step_function.call(@latest_entry[qualifier])
                             end

    # Prepare the line_condition based on the division and planet
    @line_condition = ''
    if ['Gate', 'Gate #'].include?(@division)
      @line_condition += if @planet == 'Sun'
                           " AND sun_line = 1.0"
                         elsif @planet == 'north_node'
                           " AND north_node_line >= 6.99"
                         else
                           " AND #{@planet.downcase}_line = 1 AND #{@planet.downcase}_color = 1"
                         end
    elsif ['Line', 'Line #'].include?(@division) && @planet != 'Sun' && @planet != 'north_node'
      @line_condition += " AND #{@planet.downcase}_color = 1"
    end

    # Prepare the retrograde_line_condition based on the division and planet
    @retrograde_line_condition = ''
    if ['Gate', 'Gate #'].include?(@division)
      unless @planet == 'north_node'
        @retrograde_line_condition += if @planet == 'Sun'
                                        " AND sun_line = 6.99"
                                      else
                                        " AND #{@planet.downcase}_line = 6 AND #{@planet.downcase}_color = 6"
                                      end
      end
    elsif ['Line', 'Line #'].include?(@division) && @planet != 'Sun' && @planet != 'north_node'
      @retrograde_line_condition += " AND #{@planet.downcase}_color = 6"
    end

    # Set the operator for the direction (Next or Prev)
    @operator = @direction == 'Next' ? '>=' : '<='

    # Set the timestamp modifier based on the division
    @ts_modifier = case @division
                   when 'Gate #'
                     6.days.to_i
                   when 'Line #'
                     30.hours.to_i
                   else
                     0
                   end
    # Adjust ts_modifier if direction is "Prev"
    @ts_modifier *= -1 if @direction == 'Prev'
  end

  def build_latest_entry_json(sun_base, sun_tone, sun_color, north_node_base, north_node_tone, north_node_color)
    {
      Sun: { g: @latest_entry.sun_gate, l: @latest_entry.sun_line.floor, c: sun_color, t: sun_tone, b: sun_base },
      Earth: { g: opposite_gate(@latest_entry.sun_gate), l: @latest_entry.sun_line.floor, c: sun_color, t: sun_tone, b: sun_base },
      NorthNode: { g: @latest_entry.north_node_gate, l: @latest_entry.north_node_line.floor, c: north_node_color, t: north_node_tone, b: north_node_base },
      SouthNode: { g: opposite_gate(@latest_entry.north_node_gate), l: @latest_entry.north_node_line.floor, c: north_node_color, t: north_node_tone, b: north_node_base },
      Moon: { g: @latest_entry.moon_gate, l: @latest_entry.moon_line, c: @latest_entry.moon_color },
      Mercury: { g: @latest_entry.mercury_gate, l: @latest_entry.mercury_line, c: @latest_entry.mercury_color },
      Venus: { g: @latest_entry.venus_gate, l: @latest_entry.venus_line, c: @latest_entry.venus_color },
      Mars: { g: @latest_entry.mars_gate, l: @latest_entry.mars_line, c: @latest_entry.mars_color },
      Jupiter: { g: @latest_entry.jupiter_gate, l: @latest_entry.jupiter_line, c: @latest_entry.jupiter_color },
      Saturn: { g: @latest_entry.saturn_gate, l: @latest_entry.saturn_line, c: @latest_entry.saturn_color },
      Uranus: { g: @latest_entry.uranus_gate, l: @latest_entry.uranus_line, c: @latest_entry.uranus_color },
      Neptune: { g: @latest_entry.neptune_gate, l: @latest_entry.neptune_line, c: @latest_entry.neptune_color },
      Pluto: { g: @latest_entry.pluto_gate, l: @latest_entry.pluto_line, c: @latest_entry.pluto_color }
    }.to_json
  end

  def color(line_with_remainder)
    remainder = line_with_remainder % 1.0
    (remainder * 6).floor + 1
  end

  def tone(line_with_remainder)
    remainder = line_with_remainder % 1.0
    ((remainder % 0.16666666) * 6).floor + 1
  end

  def base(line_with_remainder)
    base_increment = 0.0055555
    base_number = ((line_with_remainder - 1) / base_increment).to_i + 1
    (base_number % 6).zero? ? 6 : base_number % 6
  end
end
