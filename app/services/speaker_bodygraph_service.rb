class SpeakerBodygraphService
  include <PERSON><PERSON><PERSON><PERSON><PERSON>
  
  def initialize(speaker)
    @speaker = speaker
  end
  
  def call
    return nil unless @speaker.birth_date_utc.present? && @speaker.birth_date_local.present? && @speaker.birth_location.present?
    
    # Extract location data
    location_parts = @speaker.birth_location.split(',').map(&:strip)
    birth_city = location_parts.first
    birth_country = location_parts.last if location_parts.size > 1
    
    # Always calculate the design date to ensure accuracy
    design_date = @speaker.calculate_accurate_design_date
    
    # Calculate activations
    personality_activations = calculate_personality_activations
    design_activations = calculate_design_activations(design_date)
    
    # Calculate bodygraph data
    bodygraph_data = BodygraphData.new(personality_activations, design_activations)
    
    # Return the data needed for the bodygraph
    {
      personality_activations: personality_activations,
      design_activations: design_activations,
      personality_activations_json: build_activations_json(personality_activations),
      design_activations_json: build_activations_json(design_activations),
      birth_city: birth_city,
      birth_country: birth_country,
      aura_type: bodygraph_data.aura_type,
      inner_authority: bodygraph_data.inner_authority,
      definition: bodygraph_data.definition,
      profile: "#{personality_activations[:sun_line].to_i}/#{design_activations[:sun_line].to_i}",
      incarnation_cross: bodygraph_data.incarnation_cross,
      all_activated_gates: bodygraph_data.all_activated_gates
    }
  end
  
  private
  
  def calculate_personality_activations
    return {} unless @speaker.birth_date_utc.present?
    
    # Calculate celestial positions for birth date
    celestial_positions = {}
    birth_date = @speaker.birth_date_utc
    birth_hour = birth_date.hour.to_f + (birth_date.min.to_f / 60) + (birth_date.sec.to_f / 3600)
    jd_birth_date = Swe4r.swe_julday(birth_date.year, birth_date.month, birth_date.day, birth_hour)
    
    # Calculate positions for all celestial bodies
    celestial_bodies = [
      Swe4r::SE_SUN, Swe4r::SE_TRUE_NODE, Swe4r::SE_MOON, 
      Swe4r::SE_MERCURY, Swe4r::SE_VENUS, Swe4r::SE_MARS, 
      Swe4r::SE_JUPITER, Swe4r::SE_SATURN, Swe4r::SE_URANUS, 
      Swe4r::SE_NEPTUNE, Swe4r::SE_PLUTO
    ]
    
    celestial_bodies.each do |body|
      celestial_positions[body] = Swe4r.swe_calc_ut(jd_birth_date, body, Swe4r::SEFLG_MOSEPH)
    end
    
    # Use HDKit to generate activations
    hdkit = Hdkit.new
    hdkit.generate_activations(celestial_positions)
  end
  
  def calculate_design_activations(design_date = nil)
    # Use the provided design date or fall back to the speaker's design_date_utc
    design_date ||= @speaker.design_date_utc
    return {} unless design_date.present?
    
    # Calculate celestial positions for design date
    celestial_positions = {}
    design_hour = design_date.hour.to_f + (design_date.min.to_f / 60) + (design_date.sec.to_f / 3600)
    jd_design_date = Swe4r.swe_julday(design_date.year, design_date.month, design_date.day, design_hour)
    
    # Calculate positions for all celestial bodies
    celestial_bodies = [
      Swe4r::SE_SUN, Swe4r::SE_TRUE_NODE, Swe4r::SE_MOON, 
      Swe4r::SE_MERCURY, Swe4r::SE_VENUS, Swe4r::SE_MARS, 
      Swe4r::SE_JUPITER, Swe4r::SE_SATURN, Swe4r::SE_URANUS, 
      Swe4r::SE_NEPTUNE, Swe4r::SE_PLUTO
    ]
    
    celestial_bodies.each do |body|
      celestial_positions[body] = Swe4r.swe_calc_ut(jd_design_date, body, Swe4r::SEFLG_MOSEPH)
    end
    
    # Use HDKit to generate activations
    hdkit = Hdkit.new
    hdkit.generate_activations(celestial_positions)
  end
  
  def build_activations_json(activations)
    return {} if activations.empty?
    
    # Calculate base, tone, and color for sun and north node
    sun_base = base(activations[:sun_line])
    sun_tone = tone(activations[:sun_line])
    sun_color = color(activations[:sun_line])
    north_node_base = base(activations[:north_node_line])
    north_node_tone = tone(activations[:north_node_line])
    north_node_color = color(activations[:north_node_line])
    
    # Build JSON structure similar to what's used in the bodygraph partial
    {
      Sun: { g: activations[:sun_gate], l: activations[:sun_line].to_i, c: sun_color, t: sun_tone, b: sun_base },
      Earth: { g: activations[:earth_gate], l: activations[:sun_line].to_i, c: sun_color, t: sun_tone, b: sun_base },
      NorthNode: { g: activations[:north_node_gate], l: activations[:north_node_line].to_i, c: north_node_color, t: north_node_tone, b: north_node_base },
      SouthNode: { g: activations[:south_node_gate], l: activations[:north_node_line].to_i, c: north_node_color, t: north_node_tone, b: north_node_base },
      Moon: { g: activations[:moon_gate], l: activations[:moon_line].to_i, c: activations[:moon_color] },
      Mercury: { g: activations[:mercury_gate], l: activations[:mercury_line].to_i, c: activations[:mercury_color] },
      Venus: { g: activations[:venus_gate], l: activations[:venus_line].to_i, c: activations[:venus_color] },
      Mars: { g: activations[:mars_gate], l: activations[:mars_line].to_i, c: activations[:mars_color] },
      Jupiter: { g: activations[:jupiter_gate], l: activations[:jupiter_line].to_i, c: activations[:jupiter_color] },
      Saturn: { g: activations[:saturn_gate], l: activations[:saturn_line].to_i, c: activations[:saturn_color] },
      Uranus: { g: activations[:uranus_gate], l: activations[:uranus_line].to_i, c: activations[:uranus_color] },
      Neptune: { g: activations[:neptune_gate], l: activations[:neptune_line].to_i, c: activations[:neptune_color] },
      Pluto: { g: activations[:pluto_gate], l: activations[:pluto_line].to_i, c: activations[:pluto_color] }
    }
  end
  
  def color(line_with_remainder)
    remainder = line_with_remainder % 1.0
    (remainder * 6).floor + 1
  end

  def tone(line_with_remainder)
    remainder = line_with_remainder % 1.0
    ((remainder % 0.16666666) * 6).floor + 1
  end

  def base(line_with_remainder)
    base_increment = 0.0055555
    base_number = ((line_with_remainder - 1) / base_increment).to_i + 1
    (base_number % 6).zero? ? 6 : base_number % 6
  end
end
