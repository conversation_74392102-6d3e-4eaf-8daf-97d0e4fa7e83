/* @import "tailwindcss/base";
@import "tailwindcss/utilities";
@import "tailwindcss/components"; */

@font-face {
  font-family: Proxima Sans;
  src: url(/assets/ProximaSans.ttf);
}

body {
  /*  font-family: Proxima <PERSON>, '<PERSON> Sans', '<PERSON> Sans MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif; */
}

/* Apply the fading and shrinking animation */
.flash-message {
  animation: fadeOut 3s ease-out forwards;
}

/* Define the animation to fade out and shrink */
@keyframes fadeOut {
  0% {
    opacity: 1;
    height: auto;
    padding: 10px;  /* or whatever the default padding is */
    margin: 10px 0; /* adjust based on your layout */
  }
  80% {
    opacity: 1;
    height: auto;
    padding: 10px;
    margin: 10px 0;
  }
  100% {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
  }
}

/* Note: We're using Tailwind's built-in aspect ratio utilities instead of custom ones */

/* Event type colors */
.event-type-talk {
  @apply bg-blue-100 text-blue-800 border-blue-200;
}

.event-type-workshop {
  @apply bg-green-100 text-green-800 border-green-200;
}

.event-type-performance {
  @apply bg-purple-100 text-purple-800 border-purple-200;
}

.event-type-special_event {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.event-type-interview {
  @apply bg-indigo-100 text-indigo-800 border-indigo-200;
}

.event-type-panel {
  @apply bg-pink-100 text-pink-800 border-pink-200;
}

