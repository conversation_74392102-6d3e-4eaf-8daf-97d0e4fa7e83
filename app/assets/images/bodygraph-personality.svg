<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 279.3 1243.2" style="enable-background:new 0 0 279.3 1243.2;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:none;}
	.st2{fill:#383838;}
	.st3{font-family:'Roboto';}
	.st4{font-size:33.3198px;}
	.st5{letter-spacing:1;}
</style>
<g id="Personality">
	<!-- outline -->
	<!-- <g id="Rectangle">
		<rect x="22" y="19.5" class="st0" width="235.2" height="1204.2"/>
		<path d="M256.8,20v1203.2H22.5V20H256.8 M257.7,19H21.6v1205.1h236.2V19L257.7,19z"/>
	</g> -->
	<rect x="133.9" y="131.9" class="st1" width="96" height="41.5"/>
	<text id="Sun" transform="matrix(1 0 0 1 133.8613 155.4133)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="215.9" class="st1" width="96" height="41.5"/>
	<text id="Earth" transform="matrix(1 0 0 1 133.8613 239.4114)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="299.9" class="st1" width="96" height="41.5"/>
	<text id="Moon" transform="matrix(1 0 0 1 133.8613 323.4065)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="378" class="st1" width="96" height="41.5"/>
	<text id="NorthNode" transform="matrix(1 0 0 1 133.8613 401.5149)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="467.9" class="st1" width="96" height="41.5"/>
	<text id="SouthNode" transform="matrix(1 0 0 1 133.8613 491.4026)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="551.9" class="st1" width="96" height="41.5"/>
	<text id="Mercury" transform="matrix(1 0 0 1 133.8613 575.3977)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="635.9" class="st1" width="96" height="41.5"/>
	<text id="Venus" transform="matrix(1 0 0 1 133.8613 659.3958)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="719.9" class="st1" width="96" height="41.5"/>
	<text id="Mars" transform="matrix(1 0 0 1 133.8613 743.3909)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="803.9" class="st1" width="96" height="41.5"/>
	<text id="Jupiter" transform="matrix(1 0 0 1 133.8613 827.3889)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="887.9" class="st1" width="96" height="41.5"/>
	<text id="Saturn" transform="matrix(1 0 0 1 133.8613 911.384)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="971.9" class="st1" width="96" height="41.5"/>
	<text id="Uranus" transform="matrix(1 0 0 1 133.8613 995.3821)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="1055.9" class="st1" width="96" height="41.5"/>
	<text id="Neptune" transform="matrix(1 0 0 1 133.8613 1079.3772)" class="st2 st3 st4 st5"></text>
	<rect x="133.9" y="1139.9" class="st1" width="96" height="41.5"/>
	<text id="Pluto" transform="matrix(1 0 0 1 133.8613 1163.3752)" class="st2 st3 st4 st5"></text>
	<g id="Icons">
	<!-- Uranus -->
		<path transform="translate(0,-90)" class="st2" d="M100.3,992.7c-2.9,0-5.9,0-8.9,0c0-4.5,0-8.9,0-13.4c-3.7,0-7.3,0-11,0c0,3.8,0,7.5,0,11.2
			c0,0.3,0.6,0.7,1,0.9c2.1,1.3,3.2,3.2,3,5.7c-0.2,2.5-1.5,4.3-3.8,5.2c-2.3,0.9-4.4,0.5-6.2-1.1c-3.1-2.7-2.6-7.7,1-9.7
			c0.8-0.4,1.1-0.9,1-1.7c0-3.1,0-6.2,0-9.3c0-0.4,0-0.7-0.1-1.2c-3.6,0-7.1,0-10.8,0c0,4.4,0,8.8,0,13.3c-3,0-5.9,0-9,0
			c0-0.6-0.1-1.3,0-2c0-0.3,0.4-0.6,0.7-0.7c1.1-0.4,2.2-0.7,3.4-0.9c0.7-0.1,0.9-0.4,0.9-1.1c0-7.1,0-14.3,0-21.4
			c0-0.3-0.4-0.8-0.8-1c-0.8-0.3-1.6-0.5-2.4-0.7c-2-0.6-2.1-0.6-1.6-2.9c2.9,0,5.8,0,8.8,0c0,4.5,0,8.8,0,13.3c3.7,0,7.2,0,10.9,0
			c0-4.4,0-8.8,0-13.3c1.4,0,2.6,0,4,0c0,4.4,0,8.8,0,13.3c3.7,0,7.3,0,11,0c0-4.4,0-8.8,0-13.4c3.1,0,6,0,9,0c0,0.8,0,1.7,0,2.5
			c-1.4,0.4-2.8,0.8-4.2,1.2c-0.4,0.1-0.8,0.6-0.8,1c0,7.2,0,14.3,0,21.5c0,0.3,0.2,0.8,0.4,0.9c1.5,0.4,3.1,0.7,4.6,1.1
			C100.3,990.9,100.3,991.8,100.3,992.7z M80.4,996.8c0-1.1-0.9-2.1-2-2.1c-1.1,0-2,0.8-2.1,1.9c-0.1,1.2,0.8,2.1,2,2.2
			C79.4,998.8,80.4,997.9,80.4,996.8z"/>
		<!-- Mercury -->
		<path transform="translate(0,-48)" class="st2" d="M93.8,536.9c0.1,5.3-2.2,9.4-6.4,12.6c-0.4,0.3-0.8,0.6-1.3,0.9c0.4,0.3,0.6,0.5,0.9,0.7
			c5.2,3.6,7.8,9.7,6.5,15.9c-1.2,6.2-5.9,10.9-12.2,12.1c-0.8,0.2-1.2,0.4-1.1,1.3c0.1,0.6,0,1.1,0,1.8c1.6,0,3.1,0,4.7,0
			c0,1.4,0,2.6,0,4c-1.5,0-3,0-4.6,0c0,1.6,0,3.1,0,4.7c-1.4,0-2.6,0-4,0c0-1.2,0-2.4,0-3.5c0-0.8-0.2-1.1-1.1-1.1
			c-1.2,0.1-2.3,0-3.6,0c0-1.3,0-2.6,0-4c1.5,0,3,0,4.6,0c0-0.9,0.1-1.6,0-2.4c-0.1-0.3-0.6-0.7-0.9-0.7c-6.8-1.5-11.5-6.3-12.4-13
			c-0.8-6.1,1.4-11.1,6.3-14.8c0.4-0.3,0.7-0.5,1.3-0.9c-5-3.2-7.7-7.7-7.8-13.6c1.3,0,2.7,0,4,0c-0.4,7.3,7.1,13.1,14.4,11.2
			c5.2-1.4,9-6.4,8.5-11.2C91.1,536.9,92.4,536.9,93.8,536.9z M89.9,564.1c0-6.4-5.1-11.6-11.4-11.6c-6.4,0-11.6,5.1-11.6,11.5
			c0,6.3,5.2,11.5,11.5,11.5C84.7,575.5,89.8,570.4,89.9,564.1z"/>
			<!-- Neptune -->
		<path transform="translate(0,-100)" class="st2" d="M87.5,1052.9c-0.8,0.8-1.5,1.7-2.3,2.5c-0.8-0.8-1.5-1.6-2.2-2.3c2.1-2.1,4.3-4.3,6.4-6.4
			c2.1,2.1,4.3,4.3,6.6,6.6c-0.7,0.6-1.5,1.3-2.4,2.2c-0.7-0.8-1.4-1.7-2.2-2.6c-0.1,0.1-0.2,0.1-0.3,0.2c0,0.3-0.1,0.6-0.1,0.9
			c0,2.4,0,4.8,0,7.2c-0.1,6-4.1,11-10,12.4c-0.3,0.1-0.7,0.1-1.1,0.2c0,1.9,0,3.8,0,5.7c1.3,0,2.5,0,3.7,0c0,1.1,0,2.1,0,3.2
			c-1.2,0-2.5,0-3.8,0c0,1.3,0,2.6,0,3.9c-1.1,0-2.1,0-3.2,0c0-1.2,0-2.5,0-3.8c-1.4,0-2.6,0-3.9,0c0-1.1,0-2.1,0-3.2
			c1.3,0,2.5,0,3.9,0c0-1.8,0-3.6,0-5.3c0-0.2-0.5-0.5-0.9-0.6c-5.1-1.2-8.5-4.3-9.8-9.4c-0.4-1.4-0.4-2.9-0.4-4.3
			c-0.1-2.3,0-4.5,0-7.1c-1,1.1-1.7,2-2.5,2.9c-0.9-1-1.6-1.8-2.2-2.5c2.1-2.1,4.3-4.3,6.4-6.4c2.1,2.1,4.3,4.3,6.6,6.5
			c-0.7,0.6-1.4,1.3-2.2,2.1c-0.7-0.8-1.5-1.7-2.5-2.7c-0.1,0.6-0.2,0.8-0.2,1c0,2.6,0,5.2,0,7.8c0.1,4.3,3.2,8,7.3,8.9
			c0.1,0,0.3,0,0.5,0c0-8,0-15.9,0-24.2c-0.9,1.1-1.6,2-2.4,3c-1-1.1-1.7-1.9-2.2-2.5c2.1-2.1,4.3-4.3,6.5-6.5c2,2,4.2,4.2,6.4,6.5
			c-0.7,0.6-1.5,1.3-2.3,2.1c-0.7-0.8-1.5-1.6-2.5-2.6c0,8.2,0,16.2,0,24.3c0.4-0.1,0.8-0.2,1.2-0.3c3.7-1.2,6.4-4.6,6.5-8.6
			c0.1-2.8,0-5.6,0-8.4C87.7,1053,87.6,1052.9,87.5,1052.9z"/>
			<!-- Jupiter -->
		<path transform="translate(0,-76)" class="st2" d="M86.5,792.5c1.4,0,2.6,0,3.9,0c0,9.4,0,18.7,0,28.1c1.8,0,3.5,0,5.3,0c0,1.4,0,2.7,0,4.1c-1.7,0-3.4,0-5.2,0
			c0,2.8,0,5.4,0,8.1c-1.3,0-2.6,0-4,0c0-2.6,0-5.3,0-8.1c-8,0-15.8,0-23.7,0c0-1.4,0-2.6,0-3.8c1.5-0.3,3-0.4,4.5-0.9
			c6.4-2,9.7-6.7,11.1-13c0.4-1.8,0.4-3.7,0.3-5.5c-0.3-3.7-3.4-6.4-7-6.4c-3.8,0.1-6.5,2.7-6.8,6.5c-0.1,1.4,0.1,2.6,1.4,3.4
			c0.2,0.1,0.4,0.5,0.4,0.7c0,1,0,2,0,3.1c-1.5,0.3-2.8,0-3.7-1.1c-0.8-1-1.6-2.2-1.8-3.5c-1.3-6.3,2.8-12.1,8.9-13
			c6.3-0.9,12.1,3.6,12.6,9.9c0.6,7.3-1.8,13.6-7.2,18.7c-0.2,0.2-0.4,0.3-0.5,0.7c3.8,0,7.5,0,11.5,0
			C86.5,811.2,86.5,801.9,86.5,792.5z"/>
			<!-- Earth -->
		<path transform="translate(0,-5)" class="st2" d="M75.9,245.3c-9.7,0-17.6-7.9-17.6-17.6c0-9.7,7.9-17.6,17.6-17.6c9.7,0,17.6,7.9,17.6,17.6
			C93.5,237.4,85.6,245.3,75.9,245.3z M88.6,225.4c-0.5-5.1-5.7-10.2-10.5-10.4c0,3.3,0,6.5,0,9.8c0,0.2,0.5,0.6,0.7,0.6
			C82.1,225.4,85.3,225.4,88.6,225.4z M73.7,215c-5.5,0.2-10.6,6.4-10.4,10.4c3.2,0,6.4,0,9.6,0c0.3,0,0.8-0.5,0.8-0.7
			C73.7,221.4,73.7,218.2,73.7,215z M78.2,230c0,3.5,0,6.9,0,10.4c5.4-0.4,10.3-6.3,10.3-10.4C85.1,230,81.7,230,78.2,230z
			 M73.5,240.3c0-3.4,0-6.9,0-10.4c-3.3,0-6.4,0-9.5,0c-0.3,0-0.6,0.1-0.8,0.1C63.3,234.7,68.9,240.2,73.5,240.3z"/>
		<!-- Moon -->
<path transform="translate(0,-27)" class="st2" d="M61.4,489.9c1.7-0.5,3.4-1.1,5-1.6c6.3-2.1,10.5-8,10.5-14.8c0-6.8-4.2-12.7-10.5-14.8
    c-1.7-0.6-3.3-1.1-5.1-1.6c11.1-5.1,21-2.6,26.5,6.9c6.5,11.2,0.2,25.6-12.3,28.3c-2.4,0.5-4.9,0.6-7.3,0c-2.3-0.6-4.5-1.3-6.8-2
    C61.4,490.1,61.4,490,61.4,489.9z M73,488.8c7.4,0.1,14-7.7,13.7-15.9c-0.3-7.9-7.2-15.4-13.7-14.8c4.8,4,7.3,9.1,7.3,15.3
    C80.5,479.6,77.9,484.8,73,488.8z"/>
<!-- Mars -->
		<path transform="translate(0,-68)" class="st2" d="M85,722.2c1.5-1.5,3.1-3,4.8-4.6c-1.6,0-2.9,0-4.4,0c0-1.4,0-2.6,0-3.9c3.7,0,7.5,0,11.3,0
			c0,3.8,0,7.5,0,11.3c-1.2,0-2.5,0-3.9,0c0-1.4,0-2.8,0-4.1c-1.5,1.5-3,3-4.6,4.6c5.4,7.8,2.7,17-3.2,21.5
			c-6.1,4.6-14.9,4.1-20.3-1.3c-5.5-5.4-6.1-14.2-1.5-20.3C67.7,719.3,77,716.6,85,722.2z M75.7,723.1c-6.4,0-11.6,5.1-11.6,11.4
			c0,6.4,5.1,11.6,11.4,11.6c6.4,0.1,11.6-5,11.7-11.5C87.2,728.4,82,723.2,75.7,723.1z"/>
<!-- Venus -->
		<path transform="translate(0,-60)" class="st2" d="M80.4,672.3c-1.4,0-2.6,0-4.1,0c0-1.6,0-3.1,0-4.7c-1.7,0-3.1,0-4.6,0c0-1.3,0-2.6,0-4c1.5,0,3,0,4.5,0
			c0-1,0-1.9,0-2.8c-5.1-0.9-9-3.5-11.5-8c-1.8-3.3-2.3-6.9-1.6-10.6c1.4-7.3,8-12.5,15.6-12.3c7.5,0.2,13.7,5.7,14.9,13.1
			c1.1,6.7-2.7,15.9-13.3,17.9c0,0.8,0,1.7,0,2.7c1.5,0,3,0,4.5,0c0,1.4,0,2.6,0,4c-1.5,0-2.9,0-4.5,0
			C80.4,669.3,80.4,670.7,80.4,672.3z M78.3,656.9c6.4,0,11.5-5.1,11.6-11.4c0.1-6.4-5.1-11.6-11.4-11.6c-6.4,0-11.6,5-11.7,11.4
			C66.8,651.7,71.9,656.9,78.3,656.9z"/>
<!-- Saturn -->
		<path transform="translate(0,-87)" class="st2" d="M89.3,914.7c-1.2,2.6-3.7,3.9-6.2,3.3c-2.3-0.5-4-2.7-3.9-5.5c0-2.4,1-4.5,2.5-6.3c1-1.2,2-2.4,2.9-3.6
			c2.2-3,2.7-6.5,1.9-10c-0.7-3.1-3.7-4.7-6.7-3.6c-2.3,0.8-3.9,2.4-5.1,4.5c-0.2,0.4-0.3,1-0.3,1.6c0,3.7,0,7.4,0,11.1
			c0,0.5,0,1,0,1.6c-1.3,0-2.5,0-3.9,0c0-8.1,0-16.3,0-24.5c-1.6,0-3.1,0-4.6,0c0-1.3,0-2.6,0-4c1.5,0,2.9,0,4.6,0
			c0-1.2,0-2.3,0-3.4c1.3,0,2.5,0,3.9,0c0,1.1,0,2.2,0,3.4c1.6,0,3.1,0,4.6,0c0,1.3,0,2.5,0,3.9c-1.5,0-2.9,0-4.5,0
			c0,1.5,0,2.8,0,4.3c0.4-0.3,0.8-0.5,1.1-0.7c3.2-2.1,6.6-2.7,10.1-1c3.5,1.7,4.8,4.8,5.1,8.5c0.3,4.4-0.9,8.4-3.7,11.8
			c-0.9,1.1-1.8,2.1-2.6,3.3c-0.6,0.9-1,2-1.2,3.1c-0.1,0.5,0.4,1.2,0.6,1.7c0.5-0.2,1.1-0.4,1.6-0.7c0.2-0.1,0.3-0.3,0.5-0.5
			C87.1,913.5,88.2,914.1,89.3,914.7z"/>
		
		<!-- Pluto -->
		<g transform="translate(0,-122)">
			<path class="st2" d="M62.9,1144.6c1.4,0,2.6,0,3.8,0c1.5,7.4,5.4,11.3,11.4,11.4c3.2,0,6-1.1,8.3-3.3c2.3-2.2,3.4-4.9,3.5-8.1
				c1.3,0,2.6,0,3.8,0c0.8,6.7-4.4,12.7-13.3,15.3c0,2.1,0,4.1,0,6.3c1.9,0,3.7,0,5.6,0c0,1.4,0,2.6,0,4c-1.9,0-3.7,0-5.7,0
				c0,2.3,0,4.5,0,6.8c-1.4,0-2.6,0-3.9,0c0-2.2,0-4.4,0-6.7c-2,0-3.8,0-5.7,0c0-1.4,0-2.7,0-4.1c1.5,0,3-0.1,4.5,0
				c1,0,1.2-0.3,1.2-1.2c-0.1-1.3-0.1-2.7,0-4c0-0.9-0.2-1.2-1.1-1.4c-6.7-1.4-11.3-6.4-12.3-13.1
				C62.9,1145.9,62.9,1145.3,62.9,1144.6z"/>
			<path class="st2" d="M68.6,1144.4c0-5.4,4.4-9.8,9.8-9.7c5.4,0,9.8,4.4,9.7,9.8c0,5.4-4.4,9.7-9.8,9.7
				C72.9,1154.2,68.6,1149.8,68.6,1144.4z M78.4,1150.2c3.2,0,5.9-2.6,5.8-5.8c0-3.1-2.7-5.8-5.8-5.7c-3.2,0-5.7,2.6-5.7,5.8
				C72.6,1147.7,75.2,1150.2,78.4,1150.2z"/>
		</g>

		<!-- Sun -->
		<g>
			<path class="st2" d="M75.8,165.4c-9.7,0-17.6-7.9-17.5-17.7c0-9.7,7.9-17.5,17.7-17.5c9.7,0,17.6,8,17.5,17.7
				C93.4,157.7,85.5,165.5,75.8,165.4z M75.8,160.9c7.2,0,13.1-5.8,13.1-13c0-7.2-5.9-13.2-13.2-13.1c-7.2,0-13.1,5.9-13.1,13.1
				C62.8,155,68.6,160.9,75.8,160.9z"/>
			<path class="st2" d="M78.1,147.8c0,1.2-1,2.3-2.3,2.3c-1.2,0-2.2-0.9-2.2-2.1c-0.1-1.3,0.9-2.3,2.2-2.3
				C77.1,145.6,78.1,146.6,78.1,147.8z"/>
		</g>
		<!-- North Node -->
		<g id="lqlFrc_8_" transform="translate(0,-100)">
			<g>
				<path class="st2" d="M62,401.8c-4.8-9-1.8-19,4.9-24.1c6.7-5.2,16.1-5.3,23-0.1c6.5,4.9,10.1,14.8,5.1,24.2c0.3,0.1,0.6,0.2,0.8,0.2
					c3.8,0.9,6.4,4.4,6,8.3c-0.4,4-3.5,7-7.5,7.1c-3.9,0.1-7.3-2.7-7.9-6.5c-0.4-2.8,0.5-5.1,2.5-7.1c4.4-4.2,6-9.4,4.4-15.3
					c-1.6-5.9-5.6-9.7-11.7-10.9c-10.2-2.1-19.3,6.2-18.3,16.6c0.4,3.8,1.9,7,4.9,9.5c2.6,2.3,3.5,6,2,9.1c-1.4,3.2-4.7,5-8.1,4.6
					c-3.4-0.4-6.1-3.1-6.7-6.5c-0.7-4,2-8,6-8.9C61.5,401.9,61.8,401.9,62,401.8z M58.8,409.6c0,2.4,1.9,4.3,4.3,4.3
					c2.3,0,4.3-2,4.3-4.3c0-2.3-1.9-4.3-4.2-4.3C60.7,405.3,58.8,407.2,58.8,409.6z M98.3,409.6c0-2.3-1.9-4.3-4.3-4.3
					c-2.4,0-4.3,2-4.3,4.3c0,2.3,2,4.3,4.3,4.3C96.4,413.9,98.3,411.9,98.3,409.6z"/>
			</g>
		</g>
		<!-- South Node -->
		<g id="lqlFrc_7_" transform="translate(0,-100)">
			<g>
				<path class="st2" d="M95,465.2c4.8,9,1.8,19-4.9,24.1c-6.7,5.2-16.1,5.3-23,0.1c-6.5-4.9-10.1-14.8-5.1-24.2c-0.3-0.1-0.6-0.2-0.8-0.2
					c-3.8-0.9-6.4-4.4-6-8.3c0.4-4,3.5-7,7.5-7.1c3.9-0.1,7.3,2.7,7.9,6.5c0.4,2.8-0.5,5.1-2.5,7.1c-4.4,4.2-6,9.4-4.4,15.3
					c1.6,5.9,5.6,9.7,11.7,10.9c10.2,2.1,19.3-6.2,18.3-16.6c-0.4-3.8-1.9-7-4.9-9.5c-2.6-2.3-3.5-6-2-9.1c1.4-3.2,4.7-5,8.1-4.6
					c3.4,0.4,6.1,3.1,6.7,6.5c0.7,4-2,8-6,8.9C95.5,465.1,95.3,465.2,95,465.2z M98.3,457.5c0-2.4-1.9-4.3-4.3-4.3
					c-2.3,0-4.3,2-4.3,4.3c0,2.3,1.9,4.3,4.2,4.3C96.3,461.8,98.3,459.8,98.3,457.5z M58.7,457.5c0,2.3,1.9,4.3,4.3,4.3
					c2.4,0,4.3-2,4.3-4.3c0-2.3-2-4.3-4.3-4.3C60.7,453.1,58.7,455.1,58.7,457.5z"/>
			</g>
		</g>
	</g>
	<rect x="27.9" y="55.7" class="st1" width="222.4" height="65.6"/>
	<!-- <text transform="matrix(1 0 0 1 46.1836 79.1662)" class="st2 st3 st4 st5">Personality</text> -->
</g>
</svg>
