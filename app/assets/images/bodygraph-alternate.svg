<svg data-v-de5d35fc="" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="190 0 1600 2000" width="100%" height="100%" xml:space="preserve" data-dom-ref-id="bodygraph" data-element-type="bodygraph" data-bodygraph-type="bodygraph" class="" id="bodygraph" style="height: 500px;">
  <defs data-v-de5d35fc="">
    <radialGradient data-v-de5d35fc="" id="circleBg">
      <stop data-v-de5d35fc="" offset="0" stop-color="#000000" stop-opacity="0.7"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#000000" stop-opacity="0.1"></stop>
    </radialGradient>
    <radialGradient data-v-de5d35fc="" id="circleRadials" gradientUnits="userSpaceOnUse">
      <stop data-v-de5d35fc="" offset="0" stop-color="#FFFFFF" stop-opacity="0"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#FFFFFF" stop-opacity="0.1"></stop>
    </radialGradient>
    <linearGradient data-v-de5d35fc="" id="figureGradient--dark" gradientUnits="userSpaceOnUse" x1="269.9971" y1="1033.0195" x2="1705.7881" y2="1033.0195">
      <stop data-v-de5d35fc="" offset="0" stop-opacity="1" style="stop-color: rgb(18, 18, 18);"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-opacity="1" style="stop-color: rgb(18, 18, 18);"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-opacity="1" style="stop-color: rgb(18, 18, 18);"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="figureGradient--light1" gradientUnits="userSpaceOnUse" x1="269.9971" y1="1033.0195" x2="1705.7881" y2="1033.0195">
      <stop data-v-de5d35fc="" data-v-f9710960="" offset="0" stop-opacity="0" style="stop-color: rgb(0, 0, 0);"></stop>
      <stop data-v-de5d35fc="" data-v-f9710960="" offset="0.5" stop-opacity="1" style="stop-color: black;"></stop>
      <stop data-v-de5d35fc="" data-v-f9710960="" offset="1" stop-opacity="0" style="stop-color: rgb(0, 0, 0);"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="figureGradientMandala--dark" gradientUnits="userSpaceOnUse" x1="269.9971" y1="1033.0195" x2="1705.7881" y2="1033.0195">
      <stop data-v-de5d35fc="" offset="0" stop-opacity=".8"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-opacity="1" style="stop-color: rgb(23, 23, 23);"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-opacity=".8"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="figureGradient--light" gradientUnits="userSpaceOnUse" x1="20" y1="1033.0195" x2="2000" y2="1033.0195">
      <stop data-v-de5d35fc="" offset="0" stop-opacity="1" style="stop-color: rgb(255, 255, 255);"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-opacity="1" style="stop-color: rgb(255, 255, 255);"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-opacity="1" style="stop-color: rgb(255, 255, 255);"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="figureGradientMandala--light" gradientUnits="userSpaceOnUse" x1="269.9971" y1="1033.0195" x2="1705.7881" y2="1033.0195">
      <stop data-v-de5d35fc="" offset="0" stop-opacity="0.7" style="stop-color: rgb(255, 255, 255);"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-opacity="1" style="stop-color: rgb(255, 255, 255);"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-opacity="0.7" style="stop-color: rgb(255, 255, 255);"></stop>
    </linearGradient>
    <filter data-v-de5d35fc="" id="gateDropShadow" x="0" y="0" width="200%" height="200%">
      <feGaussianBlur data-v-de5d35fc="" stdDeviation="15" result="blur" in="SourceAlpha"></feGaussianBlur>
      <feOffset data-v-de5d35fc="" result="offsetBlurredAlpha" in="blur" dy="10" dx="20"></feOffset>
      <feComponentTransfer data-v-de5d35fc="">
        <feFuncA data-v-de5d35fc="" type="linear" slope="0.3"></feFuncA>
      </feComponentTransfer>
      <feMerge data-v-de5d35fc="">
        <feMergeNode data-v-de5d35fc=""></feMergeNode>
        <feMergeNode data-v-de5d35fc="" in="SourceGraphic"></feMergeNode>
      </feMerge>
    </filter>
    <radialGradient data-v-de5d35fc="" id="centerUndefined--dark">
      <stop data-v-de5d35fc="" offset="0" stop-color="#212223"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-color="#212223"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#212223"></stop>
    </radialGradient>
    <radialGradient data-v-de5d35fc="" id="centerUndefined--light">
      <stop data-v-de5d35fc="" offset="0" stop-color="#ffffff" stop-opacity="0"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-color="#f0f0f0" stop-opacity=".1"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#f0f0f0" stop-opacity=".8"></stop>
    </radialGradient>
    <radialGradient data-v-de5d35fc="" id="centerUndefined--light1">
      <stop data-v-de5d35fc="" offset="0" stop-color="#FFFFFF" stop-opacity=".4"></stop>
      <stop data-v-de5d35fc="" offset="0.5" stop-color="#FFFFFF" stop-opacity=".2"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#FFFFFF" stop-opacity="0"></stop>
    </radialGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedComposite" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#6182A0" stop-opacity=".9"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#6182A0" stop-opacity=".9"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedBrown" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#57423E"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#57423E" stop-opacity="1"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedRed" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#D04A49"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#D04A49" stop-opacity="1"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedYellow" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#FBF7AD"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#FBF7AD" stop-opacity="1"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedGreen" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#669A8D"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#669A8D" stop-opacity="1"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedHighlight" gradientTransform="rotate(90)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#FFFFFF" stop-opacity=".15"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#FFFFFF" stop-opacity="0"></stop>
    </linearGradient>
    <linearGradient data-v-de5d35fc="" id="centerDefinedYellowHighlight" gradientTransform="rotate(0)">
      <stop data-v-de5d35fc="" offset="0" stop-color="#000000" stop-opacity=".1"></stop>
      <stop data-v-de5d35fc="" offset="1" stop-color="#000000" stop-opacity="0"></stop>
    </linearGradient>
    <filter data-v-de5d35fc="" id="centerDropShadow" x="0" y="0" width="200%" height="200%" color-interpolation-filters="sRGB">
      <feOffset data-v-de5d35fc="" result="offOut" in="SourceAlpha" dx="55" dy="55"></feOffset>
      <feGaussianBlur data-v-de5d35fc="" result="blurOut" in="offOut" stdDeviation="18"></feGaussianBlur>
      <feOffset data-v-de5d35fc="" result="offOut1" in="SourceGraphic" dx="40" dy="40"></feOffset>
      <feBlend data-v-de5d35fc="" in="offOut1" in2="blurOut" mode="normal"></feBlend>
    </filter>
    <filter data-v-de5d35fc="" id="generalblur" x="0" y="0">
      <feGaussianBlur data-v-de5d35fc="" in="SourceGraphic" stdDeviation="15"></feGaussianBlur>
    </filter>
    <mask data-v-de5d35fc="" id="g26Mask" maskUnits="userSpaceOnUse">
      <rect data-v-de5d35fc="" x="100" y="1100" width="1100" height="350" fill="white"></rect>
      <path data-v-de5d35fc="" fill="black" d="M1029.999,1196.601v115.898c0,7.456,6.045,13.502,13.501,13.502
        c7.454,0,13.499-6.046,13.499-13.502v-142.896c-0.069,0.069-0.146,0.146-0.213,0.214L1029.999,1196.601z"></path>
      <path data-v-de5d35fc="" fill="black" d="M973.999,1215.757v96.22c0,7.456,6.043,13.502,13.5,13.502c7.455,0,13.5-6.046,13.5-13.502
        v-96.109C992.171,1218.294,982.812,1218.261,973.999,1215.757z"></path>
      <path data-v-de5d35fc="" fill="black" d="M917.999,1169.199v143.3c0,7.456,6.045,13.502,13.501,13.502s13.5-6.046,13.5-13.502
        v-116.302l-26.387-26.385C918.416,1169.615,918.202,1169.402,917.999,1169.199z"></path>
    </mask>
    <mask data-v-de5d35fc="" id="g34Mask" maskUnits="userSpaceOnUse">
      <rect data-v-de5d35fc="" x="400" y="600" width="500" height="1000" fill="white"></rect>
      <path data-v-de5d35fc="" fill="black" d="M845.856,1288.973c-1.313-7.339-8.328-12.223-15.668-10.907
        c-65.508,11.689-106.297,26.403-156.008,45.919c-40.842,16.029-72.203,42.697-94.393,69.832
        c-7.867,9.624-14.572,19.313-20.181,28.688l22.743,14.571c8.461-14.272,20.061-29.741,34.705-44.083
        c17.884-17.528,40.231-33.384,66.983-43.873c49.496-19.392,87.307-33.069,150.907-44.481
        C842.284,1303.325,847.167,1296.311,845.856,1288.973z"></path>
    </mask>
    <symbol data-v-de5d35fc="" id="sg64">
      <rect data-v-de5d35fc="" x="918" y="249.294" width="27" height="25"></rect>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg63">
      <rect data-v-de5d35fc="" x="1030" y="249.292" width="27" height="25"></rect>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg62">
      <path data-v-de5d35fc="" d="M944.999,608.009v-61.622c0-7.456-6.044-13.5-13.5-13.5s-13.5,6.044-13.5,13.5v61.68
        c0.708-0.032,1.418-0.058,2.134-0.058H944.999z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg61">
      <rect data-v-de5d35fc="" x="974" y="249.294" width="27" height="25"></rect>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg60">
      <path data-v-de5d35fc="" d="M1000.999,1677.167c0-7.457-6.043-13.501-13.499-13.501c-7.458,0-13.501,6.044-13.501,13.501
        v38.116c8.344,0,18.553,0,27,0V1677.167z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg59">
      <path data-v-de5d35fc="" d="M1233.813,1547.469c-1.56-7.289-8.731-11.938-16.024-10.379
        c-31.183,6.665-68.324,12.198-112.244,15.395l-0.001,27.07c45.926-3.255,84.938-9.02,117.889-16.062
        C1230.721,1561.936,1235.368,1554.762,1233.813,1547.469z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg58">
      <path data-v-de5d35fc="" d="M870.152,1892.351c-24.244-0.9-60.271-4.327-101.605-14.964
        c-50.726-13.05-109.366-36.899-164.461-80.09c-5.87-4.599-14.355-3.572-18.954,2.298c-4.601,5.867-3.572,14.354,2.295,18.953
        v-0.002c58.651,45.97,120.872,71.224,174.392,84.988c45.761,11.766,85.199,15.172,110.991,15.916
        c-1.714-5.113-2.657-10.588-2.658-16.288V1892.351z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg57">
      <path data-v-de5d35fc="" d="M554.485,1200.129c2.341-7.078-1.5-14.717-8.576-17.057c-7.079-2.34-14.716,1.5-17.057,8.578
        v-0.002c-24.279,73.432-37.014,138.78-43.589,183.233l25.004,16.019C516.057,1347.984,528.492,1278.68,554.485,1200.129z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg56">
      <path data-v-de5d35fc="" d="M1057,608.049v-61.661c0-7.456-6.047-13.499-13.501-13.499
        c-7.456,0-13.499,6.043-13.499,13.499v61.622h25.561C1056.043,608.01,1056.521,608.034,1057,608.049z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg55">
      <path data-v-de5d35fc="" d="M1367.376,1719.462c-12.056,12.612-24.298,23.873-36.603,33.931
        c-5.771,4.719-6.628,13.225-1.905,18.995c2.668,3.267,6.549,4.958,10.46,4.958c3.005,0,6.027-0.998,8.536-3.049
        c13.151-10.752,26.212-22.767,39.03-36.181c41.448-43.354,67.788-89.122,84.372-131.563l-23.228-14.877
        C1432.746,1632.605,1407.813,1677.14,1367.376,1719.462z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg54">
      <path data-v-de5d35fc="" d="M825.903,1766.224c-42.445-9.393-95.231-27.413-140.316-61.07
        c-5.978-4.456-14.437-3.225-18.893,2.754c-4.457,5.979-3.225,14.437,2.754,18.894c51.098,38.058,109.384,57.018,154.915,66.721
        c17.504,3.718,33.131,6.053,45.789,7.524v-27.194C857.785,1772.325,842.666,1769.945,825.903,1766.224z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg53">
      <path data-v-de5d35fc="" d="M944.999,1715.284v-38.117c0-7.457-6.044-13.501-13.499-13.501
        c-7.458,0-13.501,6.044-13.501,13.501v38.173c0.708-0.029,1.419-0.055,2.135-0.055L944.999,1715.284z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg52">
      <path data-v-de5d35fc="" d="M1056.999,1715.322v-38.155c0-7.457-6.043-13.501-13.499-13.501
        c-7.457,0-13.501,6.044-13.501,13.501v38.117l25.562,0.001C1056.043,1715.285,1056.52,1715.309,1056.999,1715.322z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg51">
      <path data-v-de5d35fc="" d="M1194.374,1116.658c-5.659-4.854-14.185-4.202-19.037,1.457
        c-4.855,5.656-4.205,14.18,1.454,19.036c20.582,17.65,35.133,36.813,44.938,52.692l19.004-20.424
        C1229.925,1153.017,1214.85,1134.271,1194.374,1116.658z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg50">
      <path data-v-de5d35fc="" d="M741.737,1561.314c1.017,0.232,2.034,0.346,3.035,0.346c6.151,0,11.71-4.23,13.146-10.481
        c1.67-7.266-2.87-14.51-10.135-16.179c-49.848-11.454-83.452-25.674-104.743-37.146c-0.256,8.587-3.894,17.14-10.949,24.759
        C655.648,1535.084,691.069,1549.693,741.737,1561.314z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg49">
      <path data-v-de5d35fc="" d="M1398.213,1559.758c-6.938,16.91-14.863,30.873-21.591,41.648
        c-3.297,5.281-6.956,11.771-11.583,19.363c-13.83,22.703-36.027,55.155-75.773,84.897c-5.973,4.463-7.192,12.924-2.729,18.896
        c2.651,3.546,6.712,5.416,10.823,5.416c2.813,0,5.646-0.875,8.073-2.688c28.986-21.667,49.577-44.854,64.407-65.047
        c7.415-10.094,13.396-19.443,18.263-27.438c4.855-7.98,8.639-14.659,11.414-19.094c6.648-10.632,14.557-24.448,21.769-41.175
        c-0.908-0.582-1.783-1.143-2.597-1.663L1398.213,1559.758z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg48">
      <path data-v-de5d35fc="" d="M456.213,1357.475c14.181-80.862,44.929-205.504,110.081-328.203
        c3.493-6.586,0.988-14.759-5.598-18.252c-6.586-3.496-14.759-0.986-18.251,5.598h-0.002
        c-68.196,128.6-99.468,258.104-113.601,340.705C436.369,1353.366,445.786,1353.248,456.213,1357.475z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg47">
      <rect data-v-de5d35fc="" x="918" y="273.612" width="27" height="26.824"></rect>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg46">
      <path data-v-de5d35fc="" d="M1029.999,1196.601v115.898c0,7.456,6.045,13.502,13.501,13.502
        c7.454,0,13.499-6.046,13.499-13.502v-142.896c-0.069,0.069-0.146,0.146-0.213,0.214L1029.999,1196.601z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg45">
      <path data-v-de5d35fc="" d="M1098.41,835.534c27.005,26.621,65.637,70.997,100.532,134.337
        c2.459,4.463,7.073,6.986,11.834,6.987c2.204,0,4.438-0.541,6.505-1.68c6.529-3.598,8.906-11.81,5.307-18.339
        c-41.804-75.815-88.673-125.688-117.046-151.811v2.773C1105.542,817.945,1102.936,827.433,1098.41,835.534z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg44">
      <path data-v-de5d35fc="" d="M845.856,1288.973c-1.313-7.339-8.328-12.223-15.668-10.907
        c-65.508,11.689-106.297,26.403-156.008,45.919c-40.842,16.029-72.203,42.697-94.393,69.832
        c-7.867,9.624-14.572,19.313-20.181,28.688l22.743,14.571c8.461-14.272,20.061-29.741,34.705-44.083
        c17.884-17.528,40.231-33.384,66.983-43.873c49.496-19.392,87.307-33.069,150.907-44.481
        C842.284,1303.325,847.167,1296.311,845.856,1288.973z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg43">
      <path data-v-de5d35fc="" d="M974,529.608v41.781c0,7.456,6.042,13.5,13.5,13.5
        c7.455,0,13.5-6.044,13.5-13.5v-40.958C992.138,533.963,982.784,533.696,974,529.608z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg42">
      <path data-v-de5d35fc="" d="M918,1635.754v41.412c0,7.456,6.043,13.501,13.5,13.501s13.502-6.045,13.502-13.501H945
        v-41.357h-24.865C919.418,1635.809,918.708,1635.784,918,1635.754z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg41">
      <path data-v-de5d35fc="" d="M1390.135,1799.688c-4.599-5.87-13.084-6.901-18.951-2.307h-0.003
        c-55.082,43.15-113.7,66.981-164.402,80.018c-41.134,10.58-77.007,14.021-101.235,14.94v10.823c0,5.697-0.942,11.168-2.654,16.279
        c25.786-0.765,65.066-4.188,110.612-15.895c53.497-13.754,115.694-38.98,174.329-84.913
        C1393.699,1814.039,1394.73,1805.554,1390.135,1799.688z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg40">
      <path data-v-de5d35fc="" d="M1414.023,1355.212c-13.14-24.074-29.615-43.384-45.884-58.521
        c2.12,11.84-3.211,20.481-14.082,23.889c13.076,12.836,25.947,28.618,36.257,47.557c2.449,4.494,7.084,7.041,11.868,7.041
        c2.184,0,4.396-0.529,6.447-1.647C1415.176,1369.958,1417.591,1361.759,1414.023,1355.212z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg39">
      <path data-v-de5d35fc="" d="M1105.543,1854.806v8.408c25.146-0.126,61.699-3.126,103.652-15.407
        c42.854-12.542,91.352-34.822,138.668-73.512c5.771-4.718,6.624-13.223,1.906-18.995c-4.718-5.771-13.224-6.626-18.996-1.905
        v-0c-44.245,36.164-89.287,56.82-129.166,68.504c-38.942,11.406-72.939,14.198-96.063,14.318
        C1105.544,1842.979,1105.543,1849.636,1105.543,1854.806z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg38">
      <path data-v-de5d35fc="" d="M870.151,1836.212c-23.157-0.096-57.344-2.867-96.527-14.358
        c-39.936-11.72-85.042-32.438-129.336-68.717c-5.77-4.725-14.273-3.879-19,1.89c-4.725,5.768-3.879,14.272,1.891,18.997h-0
        c47.367,38.809,95.933,61.16,138.852,73.737c42.196,12.37,78.937,15.352,104.125,15.452v-8.407
        C870.151,1849.636,870.151,1842.979,870.151,1836.212z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg37">
      <path data-v-de5d35fc="" d="M1434.883,1410.521c-1.007-4.188-2.109-8.412-3.341-12.688
        c-4.483-15.573-10.515-29.771-17.52-42.623c-3.569-6.546-11.769-8.961-18.313-5.391c-6.549,3.568-8.96,11.769-5.393,18.313
        c6.122,11.234,11.369,23.588,15.279,37.17c2.033,7.07,3.707,13.978,5.072,20.729l8.021-5.14
        C1423.113,1418.061,1429.115,1414.215,1434.883,1410.521z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg36">
      <path data-v-de5d35fc="" d="M1546.5,1357.111c-14.189-82.846-45.611-212.744-114.151-341.592
        c-3.505-6.58-11.681-9.076-18.259-5.571c-6.583,3.502-9.077,11.678-5.573,18.259h-0.002
        c65.584,123.123,96.464,248.332,110.676,329.424C1529.567,1353.328,1538.954,1353.33,1546.5,1357.111z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg35">
      <path data-v-de5d35fc="" d="M1432.35,1015.521c-16.806-31.57-35.847-63.069-57.429-93.785
        c-78.993-112.429-155.006-173.97-211.749-207.318c-22.648-13.319-42.199-22.13-57.629-27.936v26.808c0,0.72,0,1.469,0,2.239
        c13.008,5.432,28.671,13.032,46.459,23.653c53.075,31.709,125.03,90.229,200.827,198.077
        c20.886,29.719,39.355,60.271,55.687,90.944c2.429,4.562,7.1,7.16,11.929,7.16c2.14,0,4.311-0.511,6.331-1.585
        C1433.355,1030.277,1435.854,1022.104,1432.35,1015.521z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg34">
      <path data-v-de5d35fc="" d="M564.017,1313.35c-4.594-7.229-9.004-17.418-12.132-29.174
        c-3.148-11.762-5.059-25.104-5.054-38.811c-0.004-15.006,2.284-30.428,7.527-44.873c2.543-7.008-1.075-14.752-8.083-17.293
        c-7.008-2.543-14.752,1.076-17.295,8.086v-0.002c-6.479,17.863-9.146,36.363-9.15,54.082c0.004,16.199,2.229,31.762,5.97,45.779
        c3.76,14.022,8.991,26.489,15.408,36.649c29.484,46.521,116.416,128.015,328.945,165.035v-27.41
        C666.367,1428.951,588.162,1351.502,564.017,1313.35z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg33">
      <path data-v-de5d35fc="" d="M1030,861.662v45.505c0,7.455,6.043,13.499,13.499,13.499
        c7.454,0,13.501-6.044,13.501-13.499v-45.545c-0.479,0.015-0.957,0.039-1.439,0.039L1030,861.662z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg32">
      <path data-v-de5d35fc="" d="M553.495,1575.181c15.709,40.462,43.619,86.31,90.369,130.214
        c8.234,7.734,16.799,14.854,25.584,21.403c2.421,1.808,5.252,2.678,8.058,2.679c4.117,0,8.183-1.877,10.833-5.431
        c4.458-5.978,3.225-14.435-2.752-18.894c-8.012-5.975-15.787-12.439-23.237-19.438c-45.843-43.079-71.745-87.451-85.608-125.428
        l-19.649,12.588C555.986,1573.585,554.771,1574.363,553.495,1575.181z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg31">
      <path data-v-de5d35fc="" d="M918,861.603v45.563c0,7.456,6.044,13.5,13.5,13.5c7.455,0,13.5-6.044,13.5-13.5v-45.505
        h-24.865C919.418,861.661,918.708,861.635,918,861.603z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg30">
      <path data-v-de5d35fc="" d="M1501.547,1625.948c-17.104,41.768-43.504,86.55-84.077,129.312
        c-14.975,15.778-30.489,29.746-46.289,42.121c-5.871,4.598-6.899,13.084-2.303,18.955c2.662,3.398,6.629,5.175,10.637,5.175
        c2.912,0,5.848-0.938,8.317-2.874c16.808-13.165,33.313-28.029,49.224-44.791c42.423-44.697,70.332-91.615,88.591-135.533
        c-5.125-1.291-10.559-3.69-16.19-7.299L1501.547,1625.948z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg29">
      <path data-v-de5d35fc="" d="M1056.999,1397.283v-84.823c0-7.456-6.044-13.5-13.498-13.5c-7.456,0-13.5,6.044-13.5,13.5
        h-0.002v84.785l25.563,0.001C1056.044,1397.246,1056.521,1397.27,1056.999,1397.283z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg28">
      <path data-v-de5d35fc="" d="M588.479,1738.116c12.71,13.3,25.657,25.225,38.699,35.907
        c2.509,2.056,5.538,3.057,8.546,3.057c3.907,0,7.782-1.688,10.452-4.945c4.723-5.768,3.879-14.271-1.889-18.996
        c-12.197-9.992-24.336-21.171-36.29-33.677c-40.375-42.257-65.288-86.698-80.589-127.571l-23.219,14.872
        C520.779,1649.145,547.107,1694.844,588.479,1738.116z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg27">
      <path data-v-de5d35fc="" d="M747.783,1534.998c-7.266-1.669-14.512,2.869-16.181,10.137s2.869,14.51,10.137,16.18
        c35.103,8.063,77.53,14.713,128.414,18.287v-27.068C821.405,1549.02,780.989,1542.628,747.783,1534.998z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg26">
      <path data-v-de5d35fc="" d="M1163.307,1252.639l0.479-0.514c-59.116,0.406-130.667,2.557-199.524,8.911
        c-55.761,5.146-98.553,10.671-134.07,17.026c-7.34,1.312-12.226,8.326-10.912,15.666c1.313,7.34,8.328,12.223,15.668,10.91h-0.002
        c34.479-6.171,76.534-11.616,131.799-16.72c64.529-5.958,131.945-8.182,188.864-8.728
        C1152.322,1271.411,1154.608,1261.987,1163.307,1252.639z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg25">
      <path data-v-de5d35fc="" d="M1194.373,1116.659c-16.65-14.319-36.947-27.796-61.278-38.452
        c-1.023,8.898-4.437,17.568-10.235,24.985c21.449,9.471,39.204,21.352,53.935,33.958c2.545,2.185,5.672,3.255,8.782,3.255
        c3.803,0,7.582-1.599,10.253-4.71C1200.683,1130.036,1200.032,1121.515,1194.373,1116.659z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg24">
      <path data-v-de5d35fc="" d="M1001,300.434v-26.822h-27v26.822C982.033,300.434,992.596,300.433,1001,300.434z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg23">
      <path data-v-de5d35fc="" d="M1001,608.008v-36.619c0-7.457-6.044-13.5-13.5-13.5c-7.458,0-13.5,6.043-13.5,13.5v36.619
        C982.344,608.008,992.554,608.008,1001,608.008z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg22">
      <path data-v-de5d35fc="" d="M1381.875,1039.718c-3.516-6.575-11.694-9.057-18.27-5.541
        c-6.576,3.516-9.059,11.694-5.541,18.27h-0.002c50.739,94.914,78.781,191.172,94.043,263.587
        c6.243,29.601,10.355,55.206,13.033,75.107l25.008-16.017C1479.281,1301.436,1451.461,1170.049,1381.875,1039.718z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg21">
      <path data-v-de5d35fc="" d="M1292.789,1146.307c-16.166-75.301-41.996-138.312-70.202-189.468
        c-3.599-6.53-11.81-8.905-18.338-5.306c-6.53,3.599-8.904,11.808-5.306,18.338h-0.002c26.321,47.775,50.562,106.316,66.192,176.304
        C1274.488,1141.072,1284.379,1141.238,1292.789,1146.307z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg20">
      <path data-v-de5d35fc="" d="M870.152,740.071c-11.131,5.289-24.484,12.602-39.764,22.717
        c-46.479,30.756-110.592,87.223-183.538,190.299c-55.677,78.68-92.973,162.92-117.998,238.562
        c-2.343,7.079,1.497,14.717,8.576,17.061c1.407,0.465,2.837,0.688,4.243,0.688c5.664,0,10.938-3.594,12.815-9.264
        c24.382-73.729,60.688-155.563,114.403-231.448c71.338-100.795,133.09-154.715,176.399-183.38
        c9.137-6.045,17.443-10.957,24.863-14.96V756.38C870.152,751.767,870.152,746.042,870.152,740.071z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg19">
      <path data-v-de5d35fc="" d="M1305.433,1727.292c5.972-4.466,7.192-12.925,2.729-18.894
        c-4.466-5.975-12.925-7.195-18.896-2.729c-39.989,29.851-97.83,57.101-183.724,67.669l0.001,27.211
        C1197.351,1789.785,1261.166,1760.438,1305.433,1727.292z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg18">
      <path data-v-de5d35fc="" d="M449.77,1638.414c18.263,43.89,46.176,90.789,88.55,135.43
        c15.871,16.727,32.341,31.561,49.109,44.704c2.469,1.938,5.404,2.875,8.316,2.875c4.006,0,7.972-1.774,10.635-5.174
        c4.602-5.866,3.572-14.353-2.296-18.951c-15.762-12.354-31.241-26.296-46.18-42.038c-40.524-42.715-66.884-87.399-83.986-129.107
        l-7.591,4.861C460.562,1634.707,455.001,1637.146,449.77,1638.414z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg17">
      <path data-v-de5d35fc="" d="M918,546.389c0,7.456,6.043,13.5,13.501,13.5c7.456,0,13.5-6.045,13.5-13.5h-0.002v-48.705
        l-27-42.145L918,546.389L918,546.389z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg16">
      <path data-v-de5d35fc="" d="M870.152,686.362c-15.479,5.803-35.146,14.646-57.949,28.058
        c-56.743,33.346-132.757,94.888-211.749,207.317c-21.832,31.07-41.065,62.946-58.012,94.879
        c-3.495,6.587-0.989,14.757,5.597,18.252c2.017,1.069,4.183,1.578,6.317,1.578c4.836,0,9.512-2.605,11.937-7.175
        c16.467-31.033,35.125-61.947,56.252-92.012c77.001-109.559,150.034-168.214,203.341-199.565
        c16.897-9.934,31.812-17.116,44.265-22.3c0-0.721,0-1.428,0-2.104L870.152,686.362z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg15">
      <path data-v-de5d35fc="" d="M917.999,1169.199v143.3c0,7.456,6.045,13.502,13.501,13.502s13.5-6.046,13.5-13.502
        v-116.302l-26.387-26.385C918.416,1169.615,918.202,1169.402,917.999,1169.199z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg14">
      <path data-v-de5d35fc="" d="M1000.999,1312.5c0-7.457-6.043-13.5-13.5-13.5s-13.5,6.043-13.5,13.5v84.783
        c8.344,0,18.553,0,26.999,0L1000.999,1312.5L1000.999,1312.5z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg13">
      <path data-v-de5d35fc="" d="M1056.999,975.311v-68.144c0-7.456-6.045-13.501-13.499-13.501
        c-7.456,0-13.501,6.045-13.501,13.501v41.146l26.785,26.784C1056.854,975.165,1056.93,975.241,1056.999,975.311z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg12">
      <path data-v-de5d35fc="" d="M1105.543,770.514c42.678,23.082,114.727,76.433,200.943,198.172
        c19.367,27.367,36.477,55.505,51.576,83.759c2.431,4.55,7.096,7.14,11.918,7.14c2.146,0,4.324-0.513,6.351-1.595
        c6.576-3.517,9.06-11.692,5.545-18.271c-15.584-29.158-33.271-58.258-53.353-86.632
        c-72.946-103.076-137.061-159.542-183.538-190.298c-15.132-10.019-28.376-17.289-39.441-22.565
        c-0.001,5.916-0.001,11.583-0.001,16.155V770.514z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg11">
      <path data-v-de5d35fc="" d="M1029.999,546.389c0,7.456,6.043,13.501,13.501,13.501c7.455,0,13.501-6.045,13.501-13.501
        h-0.002V458.56l-27,42.146V546.389z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg10">
      <path data-v-de5d35fc="" d="M844.492,1056.748c-90.371,8.191-172.3,26.736-232.589,51.879
        c-21.707,9.064-39.394,21.109-53.16,35.258c-13.774,14.138-23.582,30.34-29.765,47.4c-2.543,7.01,1.076,14.752,8.085,17.295
        c7.009,2.543,14.753-1.078,17.296-8.086c4.96-13.652,12.668-26.408,23.728-37.775c11.066-11.354,25.526-21.354,44.228-29.18
        c55.896-23.416,134.226-41.383,220.989-49.576C841.189,1074.988,841.587,1065.547,844.492,1056.748z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg9">
      <path data-v-de5d35fc="" d="M1029.998,1635.811v41.355c0,7.457,6.046,13.502,13.502,13.502s13.5-6.045,13.5-13.502
        v-41.395c-0.479,0.014-0.957,0.036-1.439,0.036L1029.998,1635.811z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg8">
      <path data-v-de5d35fc="" d="M974,861.663v27.503h27v-27.503C992.554,861.663,982.344,861.663,974,861.663z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg7">
      <path data-v-de5d35fc="" d="M945,948.707v-41.54c0-7.456-6.043-13.501-13.499-13.501S918,899.711,918,907.167v68.538
        c0.203-0.203,0.415-0.415,0.612-0.612L945,948.707z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg6">
      <path data-v-de5d35fc="" d="M1217.787,1537.088c-7.292,1.562-11.939,8.733-10.379,16.025
        c1.354,6.339,6.956,10.681,13.187,10.681c0.938,0,1.886-0.098,2.838-0.302c56.35-12.022,94.968-27.84,120.005-41.162
        c-6.998-7.669-10.563-16.267-10.712-24.885C1310.178,1509.694,1273.486,1525.163,1217.787,1537.088z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg5">
      <path data-v-de5d35fc="" d="M945,1397.284V1312.5c0-7.455-6.044-13.5-13.5-13.5s-13.5,6.045-13.5,13.5v84.84
        c0.708-0.03,1.418-0.055,2.134-0.055L945,1397.284z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg4">
      <polygon data-v-de5d35fc="" points="1057,273.61 1030,273.61 1029.998,273.61 1029.998,300.435 1057,300.436  "></polygon>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg3">
      <path data-v-de5d35fc="" d="M974,1635.811v41.355c0,7.456,6.043,13.501,13.5,13.501s13.5-6.045,13.5-13.501v-41.355
        C992.553,1635.811,982.344,1635.811,974,1635.811z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg2">
      <path data-v-de5d35fc="" d="M973.999,1215.757v96.22c0,7.456,6.043,13.502,13.5,13.502c7.455,0,13.5-6.046,13.5-13.502
        v-96.109C992.171,1218.294,982.812,1218.261,973.999,1215.757z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="sg1">
      <path data-v-de5d35fc="" d="M1001,928.522v-39.355h-27v39.46C982.813,926.125,992.172,926.093,1001,928.522z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="croot">
      <path data-v-de5d35fc="" d="M1105.542,1903.162
        c-0.001,27.972-22.378,50.647-49.982,50.648l-47.721,0.001c-11.042,0.001-28.943,0.001-39.984,0l-47.721-0.001
        c-27.604-0.001-49.981-22.677-49.982-50.648l-0.001-48.356c-0.001-11.189-0.001-29.329,0-40.518l0.001-48.356
        c0.001-27.973,22.378-50.648,49.982-50.648l47.721-0.002c11.041,0,28.942,0,39.984,0l47.721,0.002
        c27.604,0,49.981,22.676,49.982,50.648l0.001,48.356c0.001,11.188,0.001,29.328,0,40.518L1105.542,1903.162z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="csacral">
      <path data-v-de5d35fc="" d="M1105.542,1585.162
        c-0.001,27.972-22.378,50.647-49.982,50.648l-47.721,0.001c-11.042,0.001-28.943,0.001-39.984,0l-47.721-0.001
        c-27.604-0.001-49.981-22.677-49.982-50.648l-0.001-48.356c-0.001-11.189-0.001-29.329,0-40.518l0.001-48.356
        c0.001-27.973,22.378-50.648,49.982-50.648l47.721-0.002c11.041,0,28.942,0,39.984,0l47.721,0.002
        c27.604,0,49.981,22.676,49.982,50.648l0.001,48.356c0.001,11.188,0.001,29.328,0,40.518L1105.542,1585.162z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="csolar_plexus">
      <path data-v-de5d35fc="" d="M1509.455,1362.758c30.77-19.708,55.714-3.729,55.715,35.689l0.003,80.596c0.001,9.854,0.001,25.832,0,35.687l-0.003,80.595
        c-0.001,39.419-24.945,55.397-55.715,35.689l-62.911-40.295c-7.692-4.927-20.163-12.915-27.855-17.843l-62.908-40.301
        c-30.768-19.711-30.768-51.668,0-71.379l62.908-40.3c7.692-4.928,20.163-12.917,27.855-17.844L1509.455,1362.758z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="cspleen">
      <path data-v-de5d35fc="" d="M466.328,1362.758c-30.77-19.708-55.713-3.729-55.715,35.689l-0.003,80.596c0,9.854,0,25.832,0,35.687l0.003,80.595
        c0.001,39.419,24.945,55.397,55.715,35.689l62.911-40.295c7.692-4.927,20.164-12.915,27.855-17.843l62.908-40.301
        c30.768-19.711,30.768-51.668,0-71.379l-62.908-40.3c-7.692-4.928-20.163-12.917-27.855-17.844L466.328,1362.758z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="cheart">
      <path data-v-de5d35fc="" d="M1364.791,1286.496
        c11.264,24.11-3.228,39.686-32.368,34.789l-60.875-10.229c-7.377-1.239-19.338-3.25-26.715-4.49l-61.13-10.276
        c-29.14-4.898-38.271-24.444-20.396-43.657l37.34-40.135c4.525-4.864,11.862-12.75,16.389-17.613l37.5-40.302
        c17.877-19.211,41.501-15.241,52.767,8.868l23.534,50.365c2.852,6.104,7.476,15.999,10.327,22.104L1364.791,1286.496z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="cg">
      <path data-v-de5d35fc="" d="M1023.042,1203.557
        c-19.519,19.518-51.165,19.517-70.685-0.002l-33.744-33.743c-7.808-7.808-20.466-20.466-28.273-28.273l-33.742-33.745
        c-19.519-19.521-19.519-51.167-0.001-70.686l33.742-33.744c7.807-7.807,20.465-20.465,28.272-28.272l33.744-33.741
        c19.519-19.519,51.164-19.518,70.684,0.001l33.745,33.743c7.808,7.808,20.466,20.466,28.272,28.274l33.743,33.745
        c19.519,19.52,19.519,51.166,0.001,70.685l-33.742,33.744c-7.807,7.808-20.465,20.466-28.272,28.272L1023.042,1203.557z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="cthroat">
      <path data-v-de5d35fc="" d="M1105.542,807.801
        c-0.001,29.746-22.378,53.859-49.982,53.86l-47.721,0.001c-11.042,0.001-28.943,0.001-39.984,0l-47.721-0.001
        c-27.604-0.001-49.981-24.114-49.982-53.86l-0.001-51.422c-0.001-11.898-0.001-31.188,0-43.087l0.001-51.423
        c0.001-29.745,22.378-53.858,49.982-53.859l47.721-0.001c11.041-0.001,28.942-0.001,39.984,0l47.721,0.001
        c27.604,0.001,49.981,24.114,49.982,53.859l0.001,51.423c0.001,11.898,0.001,31.188,0,43.087L1105.542,807.801z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="cajna">
      <path data-v-de5d35fc="" d="M854.339,356.152
        c-19.708-30.77-3.729-55.714,35.688-55.715l80.596-0.003c9.854-0.001,25.832-0.001,35.687,0l80.596,0.003
        c39.419,0.001,55.397,24.945,35.688,55.715l-40.294,62.911c-4.928,7.692-12.916,20.163-17.844,27.855l-40.301,62.908
        c-19.711,30.768-51.667,30.768-71.378,0l-40.301-62.908c-4.928-7.692-12.916-20.163-17.844-27.855L854.339,356.152z"></path>
    </symbol>
    <symbol data-v-de5d35fc="" id="chead">
      <path data-v-de5d35fc="" d="M1122.594,193.576
        c19.709,30.77,3.73,55.714-35.688,55.715l-80.596,0.004c-9.854,0-25.832,0-35.687,0l-80.596-0.004
        c-39.418-0.001-55.396-24.945-35.688-55.715l40.295-62.911c4.927-7.692,12.915-20.163,17.843-27.855l40.301-62.907
        c19.711-30.769,51.668-30.769,71.379,0l40.3,62.907c4.928,7.692,12.917,20.163,17.844,27.855L1122.594,193.576z"></path>
    </symbol>
  </defs>
  <g data-v-de5d35fc="" id="graph">
    <!-- <path data-v-de5d35fc="" id="figure" stroke-width="0" stroke-opacity=".2" d="M671.25,720.333
      c65.375-9.208,139.079-31.648,174.117-78.966c16.881-22.798,28.908-71.182,24.447-81.59c-5.669-14.687-12.07-18.102-41.12-12.717
      c-15.263,2.493-45.825-2.943-46.283-29.05c1.443-21.161,3.351-77.706-2.598-86.628c-5.948-8.922-25.356-11.331-23.87-21.74
      C757.432,399.234,787.812,340.841,788,329c0.342-11.055-10.485-25.269-14.176-29.8c-0.701-0.861-1.308-2.242-1.308-2.242
      s-0.447-1.587-0.411-2.653c0.353-10.534,3.855-69.289,39.709-123.306c47.271-71.216,126.082-72.703,163-72.703
      c31.227,0,114.516-5.073,179,72.703c87.732,105.736,22,227.669-18,297c-40.653,70.464-34.601,135.015-5.182,173.367
      c51.63,67.306,140.211,75.574,177.034,79.299c6.29,0.775,30.712,2.604,42,10.667c14,10,19.035,27.878,21,32.333
      c33.599,81.192,250.128,602.245,311.976,760.714c9.01,24.849,14.156,42.028,13.288,54.655c-1.088,15.818-10.16,31.84-21.706,44.259
      c-255.071,280.326-426.6,344.511-686.225,344.511l-0.22,0.005c-258.299,0-438.955-66.053-685.481-344.145
      c-10.958-12.36-20.065-29.441-20.555-45.333c-0.033-1.062-2.509-18.369,9.915-48.615
      c65.989-163.213,263.609-628.923,322.634-766.192c10.524-24.476,24.68-31.761,24.832-31.859
      C645.625,727.5,653.981,723.124,671.25,720.333z"></path> -->
    <g data-v-de5d35fc="" id="channels">
      <path data-v-de5d35fc="" id="c102034" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.563,2.46-31.708,8.12-46.938c0.003-0.01,0.008-0.019,0.011-0.023c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.47,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571c-2.116-8.975-1.717-18.417,1.188-27.217
        c-90.373,8.194-172.296,26.73-232.589,51.877c-10.608,4.429-20.193,9.642-28.802,15.483
        c21.954-51.684,50.003-104.873,85.786-155.428c71.34-100.795,133.091-154.717,176.4-183.379
        c9.136-6.045,17.441-10.958,24.863-14.961V756.38c0-4.611,0-10.336,0-16.308c-11.13,5.289-24.483,12.603-39.763,22.718
        c-46.479,30.756-110.591,87.221-183.539,190.297c-55.541,78.489-92.788,162.507-117.813,238.006
        c-0.62,1.87-0.797,3.776-0.606,5.616c-5.572,16.97-7.935,34.329-7.937,50.946c0.085,31.453,8.177,60.168,20.713,80.14
        c29.483,46.524,116.416,128.014,328.945,165.037v-27.41C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c203457" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.572,2.463-31.729,8.129-46.963c0.763-2.056,0.981-4.168,0.752-6.204c24.357-72.246,60.166-151.854,112.513-225.806
        c71.342-100.796,133.093-154.718,176.401-183.38c9.136-6.045,17.442-10.958,24.863-14.961V756.38c0-4.611,0-10.336,0-16.308
        c-11.129,5.289-24.483,12.603-39.763,22.718c-46.479,30.756-110.591,87.221-183.539,190.297
        c-55.541,78.489-92.788,162.507-117.813,238.006h-0.001c-24.402,73.658-37.182,139.229-43.773,183.792l25.006,16.02
        c3.248-24.081,8.593-56.479,17.248-94.146c3.495,11.704,8.158,22.229,13.69,31.04c29.484,46.524,116.416,128.017,328.945,165.037
        v-27.41C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c103457" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.563,2.46-31.708,8.12-46.938c0.003-0.01,0.008-0.019,0.011-0.023c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.47,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571c-2.116-8.975-1.717-18.417,1.188-27.217
        c-90.373,8.194-172.296,26.73-232.589,51.877c-21.607,9.021-38.998,21.277-52.444,35.531
        c-11.896,12.594-20.683,26.701-26.845,41.333c-1.591,1.489-2.844,3.387-3.576,5.602h-0.002
        c-24.402,73.658-37.182,139.229-43.773,183.792l25.005,16.02c3.249-24.081,8.594-56.479,17.249-94.146
        c3.495,11.704,8.158,22.229,13.69,31.04c29.484,46.524,116.416,128.017,328.945,165.037v-27.41
        C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c102057" mask="url(#g34Mask)" d="M845.289,785.305c9.136-6.045,17.442-10.958,24.863-14.961v-13.965
        c0-4.611,0-10.336,0-16.308c-11.129,5.289-24.483,12.603-39.763,22.718c-46.479,30.756-110.591,87.221-183.539,190.297
        c-55.541,78.489-92.788,162.507-117.813,238.006h-0.001c-24.402,73.658-37.182,139.229-43.773,183.792l25.005,16.02
        c5.664-41.986,17.7-109.237,42.557-185.688c1.189-1.271,2.157-2.791,2.801-4.521c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.47,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571c-2.116-8.975-1.717-18.417,1.188-27.217
        c-90.373,8.194-172.296,26.729-232.589,51.877c-10.608,4.429-20.193,9.642-28.802,15.483
        c21.954-51.684,50.003-104.873,85.786-155.428C740.229,867.889,801.98,813.968,845.289,785.305z" class=""></path>
      <path data-v-de5d35fc="" id="c10203457" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.563,2.46-31.708,8.12-46.938c0.003-0.01,0.008-0.019,0.011-0.023c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.47,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571c-2.116-8.975-1.717-18.417,1.188-27.217
        c-90.373,8.194-172.296,26.73-232.589,51.877c-10.608,4.429-20.193,9.642-28.802,15.483
        c21.954-51.684,50.003-104.873,85.786-155.428c71.34-100.795,133.091-154.717,176.4-183.379
        c9.136-6.045,17.441-10.958,24.863-14.961V756.38c0-4.611,0-10.336,0-16.308c-11.13,5.289-24.483,12.603-39.763,22.718
        c-46.479,30.756-110.591,87.221-183.539,190.297c-55.541,78.489-92.788,162.507-117.813,238.006h-0.001
        c-24.402,73.658-37.183,139.229-43.773,183.792l25.005,16.02c3.249-24.081,8.594-56.479,17.249-94.146
        c3.495,11.704,8.158,22.229,13.69,31.04c29.484,46.524,116.416,128.017,328.945,165.037v-27.41
        C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c0" desc="10-20" d="M845.289,785.305c9.136-6.045,17.442-10.958,24.863-14.961v-13.965
        c0-4.611,0-10.336,0-16.308c-11.129,5.289-24.483,12.603-39.763,22.718c-46.479,30.756-110.591,87.221-183.539,190.297
        c-55.541,78.489-92.788,162.507-117.813,238.006c-2.111,6.366,0.791,13.176,6.547,16.188c0.827,0.54,1.719,1.005,2.683,1.362
        c1.55,0.573,3.14,0.85,4.701,0.851c5.481,0,10.635-3.363,12.657-8.802c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.468,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571
        c-2.116-8.975-1.717-18.417,1.188-27.217c-90.373,8.194-172.296,26.73-232.589,51.877c-10.608,4.429-20.193,9.642-28.802,15.483
        c21.954-51.684,50.003-104.873,85.786-155.428C740.229,867.889,801.98,813.968,845.289,785.305z" class=""></path>
      <path data-v-de5d35fc="" id="c1" desc="20-34" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.572,2.463-31.729,8.129-46.963c0.763-2.056,0.981-4.168,0.752-6.203c24.357-72.247,60.166-151.854,112.513-225.807
        c71.342-100.796,133.093-154.718,176.401-183.38c9.136-6.045,17.442-10.958,24.863-14.961V756.38c0-4.611,0-10.336,0-16.308
        c-11.129,5.289-24.483,12.603-39.763,22.718c-46.479,30.756-110.591,87.221-183.539,190.297
        c-55.541,78.489-92.788,162.507-117.813,238.006c-0.621,1.871-0.797,3.779-0.608,5.621c-5.569,16.968-7.934,34.325-7.936,50.941
        c0.085,31.453,8.177,60.168,20.713,80.14c29.484,46.524,116.416,128.014,328.945,165.037v-27.41
        C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c2" desc="34-57" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.572,2.463-31.729,8.129-46.963c2.41-6.483-0.484-13.636-6.493-16.707c-0.929-0.6-1.937-1.102-3.032-1.464
        c-7.077-2.347-14.717,1.49-17.06,8.567h-0.002c-24.402,73.658-37.182,139.229-43.773,183.792l25.005,16.016
        c3.25-24.077,8.594-56.476,17.25-94.146c3.494,11.704,8.157,22.226,13.689,31.04c29.484,46.524,116.416,128.014,328.945,165.037
        v-27.41C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c3" desc="10-57" d="M844.492,1056.75c-90.373,8.194-172.296,26.73-232.589,51.877
        c-21.607,9.021-38.998,21.277-52.444,35.531c-11.895,12.594-20.683,26.701-26.845,41.333c-1.591,1.489-2.843,3.387-3.576,5.602
        h-0.002c-24.402,73.658-37.183,139.229-43.773,183.792l25.005,16.02c5.664-41.986,17.7-109.237,42.557-185.688
        c1.189-1.271,2.156-2.791,2.801-4.521c5.074-13.645,12.667-26.559,23.467-37.997c10.811-11.438,24.854-21.471,43.226-29.156
        c55.891-23.409,134.227-41.381,220.987-49.571C841.188,1074.992,841.588,1065.55,844.492,1056.75z" class=""></path>
      <path data-v-de5d35fc="" id="c4" desc="61-24" d="M974,249.295v51.139c8.033,0,18.595,0,27,0v-51.139H974L974,249.295z" class=""></path>
      <path data-v-de5d35fc="" id="c5" desc="43-23" d="M974,529.608v78.399c8.344,0,18.553,0,27,0V530.43
        C992.138,533.963,982.784,533.696,974,529.608z" class=""></path>
      <path data-v-de5d35fc="" id="c6" desc="28-38" d="M870.152,1863.213v-8.407c0-5.17,0-11.822,0-18.594
        c-23.157-0.096-57.345-2.867-96.527-14.358c-39.936-11.72-85.042-32.438-129.336-68.717c-0.009-0.008-0.021-0.014-0.029-0.021
        c-12.188-9.987-24.316-21.157-36.261-33.653c-40.375-42.257-65.288-86.698-80.589-127.571l-23.219,14.872
        c16.589,42.381,42.917,88.081,84.29,131.354c12.71,13.3,25.656,25.225,38.698,35.907c0.001,0,0.002,0.002,0.003,0.002
        c47.365,38.807,95.931,61.154,138.847,73.731C808.224,1860.131,844.964,1863.111,870.152,1863.213z" class=""></path>
      <path data-v-de5d35fc="" id="c7" desc="20-57" d="M870.152,756.379c0-4.611,0-10.336,0-16.308c-11.129,5.289-24.483,12.603-39.763,22.718
        c-46.479,30.756-110.591,87.221-183.539,190.297c-55.541,78.489-92.788,162.507-117.813,238.006h-0.001
        c-24.402,73.658-37.182,139.229-43.773,183.792l25.005,16.02c5.804-43.02,18.286-112.543,44.399-191.314
        c0.002-0.006,0.003-0.011,0.004-0.017c24.382-73.581,60.641-155.194,114.216-230.886
        C740.229,867.89,801.98,813.968,845.289,785.306c9.136-6.045,17.442-10.958,24.863-14.961V756.379z" class=""></path>
      <path data-v-de5d35fc="" id="c8" desc="39-55" d="M1448.04,1591.677c-15.294,40.934-40.228,85.463-80.664,127.785
        c-12.056,12.612-24.298,23.873-36.603,33.931v-0.002c-44.245,36.164-89.287,56.82-129.166,68.507
        c-38.942,11.403-72.939,14.195-96.063,14.315c0,6.771-0.001,13.423-0.001,18.594v8.407c25.146-0.126,61.699-3.126,103.652-15.407
        c42.84-12.534,91.316-34.81,138.623-73.476c0.015-0.012,0.027-0.021,0.046-0.034c13.148-10.752,26.212-22.767,39.03-36.181
        c41.445-43.354,67.785-89.122,84.369-131.563L1448.04,1591.677z" class=""></path>
      <path data-v-de5d35fc="" id="c9" desc="12-22" d="M1381.88,1039.727c-0.002-0.002-0.003-0.005-0.004-0.008
        c-15.584-29.158-33.271-58.258-53.353-86.632c-72.946-103.076-137.063-159.542-183.538-190.298
        c-15.132-10.02-28.376-17.289-39.441-22.565c-0.001,5.916-0.001,11.583-0.001,16.155v14.135
        c42.678,23.082,114.727,76.436,200.943,198.172c19.364,27.367,36.477,55.506,51.576,83.76v0.001l0,0
        c50.736,94.913,78.778,191.172,94.043,263.587c6.24,29.601,10.354,55.206,13.03,75.104l25.011-16.017
        C1479.281,1301.438,1451.462,1170.055,1381.88,1039.727z" class=""></path>
      <path data-v-de5d35fc="" id="c10" desc="3-60" d="M1000.999,1635.811c-8.446,0-18.655,0-26.999,0v79.477h27L1000.999,1635.811
        L1000.999,1635.811z" class=""></path>
      <path data-v-de5d35fc="" id="c11" desc="2-14" d="M1001,1216.389c-8.828,2.432-18.188,2.396-27-0.105v181.004h26.999L1001,1216.389z" class=""></path>
      <path data-v-de5d35fc="" id="c12" desc="8-1" d="M1001,861.663c-8.446,0-18.656,0-27,0v66.965c8.813-2.502,18.171-2.535,27-0.104V861.663
        z" class=""></path>
      <path data-v-de5d35fc="" id="c13" desc="10-34" mask="url(#g34Mask)" d="M564.017,1313.351c-9.11-14.214-16.607-39.146-16.522-65.691
        c-0.002-15.563,2.46-31.708,8.12-46.938c0.003-0.01,0.008-0.019,0.011-0.023c5.074-13.645,12.667-26.559,23.467-37.997
        c10.811-11.438,24.854-21.47,43.226-29.156c55.891-23.409,134.227-41.381,220.987-49.571c-2.116-8.975-1.717-18.417,1.188-27.217
        c-90.373,8.194-172.296,26.73-232.589,51.877c-21.607,9.021-38.998,21.277-52.444,35.531
        c-13.458,14.249-22.946,30.435-29.144,47.128c-0.002,0.006-0.004,0.013-0.007,0.019c-6.934,18.669-9.813,37.969-9.814,56.354
        c0.085,31.453,8.177,60.168,20.713,80.14c29.484,46.524,116.416,128.017,328.945,165.037v-27.41
        C666.367,1428.952,588.162,1351.502,564.017,1313.351z" class=""></path>
      <path data-v-de5d35fc="" id="c14" desc="25-51" d="M1194.374,1116.658c-0.008-0.007-0.017-0.017-0.025-0.021
        c-16.646-14.312-36.935-27.779-61.254-38.432c-1.021,8.896-4.437,17.566-10.232,24.984c21.438,9.466,39.183,21.339,53.904,33.938
        c0.013,0.007,0.02,0.016,0.024,0.021c20.582,17.648,35.133,36.813,44.938,52.69l19.007-20.424
        C1229.925,1153.017,1214.85,1134.271,1194.374,1116.658z" class=""></path>
      <rect data-v-de5d35fc="" id="c15" desc="63-4" x="1029.999" y="249.294" width="27.002" height="51.141" class=""></rect>
      <path data-v-de5d35fc="" id="c16" desc="17-62" d="M944.999,497.684l-27-42.145v152.528c0.708-0.032,27-0.06,27-0.06V497.684z" class=""></path>
      <path data-v-de5d35fc="" id="c17" desc="18-58" d="M872.811,1919.45c-1.714-5.113-2.656-10.588-2.657-16.288v-10.813
        c-24.244-0.897-60.271-4.323-101.605-14.964c-50.726-13.05-109.366-36.896-164.461-80.09c-0.007-0.006-0.016-0.011-0.022-0.017
        c-15.755-12.354-31.228-26.285-46.158-42.021c-40.524-42.715-66.885-87.396-83.986-129.104l-7.591,4.858
        c-5.767,3.693-11.327,6.137-16.559,7.4c18.264,43.89,46.177,90.789,88.551,135.43c15.864,16.724,32.329,31.549,49.092,44.689
        c0.006,0.005,0.011,0.01,0.017,0.015v-0.001l0.001,0.001c0.005,0.004,0.011,0.007,0.017,0.011
        c58.646,45.964,120.858,71.212,174.375,84.979C807.581,1915.3,847.019,1918.706,872.811,1919.45z" class=""></path>
      <path data-v-de5d35fc="" id="c18" desc="16-48" d="M812.203,714.42c-56.743,33.346-132.757,94.888-211.749,207.317
        c-21.832,31.069-41.065,62.945-58.012,94.879c-68.196,128.6-99.469,258.104-113.602,340.705
        c7.527-3.955,16.944-4.073,27.372,0.149c14.18-80.854,44.925-205.484,110.069-328.182c0.004-0.007,0.008-0.018,0.012-0.021
        c16.467-31.033,35.125-61.947,56.252-92.012C699.547,827.7,772.58,769.045,825.887,737.694
        c16.897-9.935,31.813-17.116,44.267-22.301c0-0.721,0-1.428,0-2.104v-26.93C854.674,692.166,835.007,701.01,812.203,714.42z" class=""></path>
      <path data-v-de5d35fc="" id="c19" desc="9-52" d="M1057,1635.771c-0.479,0.014-0.957,0.033-1.439,0.033l-25.563,0.001l0.001,79.479
        l25.562,0.001c0.482,0,0.959,0.021,1.438,0.037L1057,1635.771z" class="defined"></path>
      <path data-v-de5d35fc="" id="c20" desc="5-15" d="M945,1196.197l-26.387-26.385c-0.197-0.197-0.411-0.41-0.614-0.613L918,1397.34
        c0.708-0.03,1.418-0.055,2.134-0.055l24.867-0.001L945,1196.197z" class=""></path>
      <path data-v-de5d35fc="" id="c21" desc="31-7" d="M944.999,861.662c0,0-26.291-0.027-27-0.06v114.103l0.612-0.612L945,948.707
        L944.999,861.662z" class=""></path>
      <path data-v-de5d35fc="" id="c22" desc="30-41" d="M1509.455,1631.014l-7.908-5.063c-17.104,41.768-43.504,86.55-84.077,129.312
        c-14.975,15.778-30.489,29.746-46.289,42.121c-55.082,43.15-113.7,66.981-164.399,80.021
        c-41.137,10.58-77.01,14.021-101.235,14.938v10.823c0,5.697-0.942,11.168-2.654,16.279c25.786-0.768,65.066-4.188,110.612-15.896
        c53.475-13.748,115.641-38.961,174.254-84.854c0.025-0.021,0.053-0.036,0.078-0.06c16.808-13.165,33.313-28.026,49.224-44.791
        c42.427-44.694,70.332-91.612,88.595-135.53C1520.521,1637.021,1515.088,1634.621,1509.455,1631.014z" class=""></path>
      <path data-v-de5d35fc="" id="c23" desc="35-36" d="M1432.349,1015.521C1432.349,1015.52,1432.348,1015.52,1432.349,1015.521
        c-16.806-31.571-35.847-63.069-57.428-93.783c-78.993-112.43-155.006-173.971-211.749-207.318
        c-22.646-13.319-42.199-22.13-57.629-27.936v29.047c13.008,5.432,28.671,13.031,46.459,23.652
        c53.075,31.709,125.03,90.229,200.827,198.077c20.886,29.72,39.354,60.271,55.687,90.944v0.001h-0.001
        c65.584,123.123,96.464,248.332,110.679,329.424c10.378-4.303,19.765-4.301,27.311-0.521
        C1532.311,1274.267,1500.889,1144.367,1432.349,1015.521z" class=""></path>
      <rect data-v-de5d35fc="" id="c24" desc="64-47" x="918" y="249.292" width="27" height="51.143" class=""></rect>
      <path data-v-de5d35fc="" id="c25" desc="11-56" d="M1056.999,458.56l-27,42.146L1030,608.009h25.561c0.482,0,0.964,0.024,1.439,0.039
        L1056.999,458.56z" class=""></path>
      <path data-v-de5d35fc="" id="c26" desc="42-53" d="M944.999,1635.809c0,0-26.291-0.021-27-0.055v79.586c0.708-0.03,27-0.056,27-0.056
        V1635.809z" class=""></path>
      <!---->
      <path data-v-de5d35fc="" id="c28" desc="33-13" d="M1057,861.622c-0.479,0.015-27,0.04-27,0.04l-0.001,86.65l26.785,26.784
        c0.069,0.069,0.146,0.146,0.215,0.216L1057,861.622z" class=""></path>
      <path data-v-de5d35fc="" id="c29" desc="32-54" d="M825.903,1766.224c-42.443-9.393-95.229-27.413-140.313-61.064
        c-0.001-0.001-0.002-0.002-0.003-0.003c-8.012-5.978-15.787-12.439-23.237-19.438c-45.843-43.079-71.744-87.451-85.607-125.428
        l-19.649,12.588c-1.105,0.709-2.32,1.487-3.598,2.306c15.709,40.463,43.619,86.312,90.369,130.215
        c8.224,7.727,16.778,14.837,25.551,21.38c0.011,0.009,0.021,0.019,0.033,0.023c51.098,38.061,109.384,57.021,154.915,66.722
        c17.504,3.72,33.131,6.055,45.789,7.523v-27.191C857.785,1772.325,842.666,1769.945,825.903,1766.224z" class=""></path>
      <path data-v-de5d35fc="" id="c30" desc="26-44" d="M1163.785,1252.125c-59.116,0.406-130.667,2.557-199.524,8.911
        c-55.761,5.146-98.553,10.671-134.07,17.026c-0.017,0.003-0.032,0.008-0.049,0.012c-65.48,11.688-106.263,26.398-155.962,45.91
        c-40.842,16.028-72.203,42.696-94.393,69.832c-7.867,9.624-14.572,19.313-20.181,28.688l22.743,14.571
        c8.461-14.272,20.061-29.741,34.705-44.083c17.884-17.528,40.231-33.384,66.983-43.873c49.494-19.391,87.304-33.068,150.899-44.48
        c0.004,0,0.007,0,0.011-0.001h-0.002c34.479-6.171,76.534-11.615,131.799-16.72c64.529-5.958,131.945-8.182,188.864-8.728
        c-3.286-7.78-1-17.204,7.697-26.554L1163.785,1252.125z" class="" mask="url(#g26Mask)"></path>
      <path data-v-de5d35fc="" id="c31" desc="19-49" d="M1418.689,1572.876l-20.479-13.118c-6.938,16.91-14.863,30.873-21.591,41.648
        c-3.297,5.278-6.956,11.771-11.583,19.36c-13.83,22.703-36.027,55.155-75.773,84.897c-0.004,0.003-0.007,0.006-0.01,0.009
        c-39.99,29.849-97.828,57.094-183.714,67.661l0.001,27.211c91.808-10.764,155.623-40.111,199.89-73.257
        c28.987-21.667,49.578-44.854,64.408-65.047c7.415-10.095,13.396-19.443,18.266-27.438c4.854-7.979,8.639-14.656,11.411-19.094
        c6.648-10.632,14.56-24.448,21.771-41.175C1420.377,1573.957,1419.502,1573.396,1418.689,1572.876z" class=""></path>
      <path data-v-de5d35fc="" id="c32" desc="37-40" d="M1431.542,1397.834c-4.482-15.569-10.512-29.764-17.514-42.612
        c-0.002-0.003-0.003-0.007-0.005-0.01c-13.143-24.074-29.615-43.384-45.887-58.522c2.12,11.844-3.211,20.482-14.082,23.893
        c13.076,12.836,25.947,28.618,36.257,47.557c0.007,0.006,0.011,0.014,0.013,0.02c6.119,11.229,11.359,23.578,15.271,37.15
        c2.034,7.07,3.707,13.979,5.073,20.729l8.021-5.142c4.428-2.834,10.43-6.681,16.193-10.375
        C1433.876,1406.334,1432.771,1402.108,1431.542,1397.834z" class=""></path>
      <path data-v-de5d35fc="" id="c33" desc="21-45" d="M1222.598,956.859c-0.003-0.008-0.006-0.015-0.01-0.021
        c-41.804-75.814-88.673-125.688-117.046-151.811v2.772c0,10.146-2.607,19.632-7.132,27.733
        c27.005,26.621,65.637,70.997,100.532,134.337l0,0h-0.001c26.318,47.776,50.562,106.315,66.189,176.304
        c9.354-5.103,19.244-4.937,27.654,0.132C1276.625,1071.016,1250.8,1008.012,1222.598,956.859z" class=""></path>
      <path data-v-de5d35fc="" id="c34" desc="59-6" d="M1343.438,1522.33c-6.998-7.669-10.563-16.268-10.712-24.885
        c-22.548,12.249-59.235,27.718-114.938,39.643c-0.012,0.002-0.021,0.007-0.033,0.009c-31.175,6.662-68.307,12.192-112.211,15.388
        l-0.001,27.07c45.926-3.255,84.938-9.02,117.889-16.062c0.004-0.001,0.011-0.002,0.015-0.003
        C1279.785,1551.468,1318.4,1535.651,1343.438,1522.33z" class=""></path>
      <path data-v-de5d35fc="" id="c35" desc="50-27" d="M870.153,1579.602v-27.065c-48.748-3.517-89.164-9.904-122.37-17.534
        c-0.01-0.002-0.021-0.003-0.03-0.006c-49.831-11.452-83.425-25.668-104.711-37.141c-0.256,8.589-3.894,17.142-10.949,24.761
        c23.558,12.472,58.979,27.081,109.646,38.702c0.008,0.002,0.017,0.002,0.022,0.004
        C776.86,1569.38,819.279,1576.027,870.153,1579.602z" class=""></path>
      <!----><!----><!----><!----><!----><!----><!----><!----><!---->
      <path data-v-de5d35fc="" id="c27" desc="46-29" d="M1056.999,1169.604l-0.213,0.217l-26.787,26.781l-0.001,200.687l25.563,0.001
        c0.482,0,0.959,0.021,1.438,0.037V1169.604z" class="defined"></path>
      <!---->
    </g>
    <g data-v-de5d35fc="" id="gates">
      <g data-v-de5d35fc="" id="g26" mask="url(#g26Mask)" class="defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg26"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg26"></use>
        <path data-v-de5d35fc="" id="m" d="M1163.784,1252.125c-59.116,0.406-130.667,2.557-199.524,8.911c-55.761,5.146-98.553,10.671-134.07,17.026
          c-7.34,1.312-12.226,8.326-10.912,15.666c0.007,0.035,0.018,0.068,0.025,0.104c37.474-7.313,83.476-13.563,146.199-19.354
          c64.968-5.996,132.598-8.229,189.708-8.781c1.346-4.252,4.006-8.663,8.096-13.06L1163.784,1252.125z"></path>
      </g>
      <g data-v-de5d35fc="" id="g64" class="defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg64"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg64"></use>
        <polygon data-v-de5d35fc="" id="m" points="918,249.292 918,273.61 931.5,273.61 931.5,273.61 931.5,249.293"></polygon>
      </g>
      <!----><!----><!----><!----><!---->
      <g data-v-de5d35fc="" id="g58" class="defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg58"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg58"></use>
        <path data-v-de5d35fc="" id="m" d="M870.152,1892.351c-24.244-0.9-60.271-4.327-101.605-14.964
          c-50.726-13.05-109.366-36.899-164.461-80.09c-5.813-4.554-14.186-3.584-18.813,2.137
          c106.192,88.674,227.598,104.377,284.959,106.431c-0.047-0.896-0.08-1.794-0.08-2.701L870.152,1892.351z"></path>
      </g>
      <!----><!---->
      <g data-v-de5d35fc="" id="g54" class="defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg54"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg54"></use>
        <path data-v-de5d35fc="" id="m" d="M870.152,1773.851c-12.367-1.524-27.486-3.904-44.25-7.627
          c-42.445-9.393-95.231-27.413-140.316-61.07c-5.891-4.393-14.183-3.248-18.688,2.506c68.592,56.335,156.215,74.161,203.253,79.793
          L870.152,1773.851L870.152,1773.851z"></path>
      </g>
      <!---->
      <g data-v-de5d35fc="" id="g52" class="inChannel defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg52"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg52"></use>
        <path data-v-de5d35fc="" id="m" d="M1029.999,1677.167v38.117l13.501,0.001v-51.619
          C1036.043,1663.666,1029.999,1669.71,1029.999,1677.167z"></path>
      </g>
      <!---->
      <g data-v-de5d35fc="" id="g50" class="defined mixed">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg50"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg50"></use>
        <path data-v-de5d35fc="" id="m" d="M747.784,1535c-49.848-11.454-83.452-25.674-104.743-37.146
          c-0.136,4.567-1.239,9.125-3.295,13.537c24.455,12.776,62.44,28.047,118.197,39.648
          C759.524,1543.821,755.003,1536.658,747.784,1535z"></path>
      </g>
      <!----><!----><!---->
      <g data-v-de5d35fc="" id="g46" class="inChannel defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg46"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg46"></use>
        <path data-v-de5d35fc="" id="m" d="M1029.999,1312.499c0,7.456,6.045,13.502,13.501,13.502v-142.899l-13.501,13.499V1312.499z"></path>
      </g>
      <g data-v-de5d35fc="" id="g45" class="defined mixed">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg45"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg45"></use>
        <path data-v-de5d35fc="" id="m" d="M1198.942,969.871c2.459,4.463,7.073,6.986,11.834,6.987c2.157,0,4.343-0.526,6.373-1.618
          c-39.021-74.416-83.868-124.925-113.354-153.313c-1.219,4.839-3.043,9.41-5.387,13.606
          C1125.415,862.155,1164.047,906.53,1198.942,969.871z"></path>
      </g>
      <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!---->
      <g data-v-de5d35fc="" id="g29" class="inChannel defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg29"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg29"></use>
        <path data-v-de5d35fc="" id="m" d="M1030.001,1312.46h-0.002v84.785l13.502,0.001v-98.286
          C1036.045,1298.96,1030.001,1305.004,1030.001,1312.46z"></path>
      </g>
      <g data-v-de5d35fc="" id="g28" class="defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg28"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg28"></use>
        <path data-v-de5d35fc="" id="m" d="M644.288,1753.139c-12.197-9.992-24.336-21.171-36.29-33.677
          c-40.375-42.257-65.288-86.698-80.589-127.571l-11.619,7.442c15.952,41.628,41.582,86.698,82.45,129.457
          c15.774,16.504,31.893,30.789,48.074,43.159C650.872,1766.186,649.995,1757.814,644.288,1753.139z"></path>
      </g>
      <!---->
      <g data-v-de5d35fc="" id="g25" class="defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg25"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg25"></use>
        <path data-v-de5d35fc="" id="m" d="M1176.793,1137.15c2.545,2.185,5.672,3.255,8.782,3.255c3.677,0,7.321-1.507,9.974-4.419
          c-17.047-16.431-38.693-32.316-65.874-44.522c-1.697,4.13-3.965,8.082-6.815,11.729
          C1144.309,1112.663,1162.063,1124.544,1176.793,1137.15z"></path>
      </g>
      <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!---->
      <g data-v-de5d35fc="" id="g12" class="defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg12"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg12"></use>
        <path data-v-de5d35fc="" id="m" d="M1105.543,755.267c0,0.374,0,0.752,0,1.112v14.135
          c42.678,23.082,114.727,76.432,200.943,198.172c19.367,27.367,36.477,55.505,51.576,83.759c2.431,4.55,7.096,7.14,11.918,7.14
          c2.113,0,4.257-0.503,6.256-1.551c-16.828-32.702-36.27-65.41-58.73-97.147C1226.061,831.676,1149.416,777.569,1105.543,755.267z"></path>
      </g>
      <g data-v-de5d35fc="" id="g11" class="defined mixed">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg11"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg11"></use>
        <path data-v-de5d35fc="" id="m" d="M1029.999,500.706v45.683c0,7.456,6.043,13.501,13.501,13.501v-80.259L1029.999,500.706z"></path>
      </g>
      <g data-v-de5d35fc="" id="g9" class="inChannel defined mixed">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg9"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg9"></use>
        <path data-v-de5d35fc="" id="m" d="M1029.998,1677.166c0,7.457,6.046,13.502,13.502,13.502v-54.859l-13.502,0.001V1677.166z"></path>
      </g>
      <!----><!----><!----><!---->
      <g data-v-de5d35fc="" id="g4" class="defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg4"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg4"></use>
        <polygon data-v-de5d35fc="" id="m" points="1043.499,273.61 1030,273.61 1029.998,273.61 1029.998,300.435 1043.5,300.436
          1043.5,273.61   "></polygon>
      </g>
      <!---->
      <g data-v-de5d35fc="" id="g2" class="defined c2">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg2"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg2"></use>
        <path data-v-de5d35fc="" id="m" d="M973.999,1215.757v96.22c0,7.456,6.043,13.502,13.5,13.502l0,0v-107.816
          C982.952,1217.645,978.407,1217.009,973.999,1215.757z"></path>
      </g>
      <!----><!---->
      <g data-v-de5d35fc="" id="g34" mask="url(#g34Mask)" class="defined c1">
        <use data-v-de5d35fc="" id="h" xlink:href="#sg34"></use>
        <use data-v-de5d35fc="" id="s" xlink:href="#sg34"></use>
        <path data-v-de5d35fc="" id="m" d="M552.613,1320.574c-17.04-26.904-27.514-79.019-10.944-124.687c1.4-4.229,2.844-8.493,4.323-12.774
          c-6.933-2.351-14.504,1.256-17.013,8.172v-0.002c-6.479,17.863-9.146,36.363-9.15,54.082c0.004,16.199,2.229,31.762,5.97,45.779
          c3.76,14.022,8.991,26.489,15.408,36.649c29.484,46.521,116.416,128.015,328.945,165.035v-13.703
          C661.977,1442.375,579.419,1362.898,552.613,1320.574z"></path>
      </g>
      <!----><!----><!---->
    </g>
    <g data-v-de5d35fc="" id="channelHighlights">
      <!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!---->
      <rect data-v-de5d35fc="" id="c19" x="1039.998" y="1635.808" width="7" height="79.479" class="defined"></rect>
      <!----><!----><!----><!----><!----><!----><!---->
      <polygon data-v-de5d35fc="" id="c27" points="1046.999,1179.604 1040,1186.601 1040,1397.284 1046.999,1397.285" class="defined"></polygon>
      <!----><!----><!----><!----><!----><!----><!----><!---->
    </g>
    <g data-v-de5d35fc="" id="centers">
      <g data-v-de5d35fc="" id="root" class="defined">
        <use data-v-de5d35fc="" id="shape" xlink:href="#croot"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1075.832,1881.926
          c-0.001,20.673-16.539,37.431-36.94,37.432l-35.271,0.001c-8.159,0.001-21.39,0.001-29.549,0l-35.269-0.001
          c-20.402-0.001-36.94-16.759-36.941-37.432l-0.001-35.74c-0.001-8.27-0.001-21.674,0-29.943l0.001-35.74
          c0-20.674,16.539-37.432,36.941-37.432l35.269-0.002c8.159,0,21.391,0,29.549,0l35.271,0.002c20.401,0,36.939,16.758,36.94,37.432
          v35.74c0.002,8.27,0.002,21.674,0,29.943V1881.926z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g53" transform="translate(910, 1717)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">53</text>
          </g>
          <g data-v-de5d35fc="" id="g60" transform="translate(967, 1717)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">60</text>
          </g>
          <g data-v-de5d35fc="" id="g52" transform="translate(1023, 1717)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">52</text>
          </g>
          <g data-v-de5d35fc="" id="g54" transform="translate(872, 1765)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">54</text>
          </g>
          <g data-v-de5d35fc="" id="g38" transform="translate(872, 1824)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">38</text>
          </g>
          <g data-v-de5d35fc="" id="g58" transform="translate(872, 1882)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">58</text>
          </g>
          <g data-v-de5d35fc="" id="g41" transform="translate(1061, 1882)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">41</text>
          </g>
          <g data-v-de5d35fc="" id="g39" transform="translate(1061, 1824)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">39</text>
          </g>
          <g data-v-de5d35fc="" id="g19" transform="translate(1061, 1765)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">19</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="sacral" class="defined">
        <use data-v-de5d35fc="" id="shape" xlink:href="#csacral"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1080.832,1570.433c-0.001,21.977-17.582,39.793-39.271,39.794l-37.494,0.001c-8.676,0.001-22.741,0.001-31.416,0l-37.494-0.001
          c-21.689-0.001-39.27-17.817-39.271-39.794l-0.001-37.994c-0.001-8.791-0.001-23.044,0-31.834l0.001-37.994
          c0.001-21.978,17.582-39.794,39.271-39.794l37.494-0.002c8.675,0,22.74,0,31.416,0l37.494,0.002
          c21.688,0,39.27,17.816,39.271,39.794l0.001,37.994c0.001,8.79,0.001,23.043,0,31.834L1080.832,1570.433z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g59" transform="translate(1061, 1541)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">59</text>
          </g>
          <g data-v-de5d35fc="" id="g27" transform="translate(872, 1541)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">27</text>
          </g>
          <g data-v-de5d35fc="" id="g34" transform="translate(872, 1451)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">34</text>
          </g>
          <g data-v-de5d35fc="" id="g5" transform="translate(910, 1400)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">5</text>
          </g>
          <g data-v-de5d35fc="" id="g14" transform="translate(967, 1400)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">14</text>
          </g>
          <g data-v-de5d35fc="" id="g29" transform="translate(1023, 1400)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">29</text>
          </g>
          <g data-v-de5d35fc="" id="g42" transform="translate(910, 1590)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">42</text>
          </g>
          <g data-v-de5d35fc="" id="g3" transform="translate(967, 1590)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">3</text>
          </g>
          <g data-v-de5d35fc="" id="g9" transform="translate(1023, 1590)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">9</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="solar_plexus" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#csolar_plexus"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1502.303,1393.688c23.323-14.938,42.232-2.826,42.232,27.054l0.003,61.094c0.001,7.47,0.001,19.582,0,27.051l-0.003,61.094
          c0,29.88-18.909,41.993-42.232,27.054l-47.689-30.545c-5.83-3.735-15.283-9.79-21.115-13.525l-47.686-30.549
          c-23.322-14.941-23.322-39.165,0-54.107l47.686-30.548c5.832-3.735,15.285-9.791,21.115-13.526L1502.303,1393.688z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g49" transform="translate(1397, 1524)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">49</text>
          </g>
          <g data-v-de5d35fc="" id="g55" transform="translate(1449, 1556)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">55</text>
          </g>
          <g data-v-de5d35fc="" id="g30" transform="translate(1502, 1590)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">30</text>
          </g>
          <g data-v-de5d35fc="" id="g6" transform="translate(1337, 1474)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">6</text>
          </g>
          <g data-v-de5d35fc="" id="g37" transform="translate(1405, 1421)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">37</text>
          </g>
          <g data-v-de5d35fc="" id="g22" transform="translate(1459, 1385)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">22</text>
          </g>
          <g data-v-de5d35fc="" id="g36" transform="translate(1512, 1356)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">36</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="spleen" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#cspleen"></use>
        <path data-v-de5d35fc="" id="highlight" d="M476.202,1395.62c-23.226-14.876-42.053-2.815-42.055,26.939l-0.002,60.835c-0.001,7.438-0.001,19.498,0,26.937l0.002,60.834
          c0.002,29.755,18.829,41.815,42.055,26.939l47.486-30.415c5.807-3.72,15.22-9.749,21.026-13.468l47.484-30.42
          c23.224-14.878,23.224-39.001,0-53.879l-47.484-30.419c-5.806-3.719-15.219-9.749-21.026-13.469L476.202,1395.62z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g28" transform="translate(484, 1556)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">28</text>
          </g>
          <g data-v-de5d35fc="" id="g18" transform="translate(430, 1590)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">18</text>
          </g>
          <g data-v-de5d35fc="" id="g32" transform="translate(535, 1524)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">32</text>
          </g>
          <g data-v-de5d35fc="" id="g50" transform="translate(590, 1474)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">50</text>
          </g>
          <g data-v-de5d35fc="" id="g44" transform="translate(534, 1426)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">44</text>
          </g>
          <g data-v-de5d35fc="" id="g57" transform="translate(473, 1387)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">57</text>
          </g>
          <g data-v-de5d35fc="" id="g48" transform="translate(418, 1357)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">48</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="heart" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#cheart"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1330.249,1270.111
          c7.364,15.766-2.11,25.95-21.164,22.748l-39.805-6.688c-4.824-0.811-12.645-2.125-17.468-2.937l-39.971-6.719
          c-19.054-3.204-25.025-15.983-13.336-28.546l24.415-26.242c2.958-3.182,7.755-8.337,10.716-11.518l24.52-26.353
          c11.688-12.561,27.136-9.965,34.502,5.8l15.389,32.932c1.864,3.99,4.888,10.462,6.752,14.453L1330.249,1270.111z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g21" transform="translate(1258, 1144)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">21</text>
          </g>
          <g data-v-de5d35fc="" id="g51" transform="translate(1223, 1178)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">51</text>
          </g>
          <g data-v-de5d35fc="" id="g26" transform="translate(1162, 1247)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">26</text>
          </g>
          <g data-v-de5d35fc="" id="g40" transform="translate(1319, 1276)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">40</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="g" class="defined">
        <use data-v-de5d35fc="" id="shape" xlink:href="#cg"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1016.189,1172.52
          c-14.898,14.897-39.054,14.896-53.953-0.002l-25.756-25.755c-5.959-5.96-15.621-15.622-21.581-21.581l-25.755-25.757
          c-14.898-14.9-14.898-39.056,0-53.953l25.754-25.757c5.959-5.959,15.621-15.62,21.58-21.58l25.756-25.754
          c14.898-14.898,39.053-14.897,53.952,0.001l25.757,25.755c5.959,5.959,15.621,15.622,21.58,21.582l25.755,25.757
          c14.898,14.899,14.898,39.055,0.001,53.952l-25.755,25.757c-5.959,5.959-15.62,15.621-21.58,21.58L1016.189,1172.52z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g10" transform="translate(844, 1048)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">10</text>
          </g>
          <g data-v-de5d35fc="" id="g25" transform="translate(1082, 1052)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">25</text>
          </g>
          <g data-v-de5d35fc="" id="g15" transform="translate(910, 1123)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">15</text>
          </g>
          <g data-v-de5d35fc="" id="g46" transform="translate(1020, 1120)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">46</text>
          </g>
          <g data-v-de5d35fc="" id="g2" transform="translate(962, 1167)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">2</text>
          </g>
          <g data-v-de5d35fc="" id="g7" transform="translate(910, 972)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">7</text>
          </g>
          <g data-v-de5d35fc="" id="g13" transform="translate(1020, 972)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">13</text>
          </g>
          <g data-v-de5d35fc="" id="g1" transform="translate(962, 928)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">1</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="throat" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#cthroat"></use>
        <path data-v-de5d35fc="" id="highlight" d="
          M1075.846,790.662c-0.001,23.311-16.539,42.208-36.94,42.209l-35.271,0.001c-8.158,0.001-21.391,0.001-29.549,0l-35.27-0.001
          c-20.402-0.001-36.94-18.898-36.941-42.209V750.36c-0.002-9.325-0.002-24.44,0-33.765v-40.302
          c0.001-23.313,16.539-42.209,36.941-42.209l35.27-0.002c8.158,0,21.391,0,29.549,0l35.271,0.002
          c20.401,0,36.939,18.896,36.94,42.209v40.302c0.002,9.325,0.002,24.44,0,33.765V790.662z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g62" transform="translate(910, 610)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">62</text>
          </g>
          <g data-v-de5d35fc="" id="g23" transform="translate(967, 610)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">23</text>
          </g>
          <g data-v-de5d35fc="" id="g56" transform="translate(1023, 610)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">56</text>
          </g>
          <g data-v-de5d35fc="" id="g31" transform="translate(910, 816)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">31</text>
          </g>
          <g data-v-de5d35fc="" id="g8" transform="translate(967, 816)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">8</text>
          </g>
          <g data-v-de5d35fc="" id="g33" transform="translate(1023, 816)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">33</text>
          </g>
          <g data-v-de5d35fc="" id="g20" transform="translate(872, 733)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">20</text>
          </g>
          <g data-v-de5d35fc="" id="g16" transform="translate(872, 675)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">16</text>
          </g>
          <g data-v-de5d35fc="" id="g35" transform="translate(1061, 675)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">35</text>
          </g>
          <g data-v-de5d35fc="" id="g12" transform="translate(1061, 733)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">12</text>
          </g>
          <g data-v-de5d35fc="" id="g45" transform="translate(1061, 786)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">45</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="ajna" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#cajna"></use>
        <path data-v-de5d35fc="" id="highlight" d="M888.728,364.45
          c-15.317-23.77-2.898-43.039,27.739-43.039l62.644-0.002c7.659-0.001,20.078-0.001,27.739,0l62.643,0.002
          c30.639,0,43.057,19.27,27.738,43.039l-31.318,48.599c-3.83,5.942-10.039,15.575-13.869,21.518l-31.324,48.596
          c-15.32,23.768-40.158,23.768-55.479,0l-31.324-48.596c-3.831-5.942-10.039-15.576-13.869-21.518L888.728,364.45z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g47" transform="translate(910, 306)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">47</text>
          </g>
          <g data-v-de5d35fc="" id="g24" transform="translate(967, 306)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">24</text>
          </g>
          <g data-v-de5d35fc="" id="g4" transform="translate(1023, 306)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">4</text>
          </g>
          <g data-v-de5d35fc="" id="g11" transform="translate(1020, 415)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">11</text>
          </g>
          <g data-v-de5d35fc="" id="g17" transform="translate(916, 415)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">17</text>
          </g>
          <g data-v-de5d35fc="" id="g43" transform="translate(966, 480)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">43</text>
          </g>
        </g>
      </g>
      <g data-v-de5d35fc="" id="head" class="">
        <use data-v-de5d35fc="" id="shape" xlink:href="#chead"></use>
        <path data-v-de5d35fc="" id="highlight" d="M1093.151,179.977
          c14.939,23.324,2.827,42.232-27.053,42.232l-61.093,0.003c-7.47,0-19.582,0-27.051,0l-61.093-0.003
          c-29.879,0-41.991-18.909-27.052-42.232l30.544-47.688c3.734-5.831,9.79-15.284,13.525-21.115l30.549-47.685
          c14.941-23.323,39.165-23.323,54.106,0l30.549,47.685c3.735,5.831,9.791,15.284,13.525,21.115L1093.151,179.977z"></path>
        <g data-v-de5d35fc="" id="nums">
          <g data-v-de5d35fc="" id="g64" transform="translate(910, 198)" class="defined">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">64</text>
          </g>
          <g data-v-de5d35fc="" id="g61" transform="translate(967, 198)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">61</text>
          </g>
          <g data-v-de5d35fc="" id="g63" transform="translate(1023, 198)" class="">
            <circle data-v-de5d35fc="" cx="24" cy="24" r="24"></circle>
            <text data-v-de5d35fc="" x="24" y="34">63</text>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>