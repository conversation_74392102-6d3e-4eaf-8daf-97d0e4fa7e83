let iChing = localStorage.getItem('customIChing');
if (iChing) iChing = JSON.parse(iChing);

const gateOrder = [41, 19, 13, 49, 30, 55, 37, 63, 22, 36, 25, 17, 21, 51, 42, 3, 27, 24, 2, 23, 8, 20, 16, 35, 45, 12, 15, 52, 39, 53, 62, 56, 31, 33, 7, 4, 29, 59, 40, 64, 47, 6, 46, 18, 48, 57, 32, 50, 28, 44, 1, 43, 14, 34, 9, 5, 26, 11, 10, 58, 38, 54, 61, 60];
const harmonicOrder = [30, 49, 33, 19, 41, 39, 40, 4, 12, 35, 51, 62, 45, 25, 53, 60, 50, 61, 14, 43, 1, [34, 57, 10], 48, 36, 21, 22, 5, 9, 55, 42, 17, 11, 7, 13, 31, 63, 46, 6, 37, 47, 64, 59, 29, 58, 16, [34, 10, 20], 54, 27, 38, 26, 8, 23, 2, [57, 10, 20], 52, 15, 44, 56, [34, 57, 20], 18, 28, 32, 24, 3];
const svgRaveMandalaGateOrder = [47, 64, 40, 59, 29, 4, 7, 33, 31, 56, 62, 53, 39, 52, 15, 12, 45, 35, 16, 20, 8, 23, 2, 24, 27, 3, 42, 51, 21, 17, 25, 36, 22, 63, 37, 55, 30, 49, 13, 19, 41, 60, 61, 54, 38, 58, 10, 11, 26, 5, 9, 34, 14, 43, 1, 44, 28, 50, 32, 57, 48, 18, 46, 6];

// Planets
const planetGlyphs = {
  Sun: '☉',
  Earth: '⨁',
  NorthNode: '☊',
  SouthNode: '☋',
  Moon: '☽',
  Mercury: '☿',
  Venus: '♀',
  Mars: '♂',
  Jupiter: '♃',
  Saturn: '♄',
  Uranus: '♅',
  Neptune: '♆',
  Pluto: '♇',
};

// Astrological signs
const astrologicalSigns = ["Virgo", "Leo", "Cancer", "Gemini", "Taurus", "Aries", "Pisces", "Aquarius", "Capricorn", "Sagittarius", "Scorpio", "Libra"];
const astrologicalSignSymbols = [
  "♍", // Virgo
  "♌", // Leo
  "♋", // Cancer
  "♊", // Gemini
  "♉", // Taurus
  "♈", // Aries
  "♓", // Pisces
  "♒", // Aquarius
  "♑", // Capricorn
  "♐", // Sagittarius
  "♏", // Scorpio
  "♎" // Libra
];

// Godheads
const godheads = [
  "Maia",
  "Janus",
  "Michael",
  "Mitra",
  "Kali",
  "The Keepers of the Wheel",
  "Vishnu",
  "Prometheus",
  "Hades",
  "Minerva",
  "Christ",
  "Harmonia",
  "Thoth",
  "Maat",
  "Parvati",
  "Lakshmi"
];

// Hexagrams
const iChingHexagramGlyphs = {
  "1": "䷀",
  "2": "䷁",
  "3": "䷂",
  "4": "䷃",
  "5": "䷄",
  "6": "䷅",
  "7": "䷆",
  "8": "䷇",
  "9": "䷈",
  "10": "䷉",
  "11": "䷊",
  "12": "䷋",
  "13": "䷌",
  "14": "䷍",
  "15": "䷎",
  "16": "䷏",
  "17": "䷐",
  "18": "䷑",
  "19": "䷒",
  "20": "䷓",
  "21": "䷔",
  "22": "䷕",
  "23": "䷖",
  "24": "䷗",
  "25": "䷘",
  "26": "䷙",
  "27": "䷚",
  "28": "䷛",
  "29": "䷜",
  "30": "䷝",
  "31": "䷞",
  "32": "䷟",
  "33": "䷠",
  "34": "䷡",
  "35": "䷢",
  "36": "䷣",
  "37": "䷤",
  "38": "䷥",
  "39": "䷦",
  "40": "䷧",
  "41": "䷨",
  "42": "䷩",
  "43": "䷪",
  "44": "䷫",
  "45": "䷬",
  "46": "䷭",
  "47": "䷮",
  "48": "䷯",
  "49": "䷰",
  "50": "䷱",
  "51": "䷲",
  "52": "䷳",
  "53": "䷴",
  "54": "䷵",
  "55": "䷶",
  "56": "䷷",
  "57": "䷸",
  "58": "䷹",
  "59": "䷺",
  "60": "䷻",
  "61": "䷼",
  "62": "䷽",
  "63": "䷾",
  "64": "䷿"
};

// Amino acids
const aminoAcidByGate = {
  1: { name: 'Lysine', ring: 'The Ring of Fire', gates: [1, 14], oppositeGate: 2 },
  2: { name: 'Phenylalanine', ring: 'The Ring of Water', gates: [2, 8], oppositeGate: 1 },
  3: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 50 },
  4: { name: 'Valine', ring: 'The Ring of Union', gates: [4, 7, 29, 59], oppositeGate: 49 },
  5: { name: 'Threonine', ring: 'The Ring of Light', gates: [5, 9, 11, 26], oppositeGate: 35 },
  6: { name: 'Glycine', ring: 'The Ring of Alchemy', gates: [6, 40, 47, 64], oppositeGate: 36 },
  7: { name: 'Valine', ring: 'The Ring of Union', gates: [4, 7, 29, 59], oppositeGate: 13 },
  8: { name: 'Phenylalanine', ring: 'The Ring of Water', gates: [2, 8], oppositeGate: 14 },
  9: { name: 'Threonine', ring: 'The Ring of Light', gates: [5, 9, 11, 26], oppositeGate: 16 },
  10: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 15 },
  11: { name: 'Threonine', ring: 'The Ring of Light', gates: [5, 9, 11, 26], oppositeGate: 12 },
  12: { name: 'None (Terminator Codon)', ring: 'The Ring of Trials', gates: [12, 33, 56], oppositeGate: 11 },
  13: { name: 'Glutamine', ring: 'The Ring of Purification', gates: [13, 30], oppositeGate: 7 },
  14: { name: 'Lysine', ring: 'The Ring of Fire', gates: [1, 14], oppositeGate: 8 },
  15: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 10 },
  16: { name: 'Cysteine', ring: 'The Ring of Prosperity', gates: [16, 45], oppositeGate: 9 },
  17: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 18 },
  18: { name: 'Alanine', ring: 'The Ring of Matter', gates: [18, 46, 48, 57], oppositeGate: 17 },
  19: { name: 'Isoleucine', ring: 'The Ring of Gaia', gates: [19, 60, 61], oppositeGate: 33 },
  20: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 34 },
  21: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 48 },
  22: { name: 'Proline', ring: 'The Ring of Divinity', gates: [22, 36, 37, 63], oppositeGate: 47 },
  23: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 43 },
  24: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 44 },
  25: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 46 },
  26: { name: 'Threonine', ring: 'The Ring of Light', gates: [5, 9, 11, 26], oppositeGate: 45 },
  27: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 28 },
  28: { name: 'Asparaginic Acid', ring: 'The Ring of Illusion', gates: [28, 32], oppositeGate: 27 },
  29: { name: 'Valine', ring: 'The Ring of Union', gates: [4, 7, 29, 59], oppositeGate: 30 },
  30: { name: 'Glutamine', ring: 'The Ring of Purification', gates: [13, 30], oppositeGate: 29 },
  31: { name: 'Tyrosine', ring: 'The Ring of No Return', gates: [31, 62], oppositeGate: 41 },
  32: { name: 'Asparaginic Acid', ring: 'The Ring of Illusion', gates: [28, 32], oppositeGate: 42 },
  33: { name: 'None (Terminator Codon)', ring: 'The Ring of Trials', gates: [12, 33, 56], oppositeGate: 19 },
  34: { name: 'Asparganine', ring: 'The Ring of Destiny', gates: [34, 43], oppositeGate: 20 },
  35: { name: 'Tryptophan', ring: 'The Ring of Miracles', gates: [35], oppositeGate: 5 },
  36: { name: 'Proline', ring: 'The Ring of Divinity', gates: [22, 36, 37, 63], oppositeGate: 6 },
  37: { name: 'Proline', ring: 'The Ring of Divinity', gates: [22, 36, 37, 63], oppositeGate: 40 },
  38: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 39 },
  39: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 38 },
  40: { name: 'Glycine', ring: 'The Ring of Alchemy', gates: [6, 40, 47, 64], oppositeGate: 37 },
  41: { name: 'Methionine (Initiator)', ring: 'The Ring of Origin', gates: [41], oppositeGate: 31 },
  42: { name: 'Leucine', ring: 'The Ring of Life & Death', gates: [3, 20, 23, 24, 27, 42], oppositeGate: 32 },
  43: { name: 'Asparganine', ring: 'The Ring of Destiny', gates: [34, 43], oppositeGate: 23 },
  44: { name: 'Glutamic Acid', ring: 'The Ring of The Illuminati', gates: [44, 50], oppositeGate: 24 },
  45: { name: 'Cysteine', ring: 'The Ring of Prosperity', gates: [16, 45], oppositeGate: 26 },
  46: { name: 'Alanine', ring: 'The Ring of Matter', gates: [18, 46, 48, 57], oppositeGate: 25 },
  47: { name: 'Glycine', ring: 'The Ring of Alchemy', gates: [6, 40, 47, 64], oppositeGate: 22 },
  48: { name: 'Alanine', ring: 'The Ring of Matter', gates: [18, 46, 48, 57], oppositeGate: 21 },
  49: { name: 'Histidine', ring: 'The Ring of The Whirlwind', gates: [49, 55], oppositeGate: 4 },
  50: { name: 'Glutamic Acid', ring: 'The Ring of The Illuminati', gates: [44, 50], oppositeGate: 3 },
  51: { name: 'Arginine', ring: 'The Ring of Humanity', gates: [10, 17, 21, 25, 38, 51], oppositeGate: 57 },
  52: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 58 },
  53: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 54 },
  54: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 53 },
  55: { name: 'Histidine', ring: 'The Ring of The Whirlwind', gates: [49, 55], oppositeGate: 59 },
  56: { name: 'None (Terminator Codon)', ring: 'The Ring of Trials', gates: [12, 33, 56], oppositeGate: 60 },
  57: { name: 'Alanine', ring: 'The Ring of Matter', gates: [18, 46, 48, 57], oppositeGate: 51 },
  58: { name: 'Serine', ring: 'The Ring of Seeking', gates: [15, 39, 52, 53, 54, 58], oppositeGate: 52 },
  59: { name: 'Valine', ring: 'The Ring of Union', gates: [4, 7, 29, 59], oppositeGate: 55 },
  60: { name: 'Isoleucine', ring: 'The Ring of Gaia', gates: [19, 60, 61], oppositeGate: 56 },
  61: { name: 'Isoleucine', ring: 'The Ring of Gaia', gates: [19, 60, 61], oppositeGate: 62 },
  62: { name: 'Tyrosine', ring: 'The Ring of No Return', gates: [31, 62], oppositeGate: 61 },
  63: { name: 'Proline', ring: 'The Ring of Divinity', gates: [22, 36, 37, 63], oppositeGate: 64 },
  64: { name: 'Glycine', ring: 'The Ring of Alchemy', gates: [6, 40, 47, 64], oppositeGate: 63 }
}

const nucleicAcidSequences = {
  "1": "AAA",
  "2": "UUU",
  "3": "CAA",
  "4": "UCA",
  "5": "GUA",
  "6": "UCU",
  "7": "GCA",
  "8": "CUA",
  "9": "GAA",
  "10": "AAU",
  "11": "AAC",
  "12": "CUU",
  "13": "UGU",
  "14": "AUG",
  "15": "GGU",
  "16": "UAA",
  "17": "CUG",
  "18": "GUG",
  "19": "CAU",
  "20": "UCC",
  "21": "UUC",
  "22": "CCA",
  "23": "CGA",
  "24": "GCA",
  "25": "UAU",
  "26": "AUC",
  "27": "CCU",
  "28": "CAA",
  "29": "CUC",
  "30": "GUC",
  "31": "GAG",
  "32": "CCA",
  "33": "AAU",
  "34": "CAA",
  "35": "GCU",
  "36": "GGA",
  "37": "GAC",
  "38": "CAC",
  "39": "GCU",
  "40": "GGA",
  "41": "UAU",
  "42": "CUA",
  "43": "CAU",
  "44": "GCA",
  "45": "AGU",
  "46": "UGU",
  "47": "GAC",
  "48": "CUA",
  "49": "GUA",
  "50": "CAU",
  "51": "CGA",
  "52": "GGA",
  "53": "CAU",
  "54": "UCA",
  "55": "CUA",
  "56": "CGA",
  "57": "GAU",
  "58": "CCU",
  "59": "GGA",
  "60": "CAA",
  "61": "AGC",
  "62": "AUA",
  "63": "AUC",
  "64": "AUG",
};

// Rave Mandala

const yellow = '#FCD34D';
const lightYellow = '#FFF8AD';
const green = '#48BB78';
const brown = '#B7791F';
const red = '#F56565';
const darkRed = '#C23232';
const chocolate = '#2E1503';
const tortilla = '#9A7B4F';
const tawny = '#80471C'
const raveMandalaGateColors = {
  "1": yellow,
  "2": yellow,
  "3": red,
  "4": green,
  "5": red,
  "6": brown,
  "7": yellow,
  "8": brown,
  "9": red,
  "10": yellow,
  "11": green,
  "12": brown,
  "13": yellow,
  "14": red,
  "15": yellow,
  "16": brown,
  "17": green,
  "18": brown,
  "19": red,
  "20": brown,
  "21": red,
  "22": brown,
  "23": brown,
  "24": green,
  "25": yellow,
  "26": red,
  "27": red,
  "28": brown,
  "29": red,
  "30": brown,
  "31": brown,
  "32": brown,
  "33": brown,
  "34": red,
  "35": brown,
  "36": brown,
  "37": brown,
  "38": brown,
  "39": brown,
  "40": red,
  "41": brown,
  "42": red,
  "43": green,
  "44": brown,
  "45": brown,
  "46": yellow,
  "47": green,
  "48": brown,
  "49": brown,
  "50": brown,
  "51": red,
  "52": brown,
  "53": brown,
  "54": brown,
  "55": brown,
  "56": brown,
  "57": brown,
  "58": brown,
  "59": red,
  "60": brown,
  "61": yellow,
  "62": brown,
  "63": yellow,
  "64": yellow
};

const raveMandalaGateColorsExtra = {
  "1": yellow,
  "2": yellow,
  "3": red,
  "4": green,
  "5": red,
  "6": tawny,
  "7": yellow,
  "8": brown,
  "9": red,
  "10": yellow,
  "11": green,
  "12": brown,
  "13": yellow,
  "14": red,
  "15": yellow,
  "16": brown,
  "17": green,
  "18": tortilla,
  "19": chocolate,
  "20": brown,
  "21": darkRed,
  "22": tawny,
  "23": brown,
  "24": green,
  "25": yellow,
  "26": darkRed,
  "27": red,
  "28": tortilla,
  "29": red,
  "30": tawny,
  "31": brown,
  "32": tortilla,
  "33": brown,
  "34": red,
  "35": brown,
  "36": tawny,
  "37": tawny,
  "38": chocolate,
  "39": chocolate,
  "40": darkRed,
  "41": chocolate,
  "42": red,
  "43": green,
  "44": tortilla,
  "45": brown,
  "46": yellow,
  "47": green,
  "48": tortilla,
  "49": tawny,
  "50": tortilla,
  "51": darkRed,
  "52": brown,
  "53": chocolate,
  "54": chocolate,
  "55": tawny,
  "56": brown,
  "57": tortilla,
  "58": chocolate,
  "59": red,
  "60": chocolate,
  "61": lightYellow,
  "62": brown,
  "63": lightYellow,
  "64": lightYellow
};

const gateOf = {
  "1": "Gate of Self-Expression",
  "2": "Gate of the Direction of Self",
  "3": "Gate of Ordering",
  "4": "Gate of Formulization",
  "5": "Gate of Fixed Rhythms",
  "6": "Gate of Friction",
  "7": "Gate of Role of the Self in Interaction",
  "8": "Gate of Contribution",
  "9": "Gate of Focus",
  "10": "Gate of Behavior of the Self",
  "11": "Gate of Ideas",
  "12": "Gate of Caution",
  "13": "Gate of the Listener",
  "14": "Gate of Power Skills",
  "15": "Gate of Extremes",
  "16": "Gate of Skills",
  "17": "Gate of Opinions",
  "18": "Gate of Correction",
  "19": "Gate of Wanting",
  "20": "Gate of the Now",
  "21": "Gate of the Hunter/Huntress",
  "22": "Gate of Openness",
  "23": "Gate of Assimilation",
  "24": "Gate of Rationalizing",
  "25": "Gate of Spirit of Self",
  "26": "Gate of the Egotist",
  "27": "Gate of Caring",
  "28": "Gate of the Game Player",
  "29": "Gate of Saying Yes",
  "30": "Gate of Recognition of Feelings",
  "31": "Gate of Leading",
  "32": "Gate of Continuity",
  "33": "Gate of Privacy",
  "34": "Gate of Might",
  "35": "Gate of Change",
  "36": "Gate of Crisis",
  "37": "Gate of Friendship",
  "38": "Gate of Fighter",
  "39": "Gate of the Provocateur",
  "40": "Gate of Aloneness",
  "41": "Gate of Contraction",
  "42": "Gate of Growth",
  "43": "Gate of Insight",
  "44": "Gate of Alertness",
  "45": "Gate of Gatherer",
  "46": "Gate of the Determination of the Self",
  "47": "Gate of Realizing",
  "48": "Gate of Depth",
  "49": "Gate of Rejection",
  "50": "Gate of Values",
  "51": "Gate of Shock",
  "52": "Gate of Inaction",
  "53": "Gate of Beginnings",
  "54": "Gate of Ambition",
  "55": "Gate of Spirit",
  "56": "Gate of Stimulation",
  "57": "Gate of Intuition",
  "58": "Gate of Aliveness",
  "59": "Gate of Sexuality",
  "60": "Gate of Acceptance",
  "61": "Gate of Mystery",
  "62": "Gate of Detail",
  "63": "Gate of Doubt",
  "64": "Gate of Confusion"
}

const gateNames = {
  "1": "The Creative",
  "2": "The Receptive",
  "3": "Difficulties at the Beginning",
  "4": "Youthful Folly",
  "5": "Waiting",
  "6": "Conflict",
  "7": "The Army",
  "8": "Holding Together",
  "9": "The Taming Power of the Small",
  "10": "Treading",
  "11": "Peace",
  "12": "Standstill",
  "13": "The Fellowship of Man",
  "14": "Possession in Great Measure",
  "15": "Modesty",
  "16": "Enthusiasm",
  "17": "Following",
  "18": "Work on What Has Been Spoilt",
  "19": "Approach",
  "20": "Contemplation",
  "21": "Biting Through",
  "22": "Grace",
  "23": "Splitting Apart",
  "24": "Returning",
  "25": "Innocence",
  "26": "The Taming Power of the Great",
  "27": "Nourishment",
  "28": "Preponderance of the Great",
  "29": "The Abysmal",
  "30": "The Clinging Fire",
  "31": "Influence",
  "32": "Duration",
  "33": "Retreat",
  "34": "The Power of the Great",
  "35": "Progress",
  "36": "Darkening of the Light",
  "37": "The Family",
  "38": "Opposition",
  "39": "Obstruction",
  "40": "Deliverance",
  "41": "Decrease",
  "42": "Increase",
  "43": "Breakthrough",
  "44": "Coming to Meet",
  "45": "Gathering Together",
  "46": "Pushing Upward",
  "47": "Oppression",
  "48": "The Well",
  "49": "Revolution",
  "50": "The Cauldron",
  "51": "The Arousing",
  "52": "Keeping Still",
  "53": "Development",
  "54": "The Marrying Maiden",
  "55": "Abundance",
  "56": "The Wanderer",
  "57": "The Gentle",
  "58": "The Joyous",
  "59": "Dispersion",
  "60": "Limitation",
  "61": "Inner Truth",
  "62": "Preponderance of the Small",
  "63": "After Completion",
  "64": "Before Completion"
}

const gateShortDescriptions = {
  "1": "Creativity Rooted in Unique Direction",
  "2": "The Driver",
  "3": "Mutation is Generated & Empowered",
  "4": "Logic Protects us From Misfortune",
  "5": "The Fixed Pattern is Sacred",
  "6": "Feeling, Emoting, and Sensitivity",
  "7": "The Elected Leader",
  "8": "Leading by Example",
  "9": "Focus Empowers the Entire Process",
  "10": "The Love of Self is Awakeness",
  "11": "Ideas are Stimulation for Reflection",
  "12": "Releasing Awareness in the Proper Spirit",
  "13": "The Hearer of Secrets",
  "14": "Fuel to Empower",
  "15": "The Love of Humanity",
  "16": "The Skills for Living - Life as Art",
  "17": "Grounded in Detail",
  "18": "Essential Learning",
  "19": "Fuel for our Social Needs",
  "20": "I Am Now",
  "21": "Strength of Will",
  "22": "Sharing Spirit with Others",
  "23": "The Elimination of Intolerance",
  "24": "Inpiration Must be Given a Rational Form",
  "25": "The Spiritual Warrior",
  "26": "Fools are Liars and Prophets Too",
  "27": "Care for Oneself First",
  "28": "The Challenge is Life Itself",
  "29": "Energy to Persevere Despite Circumstance",
  "30": "Surrendering to Fate",
  "31": "I Lead",
  "32": "Conservatism",
  "33": "The Revelation of Secrets",
  "34": "Pure Unconditional Power",
  "35": "The Secret is 'No Expectation'",
  "36": "Wait for Emotional Clarity",
  "37": "Ready to Embrace the Outsider",
  "38": "Stubborness Can Overcome the Odds",
  "39": "The Journey is Toward Spirit",
  "40": "The Power of the Will to Deliver",
  "41": "Patience is the Great Virtue",
  "42": "The 'Grail' is the Experience",
  "43": "Listen to Your Own Inner Voice",
  "44": "The Personnel Manager",
  "45": "The Ruler",
  "46": "The Body is the Temple",
  "47": "Wait for the Moment of Realization",
  "48": "A Resource Available in the Now",
  "49": "Potentially Aware and Ritualistically Spiritual",
  "50": "Guarding and Maintaining the Tribe",
  "51": "Arousing Empowers the Direction of Love",
  "52": "Focused and Channeled Energy",
  "53": "Transition and Change",
  "54": "The Drive to Rise Up",
  "55": "The Quality of Spirit is in the Emotional Now",
  "56": "The Storyteller",
  "57": "Penetrate to the Core in the Now",
  "58": "The Vitality to Challenge",
  "59": "Bonding and Intimacy Beyond Words",
  "60": "The Pulsing Pressure to Mutate",
  "61": "The Pulsing Pressure to Mutate",
  "62": "Manifestation Through Detail",
  "63": "Doubt is an Essential Inspiration",
  "64": "Pressurized Mental Activity"
}

const godheadsByGate = {
  "1": "Hades",
  "2": "Maia",
  "3": "Janus",
  "4": "Thoth",
  "5": "Prometheus",
  "6": "Harmonia",
  "7": "Thoth",
  "8": "Maia",
  "9": "Prometheus",
  "10": "Vishnu",
  "11": "Prometheus",
  "12": "Lakshmi",
  "13": "Kali",
  "14": "Hades",
  "15": "Parvati",
  "16": "Lakshmi",
  "17": "Michael",
  "18": "Christ",
  "19": "Keepers of the Wheel",
  "20": "Maia",
  "21": "Michael",
  "22": "Mitra",
  "23": "Maia",
  "24": "Janus",
  "25": "Michael",
  "26": "Prometheus",
  "27": "Janus",
  "28": "Minerva",
  "29": "Thoth",
  "30": "Kali",
  "31": "Maat",
  "32": "Minerva",
  "33": "Maat",
  "34": "Hades",
  "35": "Lakshmi",
  "36": "Mitra",
  "37": "Mitra",
  "38": "Vishnu",
  "39": "Parvati",
  "40": "Harmonia",
  "41": "Keepers of the Wheel",
  "42": "Janus",
  "43": "Hades",
  "44": "Minerva",
  "45": "Lakshmi",
  "46": "Christ",
  "47": "Harmonia",
  "48": "Christ",
  "49": "Kali",
  "50": "Minerva",
  "51": "Michael",
  "52": "Parvati",
  "53": "Parvati",
  "54": "Vishnu",
  "55": "Kali",
  "56": "Maat",
  "57": "Christ",
  "58": "Vishnu",
  "59": "Thoth",
  "60": "Keepers of the Wheel",
  "61": "Keepers of the Wheel",
  "62": "Maat",
  "63": "Mitra",
  "64": "Harmonia"
}


// Rave I'Ching
const fixings = {
  "1.1.exaltingPlanet": "Moon",
  "1.1.detrimentingPlanet": "Uranus",
  "1.2.exaltingPlanet": "Venus",
  "1.2.detrimentingPlanet": "Mars",
  "1.3.exaltingPlanet": "Mars",
  "1.3.detrimentingPlanet": "Earth",
  "1.4.exaltingPlanet": "Earth",
  "1.4.detrimentingPlanet": "Jupiter",
  "1.5.exaltingPlanet": "Mars",
  "1.5.detrimentingPlanet": "Uranus",
  "1.6.exaltingPlanet": "Earth",
  "1.6.detrimentingPlanet": "Pluto",
  "2.1.exaltingPlanet": "Venus",
  "2.1.detrimentingPlanet": "Mars",
  "2.2.exaltingPlanet": "Saturn",
  "2.2.detrimentingPlanet": "Mars",
  "2.3.exaltingPlanet": "Jupiter",
  "2.3.detrimentingPlanet": "Uranus",
  "2.4.exaltingPlanet": "Venus",
  "2.4.detrimentingPlanet": "Mars",
  "2.5.exaltingPlanet": "Mercury",
  "2.5.detrimentingPlanet": "Earth",
  "2.6.exaltingPlanet": "Mercury",
  "2.6.detrimentingPlanet": "Saturn",
  "3.1.exaltingPlanet": "Earth",
  "3.1.detrimentingPlanet": "Mercury",
  "3.2.exaltingPlanet": "Mars",
  "3.2.detrimentingPlanet": "Uranus",
  "3.3.exaltingPlanet": "Venus",
  "3.3.detrimentingPlanet": "Pluto",
  "3.4.exaltingPlanet": "Neptune",
  "3.4.detrimentingPlanet": "Mars",
  "3.5.exaltingPlanet": "Mars",
  "3.5.detrimentingPlanet": "Earth",
  "3.6.exaltingPlanet": "Sun",
  "3.6.detrimentingPlanet": "Pluto",
  "4.1.exaltingPlanet": "Moon",
  "4.1.detrimentingPlanet": "Earth",
  "4.2.exaltingPlanet": "Moon",
  "4.2.detrimentingPlanet": "Mars",
  "4.3.exaltingPlanet": "Venus",
  "4.3.detrimentingPlanet": "Pluto",
  "4.4.exaltingPlanet": "Sun",
  "4.4.detrimentingPlanet": "Saturn",
  "4.5.exaltingPlanet": "Jupiter",
  "4.5.detrimentingPlanet": "Pluto",
  "4.6.exaltingPlanet": "Mercury",
  "4.6.detrimentingPlanet": "Mars",
  "5.1.exaltingPlanet": "Mars",
  "5.1.detrimentingPlanet": "Earth",
  "5.2.exaltingPlanet": "Venus",
  "5.2.detrimentingPlanet": "Pluto",
  "5.3.exaltingPlanet": "Neptune",
  "5.3.detrimentingPlanet": "Moon",
  "5.4.exaltingPlanet": "Uranus",
  "5.4.detrimentingPlanet": "Sun",
  "5.5.exaltingPlanet": "Venus",
  "5.5.detrimentingPlanet": "Pluto",
  "5.6.exaltingPlanet": "Neptune",
  "6.1.exaltingPlanet": "Pluto",
  "6.1.detrimentingPlanet": "Mercury",
  "6.2.exaltingPlanet": "Venus",
  "6.2.detrimentingPlanet": "Mars",
  "6.3.exaltingPlanet": "Neptune",
  "6.3.detrimentingPlanet": "Pluto",
  "6.4.exaltingPlanet": "Sun",
  "6.4.detrimentingPlanet": "Pluto",
  "6.5.exaltingPlanet": "Venus",
  "6.5.detrimentingPlanet": "Moon",
  "6.6.exaltingPlanet": "Mercury",
  "6.6.detrimentingPlanet": "Venus",
  "7.1.exaltingPlanet": "Venus",
  "7.1.detrimentingPlanet": "Mercury",
  "7.2.exaltingPlanet": "Neptune",
  "7.2.detrimentingPlanet": "Mercury",
  "7.3.exaltingPlanet": "Moon",
  "7.3.detrimentingPlanet": "Mercury",
  "7.4.exaltingPlanet": "Sun",
  "7.4.detrimentingPlanet": "Uranus",
  "7.5.exaltingPlanet": "Venus",
  "7.5.detrimentingPlanet": "Neptune",
  "7.6.exaltingPlanet": "Mercury",
  "7.6.detrimentingPlanet": "Uranus",
  "8.1.exaltingPlanet": "Neptune",
  "8.1.detrimentingPlanet": "Mercury",
  "8.2.exaltingPlanet": "Sun",
  "8.2.detrimentingPlanet": "Earth",
  "8.3.exaltingPlanet": "Moon",
  "8.3.detrimentingPlanet": "Saturn",
  "8.4.exaltingPlanet": "Jupiter",
  "8.4.detrimentingPlanet": "Mercury",
  "8.5.exaltingPlanet": "Jupiter",
  "8.5.detrimentingPlanet": "Sun",
  "8.6.exaltingPlanet": "Venus",
  "8.6.detrimentingPlanet": "Pluto",
  "9.1.exaltingPlanet": "Pluto",
  "9.1.detrimentingPlanet": "Mars",
  "9.2.exaltingPlanet": "Pluto",
  "9.2.detrimentingPlanet": "Jupiter",
  "9.3.exaltingPlanet": "Earth",
  "9.3.detrimentingPlanet": "Sun",
  "9.4.exaltingPlanet": "Moon",
  "9.4.detrimentingPlanet": "Mars",
  "9.5.exaltingPlanet": "Jupiter",
  "9.5.detrimentingPlanet": "Earth",
  "9.6.exaltingPlanet": "Moon",
  "9.6.detrimentingPlanet": "Pluto",
  "10.1.exaltingPlanet": "Sun",
  "10.1.detrimentingPlanet": "Moon",
  "10.2.exaltingPlanet": "Mercury",
  "10.2.detrimentingPlanet": "Mars",
  "10.3.exaltingPlanet": "Earth",
  "10.3.detrimentingPlanet": "Moon",
  "10.4.exaltingPlanet": "Uranus",
  "10.4.detrimentingPlanet": "Mercury",
  "10.5.exaltingPlanet": "Jupiter",
  "10.5.detrimentingPlanet": "Mars",
  "10.6.exaltingPlanet": "Pluto",
  "10.6.detrimentingPlanet": "Saturn",
  "11.1.exaltingPlanet": "Moon",
  "11.1.detrimentingPlanet": "Mars",
  "11.2.exaltingPlanet": "Neptune",
  "11.2.detrimentingPlanet": "Mars",
  "11.3.exaltingPlanet": "Pluto",
  "11.3.detrimentingPlanet": "Venus",
  "11.4.exaltingPlanet": "Moon",
  "11.4.detrimentingPlanet": "Sun",
  "11.5.exaltingPlanet": "Moon",
  "11.5.detrimentingPlanet": "Mercury",
  "11.6.exaltingPlanet": "Neptune",
  "11.6.detrimentingPlanet": "Jupiter",
  "12.1.exaltingPlanet": "Venus",
  "12.1.detrimentingPlanet": "Jupiter",
  "12.2.exaltingPlanet": "Saturn",
  "12.2.detrimentingPlanet": "Mercury",
  "12.3.exaltingPlanet": "Neptune",
  "12.3.detrimentingPlanet": "Mars",
  "12.4.exaltingPlanet": "Earth",
  "12.4.detrimentingPlanet": "Mercury",
  "12.5.exaltingPlanet": "Sun",
  "12.5.detrimentingPlanet": "Mars",
  "12.6.exaltingPlanet": "Sun",
  "12.6.detrimentingPlanet": "Earth",
  "13.1.exaltingPlanet": "Venus",
  "13.1.detrimentingPlanet": "Moon",
  "13.2.exaltingPlanet": "Moon",
  "13.2.detrimentingPlanet": "Sun",
  "13.3.exaltingPlanet": "Earth",
  "13.3.detrimentingPlanet": "Venus",
  "13.4.exaltingPlanet": "Pluto",
  "13.4.detrimentingPlanet": "Venus",
  "13.5.exaltingPlanet": "Neptune",
  "13.5.detrimentingPlanet": "Jupiter",
  "13.6.exaltingPlanet": "Mars",
  "13.6.detrimentingPlanet": "Mercury",
  "14.1.exaltingPlanet": "Jupiter",
  "14.1.detrimentingPlanet": "Mercury",
  "14.2.exaltingPlanet": "Jupiter",
  "14.2.detrimentingPlanet": "Mars",
  "14.3.exaltingPlanet": "Earth",
  "14.3.detrimentingPlanet": "Neptune",
  "14.4.exaltingPlanet": "Moon",
  "14.4.detrimentingPlanet": "Mars",
  "14.5.exaltingPlanet": "Sun",
  "14.5.detrimentingPlanet": "Venus",
  "14.6.exaltingPlanet": "Sun",
  "14.6.detrimentingPlanet": "Earth",
  "15.1.exaltingPlanet": "Venus",
  "15.1.detrimentingPlanet": "Mars",
  "15.2.exaltingPlanet": "Sun",
  "15.2.detrimentingPlanet": "Earth",
  "15.3.exaltingPlanet": "Earth",
  "15.3.detrimentingPlanet": "Mercury",
  "15.4.exaltingPlanet": "Jupiter",
  "15.4.detrimentingPlanet": "Saturn",
  "15.5.exaltingPlanet": "Jupiter",
  "15.5.detrimentingPlanet": "Pluto",
  "15.6.exaltingPlanet": "Pluto",
  "15.6.detrimentingPlanet": "Venus",
  "16.1.exaltingPlanet": "Earth",
  "16.1.detrimentingPlanet": "Mercury",
  "16.2.exaltingPlanet": "Sun",
  "16.2.detrimentingPlanet": "Mercury",
  "16.3.exaltingPlanet": "Moon ",
  "16.3.detrimentingPlanet": "Mars",
  "16.4.exaltingPlanet": "Jupiter",
  "16.4.detrimentingPlanet": "Mars",
  "16.5.exaltingPlanet": "Pluto",
  "16.5.detrimentingPlanet": "Moon ",
  "16.6.exaltingPlanet": "Neptune",
  "16.6.detrimentingPlanet": "Jupiter",
  "17.1.exaltingPlanet": "Mars",
  "17.1.detrimentingPlanet": "Venus",
  "17.2.exaltingPlanet": "Sun",
  "17.2.detrimentingPlanet": "Moon",
  "17.3.exaltingPlanet": "Pluto",
  "17.3.detrimentingPlanet": "Earth",
  "17.4.exaltingPlanet": "Pluto",
  "17.4.detrimentingPlanet": "Jupiter",
  "17.5.exaltingPlanet": "Uranus",
  "17.5.detrimentingPlanet": "Mars",
  "17.6.exaltingPlanet": "Moon  ",
  "17.6.detrimentingPlanet": "Jupiter",
  "18.1.exaltingPlanet": "Earth",
  "18.1.detrimentingPlanet": "Jupiter",
  "18.2.exaltingPlanet": "Pluto",
  "18.2.detrimentingPlanet": "Moon ",
  "18.3.exaltingPlanet": "Neptune",
  "18.3.detrimentingPlanet": "Jupiter",
  "18.4.exaltingPlanet": "Earth",
  "18.4.detrimentingPlanet": "Mercury",
  "18.5.exaltingPlanet": "Saturn",
  "18.5.detrimentingPlanet": "Uranus",
  "18.6.exaltingPlanet": "Mars",
  "18.6.detrimentingPlanet": "Moon",
  "19.1.exaltingPlanet": "Sun",
  "19.1.detrimentingPlanet": "Moon",
  "19.2.exaltingPlanet": "Jupiter",
  "19.2.detrimentingPlanet": "Mercury",
  "19.3.exaltingPlanet": "Venus",
  "19.3.detrimentingPlanet": "Moon",
  "19.4.exaltingPlanet": "Mars",
  "19.4.detrimentingPlanet": "Venus",
  "19.5.exaltingPlanet": "Earth",
  "19.5.detrimentingPlanet": "Jupiter",
  "19.6.exaltingPlanet": "Jupiter",
  "19.6.detrimentingPlanet": "Mars",
  "20.1.exaltingPlanet": "Venus",
  "20.1.detrimentingPlanet": "Moon",
  "20.2.exaltingPlanet": "Venus",
  "20.2.detrimentingPlanet": "Moon",
  "20.3.exaltingPlanet": "Sun",
  "20.3.detrimentingPlanet": "Earth",
  "20.4.exaltingPlanet": "Jupiter",
  "20.4.detrimentingPlanet": "Mercury",
  "20.5.exaltingPlanet": "Saturn",
  "20.5.detrimentingPlanet": "Uranus",
  "20.6.exaltingPlanet": "Venus",
  "20.6.detrimentingPlanet": "Mercury",
  "21.1.exaltingPlanet": "Mars",
  "21.1.detrimentingPlanet": "Moon",
  "21.2.exaltingPlanet": "Mars",
  "21.2.detrimentingPlanet": "Neptune",
  "21.3.exaltingPlanet": "Neptune",
  "21.3.detrimentingPlanet": "Jupiter",
  "21.4.exaltingPlanet": "Jupiter",
  "21.4.detrimentingPlanet": "Earth",
  "21.5.exaltingPlanet": "Jupiter",
  "21.5.detrimentingPlanet": "Pluto",
  "21.6.exaltingPlanet": "Pluto",
  "21.6.detrimentingPlanet": "Venus",
  "22.1.exaltingPlanet": "Moon",
  "22.1.detrimentingPlanet": "Mars",
  "22.2.exaltingPlanet": "Sun",
  "22.2.detrimentingPlanet": "Jupiter",
  "22.3.exaltingPlanet": "Saturn",
  "22.3.detrimentingPlanet": "Mars",
  "22.4.exaltingPlanet": "Neptune",
  "22.4.detrimentingPlanet": "Mars",
  "22.5.exaltingPlanet": "Jupiter",
  "22.5.detrimentingPlanet": "Mars",
  "22.6.exaltingPlanet": "Sun",
  "22.6.detrimentingPlanet": "Mars",
  "23.1.exaltingPlanet": "Jupiter",
  "23.1.detrimentingPlanet": "Mars",
  "23.2.exaltingPlanet": "Jupiter",
  "23.2.detrimentingPlanet": "Moon",
  "23.3.exaltingPlanet": "Sun",
  "23.3.detrimentingPlanet": "Pluto",
  "23.4.exaltingPlanet": "Sun",
  "23.4.detrimentingPlanet": "Earth",
  "23.5.exaltingPlanet": "Jupiter",
  "23.5.detrimentingPlanet": "Moon",
  "23.6.exaltingPlanet": "Mars",
  "23.6.detrimentingPlanet": "Jupiter",
  "24.1.exaltingPlanet": "Sun",
  "24.1.detrimentingPlanet": "Pluto",
  "24.2.exaltingPlanet": "Moon",
  "24.2.detrimentingPlanet": "Mars",
  "24.3.exaltingPlanet": "Venus",
  "24.3.detrimentingPlanet": "Jupiter",
  "24.4.exaltingPlanet": "Saturn",
  "24.4.detrimentingPlanet": "Neptune",
  "24.5.exaltingPlanet": "Moon",
  "24.5.detrimentingPlanet": "Mars",
  "24.6.exaltingPlanet": "Jupiter",
  "24.6.detrimentingPlanet": "Pluto",
  "25.1.exaltingPlanet": "Neptune",
  "25.1.detrimentingPlanet": "Mercury",
  "25.2.exaltingPlanet": "Mercury",
  "25.2.detrimentingPlanet": "Mars",
  "25.3.exaltingPlanet": "Mars",
  "25.3.detrimentingPlanet": "Pluto",
  "25.4.exaltingPlanet": "Venus",
  "25.5.exaltingPlanet": "Venus",
  "25.5.detrimentingPlanet": "Jupiter",
  "25.6.exaltingPlanet": "Earth",
  "25.6.detrimentingPlanet": "Uranus",
  "26.1.exaltingPlanet": "Neptune",
  "26.1.detrimentingPlanet": "Mars",
  "26.2.exaltingPlanet": "Sun",
  "26.2.detrimentingPlanet": "Pluto",
  "26.3.exaltingPlanet": "Sun",
  "26.3.detrimentingPlanet": "Saturn",
  "26.4.exaltingPlanet": "Pluto",
  "26.4.detrimentingPlanet": "Saturn",
  "26.5.exaltingPlanet": "Mars",
  "26.5.detrimentingPlanet": "Venus",
  "26.6.exaltingPlanet": "Sun",
  "26.6.detrimentingPlanet": "Moon",
  "27.1.exaltingPlanet": "Sun",
  "27.1.detrimentingPlanet": "Earth",
  "27.2.exaltingPlanet": "Moon",
  "27.2.detrimentingPlanet": "Mars",
  "27.3.exaltingPlanet": "Pluto",
  "27.3.detrimentingPlanet": "Mars",
  "27.4.exaltingPlanet": "Jupiter",
  "27.4.detrimentingPlanet": "Mars",
  "27.5.exaltingPlanet": "Jupiter",
  "27.5.detrimentingPlanet": "Saturn",
  "27.6.exaltingPlanet": "Moon",
  "27.6.detrimentingPlanet": "Pluto",
  "28.1.exaltingPlanet": "Mars",
  "28.1.detrimentingPlanet": "Venus",
  "28.2.exaltingPlanet": "Sun",
  "28.2.detrimentingPlanet": "Jupiter",
  "28.3.exaltingPlanet": "Saturn",
  "28.3.detrimentingPlanet": "Jupiter",
  "28.4.exaltingPlanet": "Jupiter",
  "28.4.detrimentingPlanet": "Mercury",
  "28.5.exaltingPlanet": "Pluto",
  "28.5.detrimentingPlanet": "Sun",
  "28.6.exaltingPlanet": "Pluto",
  "28.6.detrimentingPlanet": "Neptune",
  "29.1.exaltingPlanet": "Mars",
  "29.1.detrimentingPlanet": "Neptune",
  "29.2.exaltingPlanet": "Sun",
  "29.2.detrimentingPlanet": "Venus",
  "29.3.exaltingPlanet": "Mars",
  "29.3.detrimentingPlanet": "Jupiter",
  "29.4.exaltingPlanet": "Saturn",
  "29.4.detrimentingPlanet": "Venus",
  "29.5.exaltingPlanet": "Sun",
  "29.5.detrimentingPlanet": "Earth",
  "29.6.exaltingPlanet": "Mars",
  "29.6.detrimentingPlanet": "Jupiter",
  "30.1.exaltingPlanet": "Sun",
  "30.1.detrimentingPlanet": "Jupiter",
  "30.2.exaltingPlanet": "Sun",
  "30.2.detrimentingPlanet": "Mars",
  "30.3.exaltingPlanet": "Pluto",
  "30.3.detrimentingPlanet": "Jupiter",
  "30.4.exaltingPlanet": "Pluto",
  "30.4.detrimentingPlanet": "Jupiter",
  "30.5.exaltingPlanet": "Jupiter",
  "30.5.detrimentingPlanet": "Pluto",
  "30.6.exaltingPlanet": "Mars",
  "30.6.detrimentingPlanet": "Moon",
  "31.1.exaltingPlanet": "Sun  ",
  "31.1.detrimentingPlanet": "Earth",
  "31.2.exaltingPlanet": "Jupiter",
  "31.2.detrimentingPlanet": "Mercury",
  "31.3.exaltingPlanet": "Sun",
  "31.3.detrimentingPlanet": "Jupiter",
  "31.4.exaltingPlanet": "Moon",
  "31.4.detrimentingPlanet": "Mars",
  "31.5.exaltingPlanet": "Pluto",
  "31.5.detrimentingPlanet": "Moon",
  "31.6.exaltingPlanet": "Sun",
  "31.6.detrimentingPlanet": "Moon",
  "33.1.exaltingPlanet": "Sun",
  "33.1.detrimentingPlanet": "Mars",
  "33.2.exaltingPlanet": "Jupiter",
  "33.2.detrimentingPlanet": "Neptune",
  "33.3.exaltingPlanet": "Jupiter",
  "33.3.detrimentingPlanet": "Mars",
  "33.4.exaltingPlanet": "Pluto",
  "33.4.detrimentingPlanet": "Neptune",
  "33.5.exaltingPlanet": "Pluto",
  "33.5.detrimentingPlanet": "Jupiter",
  "33.6.exaltingPlanet": "Sun",
  "33.6.detrimentingPlanet": "Jupiter",
  "34.1.exaltingPlanet": "Saturn",
  "34.1.detrimentingPlanet": "Pluto",
  "34.2.exaltingPlanet": "Mars",
  "34.2.detrimentingPlanet": "Venus",
  "34.3.exaltingPlanet": "Saturn",
  "34.3.detrimentingPlanet": "Mercury",
  "34.4.exaltingPlanet": "Pluto",
  "34.4.detrimentingPlanet": "Mars",
  "34.5.exaltingPlanet": "Mars",
  "34.5.detrimentingPlanet": "Moon",
  "34.6.exaltingPlanet": "Earth",
  "34.6.detrimentingPlanet": "Jupiter",
  "35.1.exaltingPlanet": "Venus ",
  "35.1.detrimentingPlanet": "Neptune",
  "35.2.exaltingPlanet": "Venus",
  "35.2.detrimentingPlanet": "Moon",
  "35.3.exaltingPlanet": "Jupiter",
  "35.3.detrimentingPlanet": "Sun",
  "35.4.exaltingPlanet": "Moon",
  "35.4.detrimentingPlanet": "Mars",
  "35.5.exaltingPlanet": "Mercury",
  "35.5.detrimentingPlanet": "Jupiter",
  "35.6.exaltingPlanet": "Saturn",
  "35.6.detrimentingPlanet": "Mars",
  "36.1.exaltingPlanet": "Mars",
  "36.1.detrimentingPlanet": "Jupiter",
  "36.2.exaltingPlanet": "Neptune",
  "36.2.detrimentingPlanet": "Moon",
  "36.3.exaltingPlanet": "Pluto",
  "36.3.detrimentingPlanet": "Jupiter",
  "36.4.exaltingPlanet": "Pluto",
  "36.4.detrimentingPlanet": "Moon",
  "36.5.exaltingPlanet": "Pluto",
  "36.5.detrimentingPlanet": "Mercury",
  "36.6.exaltingPlanet": "Jupiter",
  "36.6.detrimentingPlanet": "Saturn",
  "37.1.exaltingPlanet": "Venus",
  "37.2.exaltingPlanet": "Jupiter",
  "37.2.detrimentingPlanet": "Mercury",
  "37.3.exaltingPlanet": "Jupiter",
  "37.3.detrimentingPlanet": "Mars",
  "37.4.exaltingPlanet": "Moon",
  "37.4.detrimentingPlanet": "Saturn",
  "37.5.exaltingPlanet": "Venus",
  "37.5.detrimentingPlanet": "Mars",
  "37.6.exaltingPlanet": "Venus",
  "37.6.detrimentingPlanet": "Mercury",
  "38.1.exaltingPlanet": "Neptune",
  "38.1.detrimentingPlanet": "Mars",
  "38.2.exaltingPlanet": "Pluto",
  "38.2.detrimentingPlanet": "Moon",
  "38.3.exaltingPlanet": "Sun",
  "38.3.detrimentingPlanet": "Earth",
  "38.4.exaltingPlanet": "Pluto",
  "38.4.detrimentingPlanet": "Mars",
  "38.5.exaltingPlanet": "Saturn",
  "38.5.detrimentingPlanet": "Pluto",
  "38.6.exaltingPlanet": "Saturn",
  "38.6.detrimentingPlanet": "Earth",
  "39.1.exaltingPlanet": "Mars",
  "39.1.detrimentingPlanet": "Mercury",
  "39.2.exaltingPlanet": "Moon",
  "39.2.detrimentingPlanet": "Jupiter",
  "39.3.exaltingPlanet": "Jupiter",
  "39.3.detrimentingPlanet": "Earth",
  "39.4.exaltingPlanet": "Moon",
  "39.4.detrimentingPlanet": "Sun",
  "39.5.exaltingPlanet": "Neptune",
  "39.5.detrimentingPlanet": "Mars",
  "39.6.exaltingPlanet": "Moon",
  "39.6.detrimentingPlanet": "Mars",
  "40.1.exaltingPlanet": "Sun",
  "40.1.detrimentingPlanet": "Moon",
  "40.2.exaltingPlanet": "Sun",
  "40.2.detrimentingPlanet": "Moon",
  "40.3.exaltingPlanet": "Pluto",
  "40.3.detrimentingPlanet": "Mars",
  "40.4.exaltingPlanet": "Uranus",
  "40.4.detrimentingPlanet": "Mars",
  "40.5.exaltingPlanet": "Uranus",
  "40.5.detrimentingPlanet": "Earth",
  "40.6.exaltingPlanet": "Sun",
  "40.6.detrimentingPlanet": "Earth",
  "41.1.exaltingPlanet": "Neptune",
  "41.1.detrimentingPlanet": "Mercury",
  "41.2.exaltingPlanet": "Saturn",
  "41.2.detrimentingPlanet": "Mars",
  "41.3.exaltingPlanet": "Saturn",
  "41.3.detrimentingPlanet": "Moon",
  "41.4.exaltingPlanet": "Earth",
  "41.4.detrimentingPlanet": "Venus",
  "41.5.exaltingPlanet": "Mars",
  "41.5.detrimentingPlanet": "Venus",
  "41.6.exaltingPlanet": "Saturn",
  "41.6.detrimentingPlanet": "Pluto",
  "42.1.exaltingPlanet": "Sun ",
  "42.1.detrimentingPlanet": "Venus",
  "42.2.exaltingPlanet": "Sun",
  "42.2.detrimentingPlanet": "Venus",
  "42.3.exaltingPlanet": "Mars",
  "42.3.detrimentingPlanet": "Moon",
  "42.4.exaltingPlanet": "Moon",
  "42.4.detrimentingPlanet": "Venus",
  "42.5.exaltingPlanet": "Sun",
  "42.5.detrimentingPlanet": "Venus",
  "42.6.exaltingPlanet": "Moon",
  "42.6.detrimentingPlanet": "Saturn",
  "43.1.exaltingPlanet": "Pluto",
  "43.1.detrimentingPlanet": "Venus",
  "43.2.exaltingPlanet": "Pluto",
  "43.2.detrimentingPlanet": "Moon",
  "43.3.exaltingPlanet": "Pluto",
  "43.3.detrimentingPlanet": "Moon",
  "43.4.exaltingPlanet": "Mercury",
  "43.4.detrimentingPlanet": "Jupiter",
  "43.5.exaltingPlanet": "Moon",
  "43.5.detrimentingPlanet": "Venus",
  "43.6.exaltingPlanet": "Sun",
  "43.6.detrimentingPlanet": "Mars",
  "44.1.exaltingPlanet": "Pluto",
  "44.1.detrimentingPlanet": "Venus",
  "44.2.exaltingPlanet": "Jupiter",
  "44.2.detrimentingPlanet": "Mars",
  "44.3.exaltingPlanet": "Mars",
  "44.3.detrimentingPlanet": "Neptune",
  "44.4.exaltingPlanet": "Pluto",
  "44.4.detrimentingPlanet": "Sun",
  "44.5.exaltingPlanet": "Uranus",
  "44.5.detrimentingPlanet": "Mars",
  "44.6.exaltingPlanet": "Pluto",
  "44.6.detrimentingPlanet": "Earth",
  "45.1.exaltingPlanet": "Jupiter",
  "45.1.detrimentingPlanet": "Mars",
  "45.2.exaltingPlanet": "Uranus",
  "45.2.detrimentingPlanet": "Mars",
  "45.3.exaltingPlanet": "Neptune",
  "45.3.detrimentingPlanet": "Mars",
  "45.4.exaltingPlanet": "Jupiter",
  "45.4.detrimentingPlanet": "Mars",
  "45.5.exaltingPlanet": "Uranus",
  "45.5.detrimentingPlanet": "Jupiter",
  "45.6.exaltingPlanet": "Uranus",
  "45.6.detrimentingPlanet": "Jupiter",
  "46.1.exaltingPlanet": "Neptune",
  "46.1.detrimentingPlanet": "Jupiter",
  "46.2.exaltingPlanet": "Sun",
  "46.2.detrimentingPlanet": "Mars",
  "46.3.exaltingPlanet": "Moon",
  "46.3.detrimentingPlanet": "Mars",
  "46.4.exaltingPlanet": "Earth",
  "46.4.detrimentingPlanet": "Pluto",
  "46.5.exaltingPlanet": "Moon",
  "46.5.detrimentingPlanet": "Neptune",
  "46.6.exaltingPlanet": "Saturn",
  "46.6.detrimentingPlanet": "Neptune",
  "47.1.exaltingPlanet": "Saturn",
  "47.1.detrimentingPlanet": "Neptune",
  "47.2.exaltingPlanet": "Saturn",
  "47.2.detrimentingPlanet": "Mercury",
  "47.3.exaltingPlanet": "Jupiter",
  "47.3.detrimentingPlanet": "Mars",
  "47.4.exaltingPlanet": "Saturn",
  "47.4.detrimentingPlanet": "Moon",
  "47.5.exaltingPlanet": "Venus",
  "47.6.detrimentingPlanet": "Sun",
  "48.1.exaltingPlanet": "Moon",
  "48.1.detrimentingPlanet": "Mars",
  "48.2.exaltingPlanet": "Pluto",
  "48.2.detrimentingPlanet": "Venus",
  "48.3.exaltingPlanet": "Moon",
  "48.3.detrimentingPlanet": "Mercury",
  "48.4.exaltingPlanet": "Sun",
  "48.4.detrimentingPlanet": "Earth",
  "48.5.exaltingPlanet": "Mars",
  "48.5.detrimentingPlanet": "Moon",
  "48.6.exaltingPlanet": "Venus",
  "48.6.detrimentingPlanet": "Moon",
  "49.1.exaltingPlanet": "Jupiter",
  "49.1.detrimentingPlanet": "Sun",
  "49.2.exaltingPlanet": "Earth",
  "49.2.detrimentingPlanet": "Pluto",
  "49.3.exaltingPlanet": "Neptune",
  "49.3.detrimentingPlanet": "Pluto",
  "49.4.exaltingPlanet": "Jupiter",
  "49.4.detrimentingPlanet": "Mars",
  "49.5.exaltingPlanet": "Moon",
  "49.5.detrimentingPlanet": "Mars",
  "49.6.exaltingPlanet": "Neptune",
  "49.6.detrimentingPlanet": "Saturn",
  "50.1.exaltingPlanet": "Mars",
  "50.1.detrimentingPlanet": "Venus",
  "50.2.exaltingPlanet": "Sun",
  "50.2.detrimentingPlanet": "Venus",
  "50.3.exaltingPlanet": "Moon",
  "50.3.detrimentingPlanet": "Mercury",
  "50.4.exaltingPlanet": "Saturn",
  "50.4.detrimentingPlanet": "Mars",
  "50.5.exaltingPlanet": "Saturn",
  "50.5.detrimentingPlanet": "Mars",
  "50.6.exaltingPlanet": "Venus",
  "50.6.detrimentingPlanet": "Moon",
  "51.1.exaltingPlanet": "Pluto",
  "51.1.detrimentingPlanet": "Venus",
  "51.2.exaltingPlanet": "Mars",
  "51.2.detrimentingPlanet": "Mercury",
  "51.3.exaltingPlanet": "Sun",
  "51.3.detrimentingPlanet": "Jupiter",
  "51.4.exaltingPlanet": "Uranus",
  "51.4.detrimentingPlanet": "Mercury",
  "51.5.exaltingPlanet": "Sun",
  "51.5.detrimentingPlanet": "Mars",
  "51.6.exaltingPlanet": "Sun",
  "51.6.detrimentingPlanet": "Pluto",
  "52.1.exaltingPlanet": "Earth ",
  "52.1.detrimentingPlanet": "Mars",
  "52.2.exaltingPlanet": "Venus",
  "52.2.detrimentingPlanet": "Mars",
  "52.3.exaltingPlanet": "Saturn",
  "52.3.detrimentingPlanet": "Venus",
  "52.4.exaltingPlanet": "Saturn",
  "52.4.detrimentingPlanet": "Jupiter",
  "52.5.exaltingPlanet": "Earth",
  "52.5.detrimentingPlanet": "Pluto",
  "52.6.exaltingPlanet": "Venus",
  "52.6.detrimentingPlanet": "Neptune",
  "53.1.exaltingPlanet": "Neptune",
  "53.1.detrimentingPlanet": "Venus",
  "53.2.exaltingPlanet": "Moon",
  "53.2.detrimentingPlanet": "Mars",
  "53.3.exaltingPlanet": "Moon",
  "53.3.detrimentingPlanet": "Mars",
  "53.4.exaltingPlanet": "Moon",
  "53.4.detrimentingPlanet": "Venus",
  "53.5.exaltingPlanet": "Neptune",
  "53.5.detrimentingPlanet": "Earth",
  "53.6.exaltingPlanet": "Moon",
  "53.6.detrimentingPlanet": "Pluto",
  "54.1.exaltingPlanet": "Pluto",
  "54.1.detrimentingPlanet": "Venus",
  "54.2.exaltingPlanet": "Saturn",
  "54.2.detrimentingPlanet": "Venus",
  "54.3.exaltingPlanet": "Pluto",
  "54.3.detrimentingPlanet": "Venus",
  "54.5.exaltingPlanet": "Sun",
  "54.6.exaltingPlanet": "Saturn ",
  "54.6.detrimentingPlanet": "Jupiter",
  "55.1.exaltingPlanet": "Jupiter ",
  "55.1.detrimentingPlanet": "Venus",
  "55.2.exaltingPlanet": "Venus",
  "55.2.detrimentingPlanet": "Earth",
  "55.3.exaltingPlanet": "Saturn",
  "55.3.detrimentingPlanet": "Mars",
  "55.4.exaltingPlanet": "Jupiter",
  "55.4.detrimentingPlanet": "Mars",
  "55.5.exaltingPlanet": "Mars",
  "55.5.detrimentingPlanet": "Sun",
  "55.6.exaltingPlanet": "Saturn",
  "55.6.detrimentingPlanet": "Moon",
  "56.1.exaltingPlanet": "Moon",
  "56.1.detrimentingPlanet": "Mars",
  "56.2.exaltingPlanet": "Uranus",
  "56.2.detrimentingPlanet": "Moon",
  "56.3.exaltingPlanet": "Sun",
  "56.3.detrimentingPlanet": "Venus",
  "56.4.exaltingPlanet": "Moon",
  "56.4.detrimentingPlanet": "Mercury",
  "56.5.exaltingPlanet": "Uranus",
  "56.5.detrimentingPlanet": "Mars",
  "56.6.exaltingPlanet": "Sun",
  "56.6.detrimentingPlanet": "Pluto",
  "57.1.exaltingPlanet": "Venus",
  "57.1.detrimentingPlanet": "Moon",
  "57.2.exaltingPlanet": "Venus",
  "57.2.detrimentingPlanet": "Moon",
  "57.3.exaltingPlanet": "Mercury",
  "57.4.exaltingPlanet": "Venus",
  "57.4.detrimentingPlanet": "Mars",
  "57.5.exaltingPlanet": "Pluto",
  "57.5.detrimentingPlanet": "Moon",
  "57.6.exaltingPlanet": "Uranus",
  "57.6.detrimentingPlanet": "Mars",
  "58.1.exaltingPlanet": "Venus",
  "58.1.detrimentingPlanet": "Moon",
  "58.2.detrimentingPlanet": "Uranus",
  "58.3.exaltingPlanet": "Uranus",
  "58.3.detrimentingPlanet": "Mars",
  "58.4.exaltingPlanet": "Pluto",
  "58.4.detrimentingPlanet": "Neptune",
  "58.5.exaltingPlanet": "Moon",
  "58.5.detrimentingPlanet": "Sun",
  "58.6.exaltingPlanet": "Moon",
  "58.6.detrimentingPlanet": "Mercury",
  "59.1.exaltingPlanet": "Sun",
  "59.1.detrimentingPlanet": "Mercury",
  "59.2.exaltingPlanet": "Uranus",
  "59.2.detrimentingPlanet": "Pluto",
  "59.3.exaltingPlanet": "Saturn",
  "59.3.detrimentingPlanet": "Mars",
  "59.4.exaltingPlanet": "Venus",
  "59.4.detrimentingPlanet": "Mercury",
  "59.5.exaltingPlanet": "Sun",
  "59.5.detrimentingPlanet": "Uranus",
  "59.6.exaltingPlanet": "Venus",
  "59.6.detrimentingPlanet": "Mercury",
  "60.1.exaltingPlanet": "Venus",
  "60.1.detrimentingPlanet": "Mercury",
  "60.2.exaltingPlanet": "Saturn",
  "60.2.detrimentingPlanet": "Earth",
  "60.3.exaltingPlanet": "Saturn",
  "60.3.detrimentingPlanet": "Mars",
  "60.4.exaltingPlanet": "Mercury",
  "60.4.detrimentingPlanet": "Venus",
  "60.5.exaltingPlanet": "Neptune",
  "60.5.detrimentingPlanet": "Jupiter",
  "60.6.exaltingPlanet": "Uranus",
  "60.6.detrimentingPlanet": "Mercury",
  "61.1.exaltingPlanet": "Neptune ",
  "61.1.detrimentingPlanet": "Venus",
  "61.2.exaltingPlanet": "Moon",
  "61.2.detrimentingPlanet": "Mars",
  "61.3.exaltingPlanet": "Moon",
  "61.3.detrimentingPlanet": "Mars",
  "61.4.exaltingPlanet": "Saturn",
  "61.4.detrimentingPlanet": "Jupiter",
  "61.5.exaltingPlanet": "Saturn ",
  "61.5.detrimentingPlanet": "Mars",
  "61.6.exaltingPlanet": "Pluto",
  "61.6.detrimentingPlanet": "Mars",
  "62.1.exaltingPlanet": "Neptune ",
  "62.1.detrimentingPlanet": "Mars",
  "62.2.exaltingPlanet": "Saturn",
  "62.2.detrimentingPlanet": "Mercury",
  "62.3.exaltingPlanet": "Uranus",
  "62.3.detrimentingPlanet": "Venus",
  "62.4.exaltingPlanet": "Venus",
  "62.4.detrimentingPlanet": "Pluto",
  "62.5.exaltingPlanet": "Moon",
  "62.5.detrimentingPlanet": "Neptune",
  "62.6.exaltingPlanet": "Saturn",
  "62.6.detrimentingPlanet": "Mercury",
  "63.1.exaltingPlanet": "Sun",
  "63.1.detrimentingPlanet": "Mars",
  "63.2.exaltingPlanet": "Jupiter",
  "63.2.detrimentingPlanet": "Mars",
  "63.3.exaltingPlanet": "Jupiter",
  "63.3.detrimentingPlanet": "Saturn",
  "63.4.exaltingPlanet": "Mercury",
  "63.4.detrimentingPlanet": "Mars",
  "63.5.exaltingPlanet": "Sun",
  "63.5.detrimentingPlanet": "Mars",
  "63.6.exaltingPlanet": "Pluto",
  "63.6.detrimentingPlanet": "Jupiter",
  "64.1.exaltingPlanet": "Venus",
  "64.1.detrimentingPlanet": "Mars",
  "64.2.exaltingPlanet": "Venus",
  "64.2.detrimentingPlanet": "Moon",
  "64.3.exaltingPlanet": "Saturn",
  "64.3.detrimentingPlanet": "Moon",
  "64.4.exaltingPlanet": "Moon",
  "64.4.detrimentingPlanet": "Mars",
  "64.5.exaltingPlanet": "Venus",
  "64.5.detrimentingPlanet": "Jupiter",
  "64.6.exaltingPlanet": "Mercury",
  "64.6.detrimentingPlanet": "Venus"
};

const godheadCoordinates = {
  'The Keepers of the Wheel': {
    width: 315,
    height: 215,
    marginLeft: -136,
    marginTop: 100
  },
  'Vishnu': {
    width: 430,
    height: 215,
    marginLeft: -354,
    marginTop: 100
  },
  'Prometheus': {
    width: 543,
    height: 215,
    marginLeft: -590,
    marginTop: 100
  },
  'Hades': {
    width: 743,
    height: 215,
    marginLeft: -720,
    marginTop: 100
  },
  'Minerva': {
    width: 903,
    height: 315,
    marginTop: -100,
    marginLeft: -880
  },
  'Christ': {
    width: 903,
    height: 615,
    marginTop: -157,
    marginLeft: -880
  },
  'Harmonia': {
    width: 903,
    height: 915,
    marginTop: -227,
    marginLeft: -880
  },
  'Thoth': {
    width: 903,
    height: 1360,
    marginTop: -100,
    marginLeft: -880
  },
  'Maat': {
    width: 743,
    height: 1890,
    marginLeft: -720,
    marginTop: 100
  },
  'Parvati': {
    width: 543,
    height: 2100,
    marginLeft: -590,
    marginTop: 300
  },
  'Lakshmi': {
    width: 430,
    height: 2100,
    marginLeft: -354,
    marginTop: 300
  },
  'Maia': {
    width: 315,
    height: 1890,
    marginLeft: -100,
    marginTop: 100
  },
  'Janus': {
    width: 230,
    height: 1380,
    marginLeft: 140,
    marginTop: -100
  },
  'Michael': {
    width: 200,
    height: 615,
    marginTop: -475,
    marginLeft: 80
  },
  'Mitra': {
    width: 200,
    height: 615,
    marginTop: -157,
    marginLeft: 80
  },
  'Kali': {
    width: 230,
    height: 315,
    marginLeft: 140,
    marginTop: -100
  }
};
