GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activeadmin (3.3.0)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.3)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crass (1.0.6)
    csv (3.3.3)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    digest-crc (0.7.0)
      rake (>= 12.0.0, < 14.0.0)
    domain_name (0.6.20240107)
    dotenv (3.1.7)
    dotenv-rails (3.1.7)
      dotenv (= 3.1.7)
      railties (>= 6.1)
    drb (2.2.1)
    erb_lint (0.9.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= *******)
      rainbow
      rubocop (>= 1)
      smart_properties
    erubi (1.13.1)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.1-aarch64-linux-gnu)
    ffi (1.17.1-aarch64-linux-musl)
    ffi (1.17.1-arm-linux-gnu)
    ffi (1.17.1-arm-linux-musl)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86_64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    ffi (1.17.1-x86_64-linux-musl)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    gems (1.3.0)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-api-client (0.53.0)
      google-apis-core (~> 0.1)
      google-apis-generator (~> 0.1)
    google-apis-core (0.16.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-discovery_v1 (0.19.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-generator (0.16.0)
      activesupport (>= 5.0)
      gems (~> 1.2)
      google-apis-core (>= 0.15.0, < 2.a)
      google-apis-discovery_v1 (~> 0.18)
      thor (>= 0.20, < 2.a)
    google-apis-iamcredentials_v1 (0.22.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-storage_v1 (0.50.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.2.2)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-cloud-storage (1.55.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-core (~> 0.13)
      google-apis-iamcredentials_v1 (~> 0.18)
      google-apis-storage_v1 (>= 0.42)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-id-token (1.4.2)
      jwt (>= 1)
    google-logging-utils (0.1.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashie (5.0.0)
    htmlbeautifier (1.4.3)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (1.2.3)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.10.2)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (3.17.0.4)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    meta-tags (2.22.1)
      actionpack (>= 6.0.0, < 8.1)
    mime-types (3.6.2)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0325)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.6-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.6-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.6-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.6-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.6-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.6-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.6-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.6-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    parallel (1.26.3)
    parser (3.3.7.3)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettier_print (1.2.1)
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.1.12)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.13.0)
      psych (>= 4.0.0)
    redis (4.8.1)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.4.1)
    rubocop (1.75.1)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.43.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.43.0)
      parser (>= *******)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    rufo (0.18.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    signet (0.19.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    smart_properties (1.17.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.6)
    stripe (13.5.0)
    stripe_event (2.11.0)
      activesupport (>= 3.1)
      stripe (>= 2.8, < 14)
    swe4r (0.0.2)
    syntax_tree (6.2.0)
      prettier_print (>= 1.2.0)
    tailwindcss-rails (2.7.9-aarch64-linux)
      railties (>= 7.0.0)
    tailwindcss-rails (2.7.9-arm-linux)
      railties (>= 7.0.0)
    tailwindcss-rails (2.7.9-arm64-darwin)
      railties (>= 7.0.0)
    tailwindcss-rails (2.7.9-x86_64-darwin)
      railties (>= 7.0.0)
    tailwindcss-rails (2.7.9-x86_64-linux)
      railties (>= 7.0.0)
    thor (1.3.2)
    tilt (2.6.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    version_gem (1.1.6)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    websocket (1.2.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  activeadmin (~> 3.2)
  bootsnap (~> 1.18.4)
  capybara
  debug
  devise
  dotenv-rails
  erb_lint
  ffi (~> 1.17)
  geocoder
  google-api-client
  google-cloud-storage
  google-id-token
  htmlbeautifier
  image_processing (~> 1.2)
  importmap-rails (~> 1.1)
  jbuilder
  jsbundling-rails
  meta-tags
  mini_magick
  omniauth-google-oauth2
  pg (~> 1.1)
  puma (~> 6.0)
  rails (~> 7.1.3)
  ransack (~> 4.3)
  redis (~> 4.0)
  rest-client
  rufo
  sassc-rails (~> 2.1.2)
  selenium-webdriver
  sprockets-rails
  stimulus-rails
  stripe_event (~> 2.11)
  swe4r
  syntax_tree
  tailwindcss-rails (~> 2.0)
  turbo-rails
  tzinfo-data
  web-console
  webdrivers

RUBY VERSION
   ruby 3.1.7p261

BUNDLED WITH
   2.6.6
