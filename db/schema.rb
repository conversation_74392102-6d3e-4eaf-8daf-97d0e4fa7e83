# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_05_01_000001) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_admin_comments", force: :cascade do |t|
    t.string "namespace"
    t.text "body"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "author_type"
    t.bigint "author_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_type", "author_id"], name: "index_active_admin_comments_on_author"
    t.index ["namespace"], name: "index_active_admin_comments_on_namespace"
    t.index ["resource_type", "resource_id"], name: "index_active_admin_comments_on_resource"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admin_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admin_users_on_reset_password_token", unique: true
  end

  create_table "audios", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.float "audio_length"
    t.string "view_url"
    t.string "download_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "audios_authors", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "audio_id", null: false
    t.index ["audio_id", "author_id"], name: "index_audios_authors_on_audio_id_and_author_id"
    t.index ["author_id", "audio_id"], name: "index_audios_authors_on_author_id_and_audio_id"
  end

  create_table "authors", force: :cascade do |t|
    t.string "name", null: false
    t.string "email"
    t.text "biography"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "authors_books", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "book_id", null: false
    t.index ["author_id", "book_id"], name: "index_authors_books_on_author_id_and_book_id"
    t.index ["book_id", "author_id"], name: "index_authors_books_on_book_id_and_author_id"
  end

  create_table "authors_cds", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "cd_id", null: false
    t.index ["author_id", "cd_id"], name: "index_authors_cds_on_author_id_and_cd_id"
    t.index ["cd_id", "author_id"], name: "index_authors_cds_on_cd_id_and_author_id"
  end

  create_table "authors_ebooks", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "ebook_id", null: false
    t.index ["author_id", "ebook_id"], name: "index_authors_ebooks_on_author_id_and_ebook_id"
    t.index ["ebook_id", "author_id"], name: "index_authors_ebooks_on_ebook_id_and_author_id"
  end

  create_table "authors_tapes", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "tape_id", null: false
    t.index ["author_id", "tape_id"], name: "index_authors_tapes_on_author_id_and_tape_id"
    t.index ["tape_id", "author_id"], name: "index_authors_tapes_on_tape_id_and_author_id"
  end

  create_table "authors_videos", id: false, force: :cascade do |t|
    t.bigint "author_id", null: false
    t.bigint "video_id", null: false
    t.index ["author_id", "video_id"], name: "index_authors_videos_on_author_id_and_video_id"
    t.index ["video_id", "author_id"], name: "index_authors_videos_on_video_id_and_author_id"
  end

  create_table "bodygraphs", force: :cascade do |t|
    t.string "name"
    t.datetime "birth_date_utc"
    t.datetime "design_date_utc"
    t.string "birth_country"
    t.string "birth_city"
    t.string "type"
    t.string "inner_authority"
    t.string "definition"
    t.string "profile"
    t.string "incarnation_cross"
    t.string "determination"
    t.string "environment"
    t.string "view"
    t.string "motivation"
    t.string "cognition"
    t.string "sense"
    t.string "variable"
    t.string "personality_activations"
    t.string "design_activations"
    t.boolean "head_defined"
    t.boolean "ajna_defined"
    t.boolean "throat_defined"
    t.boolean "spleen_defined"
    t.boolean "solar_plexus_defined"
    t.boolean "g_center_defined"
    t.boolean "sacral_defined"
    t.boolean "root_defined"
    t.integer "personality_nodes_tone"
    t.integer "design_nodes_tone"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "timezone"
    t.string "aura_type"
    t.float "latitude"
    t.float "longitude"
    t.string "birth_date"
    t.string "birth_time"
    t.text "all_activated_gates"
    t.boolean "ego_defined"
    t.index ["user_id"], name: "index_bodygraphs_on_user_id"
  end

  create_table "books", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.integer "number_of_pages"
    t.bigint "donor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["donor_id"], name: "index_books_on_donor_id"
  end

  create_table "calendar_events", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.string "facilitator_name"
    t.string "link"
    t.datetime "event_datetime"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "cart_items", force: :cascade do |t|
    t.bigint "cart_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cart_id"], name: "index_cart_items_on_cart_id"
    t.index ["product_id"], name: "index_cart_items_on_product_id"
  end

  create_table "carts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_carts_on_user_id"
  end

  create_table "cds", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.float "audio_length"
    t.bigint "donor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["donor_id"], name: "index_cds_on_donor_id"
  end

  create_table "conference_tickets", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "conference_id", null: false
    t.string "stripe_session_id"
    t.string "stripe_payment_intent_id"
    t.decimal "price", precision: 8, scale: 2
    t.string "status", default: "purchased"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "referrer_speaker_id"
    t.string "discount_type"
    t.decimal "platform_fee_percentage", precision: 5, scale: 2
    t.index ["conference_id"], name: "index_conference_tickets_on_conference_id"
    t.index ["referrer_speaker_id"], name: "index_conference_tickets_on_referrer_speaker_id"
    t.index ["user_id", "conference_id"], name: "index_conference_tickets_on_user_id_and_conference_id", unique: true
    t.index ["user_id"], name: "index_conference_tickets_on_user_id"
  end

  create_table "donors", force: :cascade do |t|
    t.string "name", null: false
    t.string "email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "download_links", force: :cascade do |t|
    t.bigint "product_id", null: false
    t.string "title"
    t.string "url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_download_links_on_product_id"
  end

  create_table "ebooks", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.integer "number_of_pages"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ephemeris_entries", id: :serial, force: :cascade do |t|
    t.datetime "ts", precision: nil
    t.integer "sun_gate"
    t.float "sun_line"
    t.integer "north_node_gate"
    t.float "north_node_line"
    t.integer "moon_gate"
    t.integer "moon_line"
    t.integer "moon_color"
    t.integer "mercury_gate"
    t.integer "mercury_line"
    t.integer "mercury_color"
    t.integer "venus_gate"
    t.integer "venus_line"
    t.integer "venus_color"
    t.integer "mars_gate"
    t.integer "mars_line"
    t.integer "mars_color"
    t.integer "jupiter_gate"
    t.integer "jupiter_line"
    t.integer "jupiter_color"
    t.integer "saturn_gate"
    t.integer "saturn_line"
    t.integer "saturn_color"
    t.integer "uranus_gate"
    t.integer "uranus_line"
    t.integer "uranus_color"
    t.integer "neptune_gate"
    t.integer "neptune_line"
    t.integer "neptune_color"
    t.integer "pluto_gate"
    t.integer "pluto_line"
    t.integer "pluto_color"
    t.index ["ts"], name: "index_ephemeris_entries_on_ts"
  end

  create_table "hdhd_conference_photos", force: :cascade do |t|
    t.bigint "conference_id", null: false
    t.string "title", null: false
    t.string "photographer", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conference_id"], name: "index_hdhd_conference_photos_on_conference_id"
  end

  create_table "hdhd_conferences", force: :cascade do |t|
    t.datetime "start_date", null: false
    t.string "description", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "product_id"
    t.index ["product_id"], name: "index_hdhd_conferences_on_product_id"
  end

  create_table "hdhd_conferences_speakers", id: false, force: :cascade do |t|
    t.bigint "speaker_id", null: false
    t.bigint "conference_id", null: false
  end

  create_table "hdhd_scheduled_events", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.datetime "start_date", null: false
    t.integer "duration_minutes", null: false
    t.string "event_type", null: false
    t.bigint "conference_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conference_id"], name: "index_hdhd_scheduled_events_on_conference_id"
    t.index ["event_type"], name: "index_hdhd_scheduled_events_on_event_type"
    t.index ["start_date"], name: "index_hdhd_scheduled_events_on_start_date"
  end

  create_table "hdhd_scheduled_events_speakers", id: false, force: :cascade do |t|
    t.bigint "scheduled_event_id", null: false
    t.bigint "speaker_id", null: false
    t.index ["scheduled_event_id"], name: "index_hdhd_scheduled_events_speakers_on_scheduled_event_id"
    t.index ["speaker_id"], name: "index_hdhd_scheduled_events_speakers_on_speaker_id"
  end

  create_table "hdhd_speakers", force: :cascade do |t|
    t.string "full_name", null: false
    t.string "email", null: false
    t.datetime "birth_date_utc"
    t.datetime "design_date_utc"
    t.datetime "birth_date_local"
    t.string "birth_location"
    t.string "websites"
    t.string "website_titles"
    t.string "bio"
    t.string "profile"
    t.string "aura_type"
    t.string "inner_authority"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.string "role", default: "speaker"
    t.string "stripe_user_id"
    t.string "friends_and_family_password"
    t.string "stripe_publishable_key"
    t.index ["role"], name: "index_hdhd_speakers_on_role"
    t.index ["stripe_user_id"], name: "index_hdhd_speakers_on_stripe_user_id"
    t.index ["user_id"], name: "index_hdhd_speakers_on_user_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "title"
    t.text "message"
    t.string "action_url"
    t.boolean "read", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "order_items", force: :cascade do |t|
    t.bigint "order_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_order_items_on_order_id"
    t.index ["product_id"], name: "index_order_items_on_product_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "products", force: :cascade do |t|
    t.string "title"
    t.text "short_description"
    t.text "long_description"
    t.string "image"
    t.decimal "price"
    t.decimal "min_price"
    t.decimal "recommended_price"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "media_type"
    t.string "media_description"
    t.string "highlights"
    t.string "stripe_price_id"
    t.string "stripe_product_id"
    t.index ["stripe_price_id"], name: "index_products_on_stripe_price_id"
    t.index ["stripe_product_id"], name: "index_products_on_stripe_product_id"
  end

  create_table "public_bodygraph_comments", force: :cascade do |t|
    t.string "title"
    t.string "href"
    t.string "file_attachment"
    t.string "comment_type"
    t.bigint "public_bodygraph_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["public_bodygraph_id"], name: "index_public_bodygraph_comments_on_public_bodygraph_id"
  end

  create_table "public_bodygraphs", force: :cascade do |t|
    t.string "name"
    t.string "birth_name"
    t.datetime "birth_date_local"
    t.datetime "birth_date_utc"
    t.datetime "design_date_utc"
    t.string "birth_country"
    t.string "birth_city"
    t.string "birth_data_source"
    t.string "birth_data_source_notes"
    t.string "birth_data_collector"
    t.string "rodden_rating"
    t.string "gender"
    t.string "aura_type"
    t.string "inner_authority"
    t.string "definition"
    t.string "profile"
    t.string "incarnation_cross"
    t.string "determination"
    t.string "environment"
    t.string "view"
    t.string "motivation"
    t.string "cognition"
    t.string "sense"
    t.string "variable"
    t.string "personality_activations"
    t.string "design_activations"
    t.boolean "head_defined"
    t.boolean "ajna_defined"
    t.boolean "throat_defined"
    t.boolean "spleen_defined"
    t.boolean "solar_plexus_defined"
    t.boolean "g_center_defined"
    t.boolean "sacral_defined"
    t.boolean "root_defined"
    t.integer "personality_nodes_tone"
    t.integer "design_nodes_tone"
    t.string "timezone"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "latitude"
    t.float "longitude"
    t.string "notable_for"
    t.boolean "famous"
    t.boolean "historical_event"
    t.string "profession"
    t.string "portrait"
    t.string "description"
    t.string "birth_date"
    t.string "birth_time"
  end

  create_table "purchased_products", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_purchased_products_on_product_id"
    t.index ["user_id"], name: "index_purchased_products_on_user_id"
  end

  create_table "reminders", force: :cascade do |t|
    t.datetime "utc_time"
    t.string "timezone"
    t.string "title"
    t.string "description"
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_reminders_on_user_id"
  end

  create_table "speaker_year_roles", force: :cascade do |t|
    t.bigint "speaker_id", null: false
    t.bigint "conference_id", null: false
    t.string "role", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conference_id"], name: "index_speaker_year_roles_on_conference_id"
    t.index ["speaker_id", "conference_id"], name: "index_speaker_year_roles_on_speaker_id_and_conference_id", unique: true
    t.index ["speaker_id"], name: "index_speaker_year_roles_on_speaker_id"
  end

  create_table "streaming_videos", force: :cascade do |t|
    t.string "title"
    t.boolean "is_4k"
    t.integer "runtime_minutes"
    t.string "url"
    t.string "short_description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "year"
  end

  create_table "tapes", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.float "audio_length"
    t.bigint "donor_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["donor_id"], name: "index_tapes_on_donor_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false
    t.string "profile_picture"
    t.string "google_uid"
    t.string "image"
    t.string "google_token"
    t.string "google_refresh_token"
    t.string "name"
    t.string "image_url"
    t.string "provider"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "videos", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.date "publication_date"
    t.date "acquisition_date"
    t.date "disposal_date"
    t.string "disposal_reason"
    t.string "physical_location"
    t.float "audio_length"
    t.string "view_url"
    t.string "download_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "bodygraphs", "users"
  add_foreign_key "books", "donors"
  add_foreign_key "cart_items", "carts"
  add_foreign_key "cart_items", "products"
  add_foreign_key "carts", "users"
  add_foreign_key "cds", "donors"
  add_foreign_key "conference_tickets", "hdhd_conferences", column: "conference_id"
  add_foreign_key "conference_tickets", "hdhd_speakers", column: "referrer_speaker_id"
  add_foreign_key "conference_tickets", "users"
  add_foreign_key "download_links", "products"
  add_foreign_key "hdhd_conference_photos", "hdhd_conferences", column: "conference_id"
  add_foreign_key "hdhd_conferences", "products"
  add_foreign_key "hdhd_scheduled_events", "hdhd_conferences", column: "conference_id"
  add_foreign_key "hdhd_scheduled_events_speakers", "hdhd_scheduled_events", column: "scheduled_event_id"
  add_foreign_key "hdhd_scheduled_events_speakers", "hdhd_speakers", column: "speaker_id"
  add_foreign_key "hdhd_speakers", "users"
  add_foreign_key "notifications", "users"
  add_foreign_key "order_items", "orders"
  add_foreign_key "order_items", "products"
  add_foreign_key "orders", "users"
  add_foreign_key "public_bodygraph_comments", "public_bodygraphs"
  add_foreign_key "purchased_products", "products"
  add_foreign_key "purchased_products", "users"
  add_foreign_key "reminders", "users"
  add_foreign_key "speaker_year_roles", "hdhd_conferences", column: "conference_id"
  add_foreign_key "speaker_year_roles", "hdhd_speakers", column: "speaker_id"
  add_foreign_key "tapes", "donors"
end
