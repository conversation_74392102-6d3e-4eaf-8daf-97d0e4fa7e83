class CreateHdhdConferencesSpeakersPhotos < ActiveRecord::Migration[7.0]
  def change
    create_table :hdhd_conferences do |t|
      t.datetime :start_date, null: false
      t.string :description, null: false
      t.timestamps
    end

    create_table :hdhd_speakers do |t|
      t.references :conference, null: false, foreign_key: { to_table: :hdhd_conferences }
      t.string :full_name, null: false
      t.string :email, null: false, unique: true
      t.datetime :birth_date_utc
      t.datetime :design_date_utc
      t.datetime :birth_date_local
      t.string :birth_location
      t.string :websites
      t.string :website_titles
      t.string :bio
      t.string :profile
      t.string :aura_type
      t.string :inner_authority
      t.timestamps
    end

    create_table :hdhd_conference_photos do |t|
      t.references :conference, null: false, foreign_key: { to_table: :hdhd_conferences }
      t.string :title, null: false
      t.string :photographer, null: false
      t.timestamps
    end
  end
end
