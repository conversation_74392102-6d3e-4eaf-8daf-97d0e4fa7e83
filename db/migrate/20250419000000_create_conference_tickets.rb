class CreateConferenceTickets < ActiveRecord::Migration[7.1]
  def change
    create_table :conference_tickets do |t|
      t.references :user, null: false, foreign_key: true
      t.references :conference, null: false, foreign_key: { to_table: :hdhd_conferences }
      t.string :stripe_session_id
      t.string :stripe_payment_intent_id
      t.decimal :price, precision: 8, scale: 2
      t.string :status, default: 'purchased'
      t.timestamps
    end
    
    add_index :conference_tickets, [:user_id, :conference_id], unique: true
  end
end
