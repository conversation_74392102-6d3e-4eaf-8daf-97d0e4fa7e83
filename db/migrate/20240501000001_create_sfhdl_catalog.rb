class CreateSfhdlCatalog < ActiveRecord::Migration[7.1]
  def change
    # Create authors table
    create_table :authors do |t|
      t.string :name, null: false
      t.string :email
      t.text :biography
      t.timestamps
    end
    
    # Create donors table
    create_table :donors do |t|
      t.string :name, null: false
      t.string :email
      t.timestamps
    end
    
    # Create books table
    create_table :books do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.integer :number_of_pages
      t.references :donor, null: true, foreign_key: true
      t.timestamps
    end
    
    # Create ebooks table
    create_table :ebooks do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.integer :number_of_pages
      t.timestamps
    end
    
    # Create CDs table
    create_table :cds do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.float :audio_length
      t.references :donor, null: true, foreign_key: true
      t.timestamps
    end
    
    # Create tapes table
    create_table :tapes do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.float :audio_length
      t.references :donor, null: true, foreign_key: true
      t.timestamps
    end
    
    # Create audios table
    create_table :audios do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.float :audio_length
      t.string :view_url
      t.string :download_url
      t.timestamps
    end
    
    # Create videos table
    create_table :videos do |t|
      t.string :title, null: false
      t.text :description
      t.date :publication_date
      t.date :acquisition_date
      t.date :disposal_date
      t.string :disposal_reason
      t.string :physical_location
      t.float :audio_length
      t.string :view_url
      t.string :download_url
      t.timestamps
    end
    
    # Create join tables for many-to-many relationships
    create_join_table :authors, :books do |t|
      t.index [:author_id, :book_id]
      t.index [:book_id, :author_id]
    end
    
    create_join_table :authors, :ebooks do |t|
      t.index [:author_id, :ebook_id]
      t.index [:ebook_id, :author_id]
    end
    
    create_join_table :authors, :cds do |t|
      t.index [:author_id, :cd_id]
      t.index [:cd_id, :author_id]
    end
    
    create_join_table :authors, :tapes do |t|
      t.index [:author_id, :tape_id]
      t.index [:tape_id, :author_id]
    end
    
    create_join_table :authors, :audios do |t|
      t.index [:author_id, :audio_id]
      t.index [:audio_id, :author_id]
    end
    
    create_join_table :authors, :videos do |t|
      t.index [:author_id, :video_id]
      t.index [:video_id, :author_id]
    end
  end
end
