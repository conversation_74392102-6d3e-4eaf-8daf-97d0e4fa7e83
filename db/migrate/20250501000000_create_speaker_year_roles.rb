class CreateSpeakerYearRoles < ActiveRecord::Migration[7.1]
  def change
    create_table :speaker_year_roles do |t|
      t.references :speaker, null: false, foreign_key: { to_table: :hdhd_speakers }
      t.references :conference, null: false, foreign_key: { to_table: :hdhd_conferences }
      t.string :role, null: false
      t.timestamps
    end
    
    add_index :speaker_year_roles, [:speaker_id, :conference_id], unique: true
  end
end
