class CreateHdhdScheduledEvents < ActiveRecord::Migration[7.1]
  def change
    create_table :hdhd_scheduled_events do |t|
      t.string :title, null: false
      t.text :description
      t.datetime :start_date, null: false
      t.integer :duration_minutes, null: false
      t.string :event_type, null: false
      t.references :conference, null: false, foreign_key: { to_table: :hdhd_conferences }
      
      t.timestamps
    end
    
    create_table :hdhd_scheduled_events_speakers, id: false do |t|
      t.references :scheduled_event, null: false, foreign_key: { to_table: :hdhd_scheduled_events }
      t.references :speaker, null: false, foreign_key: { to_table: :hdhd_speakers }
    end
    
    add_index :hdhd_scheduled_events, :event_type
    add_index :hdhd_scheduled_events, :start_date
  end
end
