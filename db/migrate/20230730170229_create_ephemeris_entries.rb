class CreateEphemerisEntries < ActiveRecord::Migration[7.0]
  def change
    create_table :ephemeris_entries do |t|
      t.datetime :ts
      t.integer :sun_gate
      t.float :sun_line
      t.integer :north_node_gate
      t.float :north_node_line
      t.integer :moon_gate
      t.integer :moon_line
      t.integer :moon_color
      t.integer :mercury_gate
      t.integer :mercury_line
      t.integer :mercury_color
      t.integer :venus_gate
      t.integer :venus_line
      t.integer :venus_color
      t.integer :mars_gate
      t.integer :mars_line
      t.integer :mars_color
      t.integer :jupiter_gate
      t.integer :jupiter_line
      t.integer :jupiter_color
      t.integer :saturn_gate
      t.integer :saturn_line
      t.integer :saturn_color
      t.integer :uranus_gate
      t.integer :uranus_line
      t.integer :uranus_color
      t.integer :neptune_gate
      t.integer :neptune_line
      t.integer :neptune_color
      t.integer :pluto_gate
      t.integer :pluto_line
      t.integer :pluto_color
    end
  end
end
