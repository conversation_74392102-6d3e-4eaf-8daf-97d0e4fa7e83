#!/usr/bin/env sh

# Check if overmind is installed
if ! command -v overmind &> /dev/null; then
  echo "Installing overmind..."
  brew install overmind
fi

# echo "Updating browsers..."
# npx update-browserslist-db@latest

echo "Precompiling assets..."
yarn precompile

echo "Building Tailwind CSS..."
yarn build:css &

# Open the dashboard in the background
(sleep 2 && open http://signposthd.localhost:3000/dashboard) &

# Run overmind with the Procfile
exec overmind start -f Procfile.dev "$@"
