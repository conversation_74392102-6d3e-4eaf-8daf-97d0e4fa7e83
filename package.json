{"name": "signposthd", "dependencies": {"@hotwired/turbo-rails": "^7.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "caniuse-lite": "^1.0.30001706", "esbuild": "^0.17.13"}, "scripts": {"rebuild": "rm -rf node_modules yarn.lock && yarn install && rails assets:clobber && rails assets:precompile", "psql": "psql -U jdempcy -d signposthd_development", "update": "npx update-browserslist-db@latest", "precompile": "rails assets:precompile", "package": "bundle lock --add-platform x86_64-linux && bundle package --all-platforms", "build:css": "tailwindcss -o app/assets/builds/tailwind.css --watch", "build": "esbuild app/javascript/*.* --bundle --sourcemap --outdir=app/assets/builds --public-path=assets --external:@hotwired/stimulus --external:@hotwired/stimulus-loading --external:controllers/*", "restart": "rails tmp:cache:clear && rails restart"}, "devDependencies": {"@prettier/plugin-ruby": "^4.0.4", "autoprefixer": "^10.0.2", "postcss": "^8.0.9", "prettier": "^3.5.3", "tailwindcss": "^4.0.17"}, "license": "UNLICENSED"}