# Set domain-specific URL options for route helpers
# This ensures that URLs are generated with the correct host based on the domain

Rails.application.config.after_initialize do
  # For High Desert Human Design
  Rails.application.routes.url_helpers.class.module_eval do
    def hdhd_stripe_callback_url(options = {})
      options = options.merge(host: 'www.highdeserthumandesign.com', protocol: 'https')
      url_for(options.merge(only_path: false, controller: 'hdhd/stripe', action: 'callback'))
    end
  end
end
