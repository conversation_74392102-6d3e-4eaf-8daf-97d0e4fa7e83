# Set Stripe API key
Stripe.api_key = ENV.fetch('STRIPE_SECRET_KEY', nil)

# NOTE: We're handling webhooks directly in WebhooksController#stripe
# This StripeEvent configuration is kept for reference but is not active
# since we're not mounting the StripeEvent engine in routes.rb
# StripeEvent.configure do |events|
#   events.subscribe 'checkout.session.completed' do |event|
#     session = event.data.object
#     user = User.find_by(email: session.customer_email)
#     product = Product.find_by(id: session.metadata['product_id'])
#
#     user.purchased_products.create(product: product) if user && product
#   end
# end
