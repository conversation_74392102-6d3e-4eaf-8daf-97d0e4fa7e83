require 'domain_constraints'

Rails.application.routes.draw do # rubocop:disable Metrics/BlockLength
  # Routes for localhost:3000 only.
  constraints DomainConstraints.new('localhost', 'localhost') do
    root 'development#index'
  end

  # Shared URLs on all domains.
  devise_for :users, controllers: {
    registrations: 'users/registrations',
    sessions: 'users/sessions',
    omniauth_callbacks: 'users/omniauth_callbacks'
  }
  devise_scope :user do
    delete '/users/sign_out', to: 'users/sessions#destroy'
    post "/auth/google_login", to: "users/sessions#google_login"
    # Add a direct route for Google OAuth2 callback
    get '/auth/google_oauth2/callback', to: 'users/omniauth_callbacks#google_oauth2'
  end

  # User Library
  namespace :users do
    resource :settings, only: [:edit, :update]
    resource :speaker_connection, only: [:new, :create]
  end
  get '/library', to: 'users#library'

  # Use a namespace for admin routes
  devise_for :admin_users, ActiveAdmin::Devise.config
  ActiveAdmin.routes(self)

  # HDHD Products, Carts, and Orders
  namespace :hdhd do
    resources :products, only: [:show, :index]
    get '/shop', to: 'products#index', as: 'shop'

    # Cart Routes
    resource :cart, only: [:show] do
      post 'add_item/:product_id', to: 'carts#add_item', as: 'add_item'
      delete 'remove_item/:product_id', to: 'carts#remove_item', as: 'remove_item'
    end

    # Orders & Checkout
    resources :orders, only: [:create] do
      post 'checkout', on: :member, to: 'orders#create_checkout_session'
      get 'success', on: :collection
      get 'cancel', on: :collection
    end
  end

  # Stripe Webhooks
  post 'webhooks/stripe', to: 'webhooks#stripe'
  get 'webhooks/test', to: 'webhooks#test'

  # Notifications
  resources :notifications, only: [] do
    member do
      post :mark_as_read
    end
    collection do
      post :mark_all_as_read
      get :unread_count
    end
  end

  constraints DomainConstraints.new('signposthd.com', 'signposthd.localhost') do
    # Routes for signposthd.com

    # Signed out Homepage
    get '/', to: 'signposthd/static_pages#home'
    get '/features/', to: 'signposthd/static_pages#features', as: :features
    get '/company/', to: 'signposthd/static_pages#company', as: :company
    get '/privacy-policy/', to: 'signposthd/static_pages#privacy_policy', as: :privacy_policy
    get '/terms-and-conditions/', to: 'signposthd/static_pages#terms_and_conditions', as: :terms_and_conditions
    get '/cookie-policy/', to: 'signposthd/static_pages#cookie_policy', as: :cookie_policy
    get '/success/', to: 'signposthd/static_pages#success', as: :success

    # Dashboard
    get '/dashboard/', to: 'signposthd/static_pages#dashboard', as: :dashboard

    # Calendar
    get '/oauth2callback', to: 'signposthd/calendar_events#oauth2callback'
    resources :calendar_events, only: [:index], module: 'signposthd'

    # Day Planner
    get '/day-planner/', to: 'signposthd/static_pages#day_planner', as: :day_planner
    get '/day-planner/line-calendar', to: 'signposthd/static_pages#line_calendar', as: :line_calendar
    scope '/day-planner', module: 'signposthd' do
      resources :reminders
    end

    # Bodygraphs
    resources :bodygraphs, module: 'signposthd'

    # Reference
    get '/reference/', to: 'signposthd/static_pages#reference', as: :reference
    get '/rave-mandala-explorer/', to: 'signposthd/static_pages#rave_mandala_explorer', as: :rave_mandala_explorer

    # Quarters and Godheads
    get '/quarters-and-godheads/', to: 'signposthd/quarters_and_godheads#index', as: :quarters_and_godheads
    get '/quarters-and-godheads/:godhead', to: 'signposthd/quarters_and_godheads#show', as: :quarters_and_godhead

    # Centers, Channels, Gates, Lines, Color, Tone, Base
    get '/centers', to: 'signposthd/centers#index', as: :centers
    get '/centers/:center', to: 'signposthd/centers#show', as: :center
    get '/circuits', to: 'signposthd/circuits#index', as: :circuits
    get '/circuits/:circuit', to: 'signposthd/circuits#show', as: :circuit
    get '/channels', to: 'signposthd/channels#index', as: :channels
    get '/channels/:channel', to: 'signposthd/channels#show', as: :channel
    get '/gates', to: 'signposthd/gates#index', as: :gates
    get '/gates/:gate', to: 'signposthd/gates#show', as: :gate
    get '/lines', to: 'signposthd/lines#index', as: :lines
    get '/lines/:line', to: 'signposthd/lines#show', as: :line
    get '/color', to: 'signposthd/color#index', as: :colors
    get '/color/:color', to: 'signposthd/color#show', as: :color
    get '/tone', to: 'signposthd/tone#index', as: :tones
    get '/tone/:tone', to: 'signposthd/tone#show', as: :tone
    get '/base', to: 'signposthd/base#index', as: :bases
    get '/base/:base', to: 'signposthd/base#show', as: :base

    # The World
    get '/the-world/', to: 'signposthd/static_pages#the_world', as: :the_world
    get '/the-world/2027/', to: 'signposthd/static_pages#2027', as: :_2027
    get '/the-world/transit-forecast/', to: 'signposthd/static_pages#transit_forecast', as: :transit_forecast
    get '/the-world/transit-theme-analysis/', to: 'signposthd/static_pages#transit_theme_analysis', as: :transit_theme_analysis

    # Research Center
    get '/research-center/', to: 'signposthd/static_pages#research_center', as: :research_center
    get '/research-center/famous-people/', to: 'signposthd/static_pages#famous_people', as: :famous_people
    get '/research-center/historical-figures/', to: 'signposthd/static_pages#historical_figures', as: :historical_figures
    get '/research-center/historical-events/', to: 'signposthd/static_pages#historical_events', as: :historical_events
    get '/research-center/global-cycles/', to: 'signposthd/static_pages#global_cycles', as: :global_cycles
    get '/research-center/amino-acid-explorer/', to: 'signposthd/amino_acid_explorer#index', as: 'amino_acid_explorer'
    get '/research-center/amino-acid-explorer/:acid', to: 'signposthd/amino_acid_explorer#show', as: 'amino_acid'
    resources :public_bodygraph_comments, module: 'signposthd'
    resources :public_bodygraphs, module: 'signposthd'

    # Streaming Videos
    get '/streaming-videos/', to: 'signposthd/streaming_videos#index', as: :streaming_videos_index
    resources :streaming_videos, module: 'signposthd' do
      member do
        get 'watch'
      end
    end
    get '/streaming-videos/admin', to: 'signposthd/streaming_videos#admin_index', as: :streaming_videos_admin

    # Audio Player
    get '/audio-player', to: 'signposthd/audio_player#index'
    get '/audio-player/:id', to: 'signposthd/audio_player#index'

    # Learning Center
    get '/learning-center/', to: 'signposthd/static_pages#learning_center', as: :learning_center
    get '/learning-center/audio-library/', to: 'signposthd/static_pages#audio_library', as: :audio_library
    get '/learning-center/video-library/', to: 'signposthd/static_pages#video_library', as: :video_library
    get '/learning-center/books/', to: 'signposthd/static_pages#books', as: :books

    # Mystic Corner
    get '/mystic-corner/', to: 'signposthd/static_pages#mystic_corner', as: :mystic_corner
    get '/mystic-corner/mystic-way-analyzer/', to: 'signposthd/static_pages#mystic_way_analyzer', as: :mystic_way_analyzer

    # Special Interest
    get '/special-interest/', to: 'signposthd/static_pages#special_interest', as: :special_interest

    # Settings
    get '/dashboard-settings/', to: 'signposthd/static_pages#dashboard_settings', as: :dashboard_settings

    # Tools
    get '/bodygraph-image-creator/', to: 'signposthd/static_pages#bodygraph_image_creator', as: :bodygraph_image_creator
    get '/sequence-analyzer/', to: 'signposthd/sequence_analyzer#index', as: :sequence_analyzer

    # Enneagram Browser
    get '/enneagram-browser/', to: 'signposthd/enneagram_browser#index', as: :enneagram_browser
    get '/enneatypes', to: 'signposthd/enneatypes#index', as: :enneatypes
    get '/enneatypes/:id', to: 'signposthd/enneatypes#show', as: :enneatype
    get '/enneagram-triads', to: 'signposthd/enneagram_triads#index', as: :enneagram_triads
    get '/enneagram-triads/:id', to: 'signposthd/enneagram_triads#show', as: :enneagram_triad
    get '/enneagram-trigroups', to: 'signposthd/enneagram_trigroups#index', as: :enneagram_trigroups
    get '/enneagram-trigroups/:id', to: 'signposthd/enneagram_trigroups#show', as: :enneagram_trigroup
    get '/enneagram-stems', to: 'signposthd/enneagram_stems#index', as: :enneagram_stems
    get '/enneagram-stems/:id', to: 'signposthd/enneagram_stems#show', as: :enneagram_stem
  end

  constraints DomainConstraints.new('centerforhumandesign.org', 'centerforhumandesign.localhost') do
    # Routes for centerforhumandesign.org
    get '/', to: 'center_for_human_design/static_pages#home', as: :center_for_human_design_home
    get '/about/', to: 'center_for_human_design/static_pages#about', as: :center_for_human_design_about
    get '/about/history/', to: 'center_for_human_design/static_pages#history', as: :center_for_human_design_history
    get '/initiatives/', to: 'center_for_human_design/static_pages#initiatives', as: :center_for_human_design_initiatives
    get '/terms-and-conditions/', to: 'center_for_human_design/static_pages#terms_and_conditions', as: :center_for_human_design_terms_and_conditions
    get '/privacy-policy/', to: 'center_for_human_design/static_pages#privacy_policy', as: :center_for_human_design_privacy_policy
    get '/cookie-policy/', to: 'center_for_human_design/static_pages#cookie_policy', as: :center_for_human_design_cookie_policy
  end

  constraints DomainConstraints.new('highdeserthumandesign.com', 'highdeserthumandesign.localhost') do
    # Routes for highdeserthumandesign.com
    get '/', to: 'hdhd/static_pages#home', as: :hdhd_home
    get '/about', to: 'hdhd/static_pages#about', as: :hdhd_about
    get '/terms-and-conditions', to: 'hdhd/static_pages#terms_and_conditions', as: :hdhd_terms_and_conditions
    get '/cookie-policy', to: 'hdhd/static_pages#cookie_policy', as: :hdhd_cookie_policy
    get '/privacy-policy', to: 'hdhd/static_pages#privacy_policy', as: :hdhd_privacy_policy
    get '/principles', to: 'hdhd/static_pages#principles', as: :hdhd_principles
    get '/hdhd-conference-attendee-agreement', to: 'hdhd/static_pages#attendee_agreement', as: :hdhd_attendee_agreement
    get '/previous-years', to: 'hdhd/conferences#index'
    get '/dashboard', to: 'hdhd/static_pages#dashboard'
    resources :speakers, controller: 'hdhd/speakers', only: [:index, :show, :edit, :update]
    get '/profile', to: 'users#show', as: :user_profile
    resources :conferences, param: :year, only: [:show, :index], controller: 'hdhd/conferences'
    resources :conference_photos, only: [:index, :show], controller: 'hdhd/conference_photos'
    get '/conference-dashboard', to: 'hdhd/conferences#conference_dashboard', as: :conference_dashboard
    get '/printouts', to: 'hdhd/static_pages#printouts'
    get '/statistics', to: 'hdhd/static_pages#statistics'
    get '/extras', to: 'hdhd/static_pages#extras'
    get '/schedule', to: 'hdhd/scheduled_events#index'
    get '/map', to: 'hdhd/static_pages#map'
    get '/shop', to: 'hdhd/products#index'

    namespace :hdhd do
      resources :speakers do
        get 'stripe/oauth', to: 'stripe#oauth', as: :stripe_oauth
        get 'stripe/unlink/confirmation', to: 'stripe#unlink_confirmation', as: :stripe_unlink_confirmation
        delete 'stripe/unlink', to: 'stripe#unlink', as: :stripe_unlink
        get 'payment_link', to: 'speakers#payment_link', as: :payment_link
        post 'payment_link', to: 'speakers#create_payment_link', as: :create_payment_link
        get 'friends-and-family', to: 'speakers#friends_and_family', as: :friends_and_family
      end

      resources :scheduled_events, only: [:index, :show]

      resources :conference_tickets, only: [:new, :create] do
        collection do
          get 'success'
          get 'cancel'
        end
      end

      get 'stripe/callback', to: 'stripe#callback', as: :stripe_callback
      get 'stripe/payment/success', to: 'stripe#payment_success', as: :stripe_payment_success
      get 'stripe/payment/cancel', to: 'stripe#payment_cancel', as: :stripe_payment_cancel
      post 'stripe/payment/create-checkout-session', to: 'stripe#create_checkout_session', as: :stripe_create_checkout_session
    end
  end

  constraints DomainConstraints.new('sfhdl.org', 'sfhdl.localhost') do
    # Routes for sfhdl.org
    get '/', to: 'sfhdl#home', as: :sfhdl_home
    get '/about/', to: 'sfhdl#about', as: :sfhdl_about
    get '/browse/', to: 'sfhdl#browse', as: :sfhdl_browse
    get '/authors/', to: 'sfhdl#authors', as: :sfhdl_authors
    get '/authors/:id', to: 'sfhdl#author', as: :sfhdl_author
    get '/terms-and-conditions/', to: 'sfhdl#terms_and_conditions', as: :sfhdl_terms_and_conditions
    get '/privacy-policy/', to: 'sfhdl#privacy_policy', as: :sfhdl_privacy_policy
    get '/cookie-policy/', to: 'sfhdl#cookie_policy', as: :sfhdl_cookie_policy

    # Catalog resources
    resources :books, only: [:index, :show], controller: 'sfhdl/books'
    resources :ebooks, only: [:index, :show], controller: 'sfhdl/ebooks'
    resources :cds, only: [:index, :show], controller: 'sfhdl/cds'
    resources :tapes, only: [:index, :show], controller: 'sfhdl/tapes'
    resources :audios, only: [:index, :show], controller: 'sfhdl/audios'
    resources :videos, only: [:index, :show], controller: 'sfhdl/videos'
  end
end
