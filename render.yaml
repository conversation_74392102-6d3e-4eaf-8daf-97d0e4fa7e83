databases:
  - name: postgres
    ipAllowList: []
    plan: free

services:
  - type: web
    name: signposthd
    plan: free
    env: ruby
    buildCommand: "./bin/render-build.sh"
    startCommand: bundle exec rails s
    envVars:
      - key: RAILS_MASTER_KEY
        sync: false
      - key: DATABASE_URL
        fromDatabase:
          name: postgres
          property: connectionString
  - type: redis
    name: redis
    ipAllowList: []
    plan: free
    maxmemoryPolicy: noeviction
